/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-domain
 * 文件名称:FollowUpBirthDetail.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.domain.followup;

import com.yuxin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <p>
 * 出身随访新生儿明细
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "FollowUpBirthDetail", description = "出身随访新生儿明细")
public class FollowUpBirthDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 出生随访详情主键ID
     */
    @ApiModelProperty(value = "出生随访详情主键ID")
    private Long birthId;
    /**
     * 性别  0：女 1：男
     */
    @ApiModelProperty(value = "性别  0：女 1：男")
    private Integer gender;
    /**
     * 死胎 0：是 1：否
     */
    @ApiModelProperty(value = "死胎 0：是 1：否")
    private Integer isStillbirth;
    /**
     * 出生体重(g)
     */
    @ApiModelProperty(value = "出生体重(g)")
    private BigDecimal weight;
    /**
     * Apgar评分
     */
    @ApiModelProperty(value = "Apgar评分")
    private String apgarScore;
    /**
     * 新生儿死亡 0:是 1:否
     */
    @ApiModelProperty(value = "新生儿死亡 0:是 1:否")
    private Integer isNeonatalDeath;
    /**
     * 畸形情况
     */
    @ApiModelProperty(value = "畸形情况")
    private String deformitySituation;

}
