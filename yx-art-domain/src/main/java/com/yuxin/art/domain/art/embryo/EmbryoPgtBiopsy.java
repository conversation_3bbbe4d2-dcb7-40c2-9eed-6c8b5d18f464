/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-domain
 * 文件名称:EmbryoPgtBiopsy.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.domain.art.embryo;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.yuxin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 胚胎gpt
 *
 * <AUTHOR>
 * @date 2023-12-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "ArtEmbryoPgt", description = "胚胎gpt")
public class EmbryoPgtBiopsy extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 治疗方案 0:AIH 1:AID 2:IVF 3:SIVF 4:ICSI 5:HICSI 6:FET 7:PESA_TESA 8:IVM 9:PGD
     */
    @ApiModelProperty(value = "治疗方案 0:AIH 1:AID 2:IVF 3:SIVF 4:ICSI 5:HICSI 6:FET 7:PESA_TESA 8:IVM 9:PGD")
    private Integer treatmentPlan;
    /**
     * 周期主键ID
     */
    @ApiModelProperty(value = "周期主键ID")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long cycleId;

    /**
     * 周期序号 每个病案从1自动增值
     */
    @ApiModelProperty(value = "周期序号 每个病案从1自动增值")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String cycleSort;
    /**
     * GPT时间
     */
    @ApiModelProperty(value = "GPT时间")
    private Date pgtTime;
    /**
     * 配偶关系(病案)主键ID
     */
    @ApiModelProperty(value = "配偶关系(病案)主键ID")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long pairBondId;
    /**
     * 周期号
     */
    @ApiModelProperty(value = "周期号")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String caseNo;
    /**
     * 女性患者主键 ID
     */
    @ApiModelProperty(value = "女性患者主键 ID")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long patientFemaleId;
    /**
     * 女性患者ID号
     */
    @ApiModelProperty(value = "女性患者ID号")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String patientFemaleIdentifyId;
    /**
     * 女性患者卡号
     */
    @ApiModelProperty(value = "女性患者卡号")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String patientFemaleCardNo;
    /**
     * 女患者姓名
     */
    @ApiModelProperty(value = "女患者姓名")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String patientFemaleName;
    /**
     * 女性患者年龄
     */
    @ApiModelProperty(value = "女性患者年龄")
    private Integer patientFemaleAge;
    /**
     * 男性患者主键 ID
     */
    @ApiModelProperty(value = "男性患者主键 ID")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long patientMaleId;
    /**
     * 男性患者ID号
     */
    @ApiModelProperty(value = "男性患者ID号")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String patientMaleIdentifyId;
    /**
     * 男性患者卡号
     */
    @ApiModelProperty(value = "男性患者卡号")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String patientMaleCardNo;
    /**
     * 男性患者姓名
     */
    @ApiModelProperty(value = "男性患者姓名")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String patientMaleName;
    /**
     * 男性患者年龄
     */
    @ApiModelProperty(value = "男性患者年龄")
    private Integer patientMaleAge;
    /**
     * pgt类型
     */
    @ApiModelProperty(value = "pgt类型")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String pgtType;
    /**
     * 活检方法
     */
    @ApiModelProperty(value = "活检方法")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String biopsyMethod;
    /**
     * 样品类型
     */
    @ApiModelProperty(value = "样品类型")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String sampleType;
    /**
     * 染色体核型
     */
    @ApiModelProperty(value = "染色体核型")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String karyoTypeFemale;
    /**
     * 染色体核型1
     */
    @ApiModelProperty(value = "染色体核型1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String karyoTypeMale;

    /**
     * 创建人ID/开单人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createrId;
    /**
     * 创建人姓名/开单名姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String createrName;
    /**
     * 创建时间/开单时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人ID
     */
    @ApiModelProperty(value = "修改人ID")
    private Long modifierId;
    /**
     * 修改人姓名
     */
    @ApiModelProperty(value = "修改人姓名")
    private String modifierName;
    /**
     * 修改日期
     */
    @ApiModelProperty(value = "修改日期")
    private Date modifyTime;
    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Long operationId;
    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operationName;
    /**
     * 操作日期
     */
    @ApiModelProperty(value = "操作日期")
    private Date operationTime;
}
