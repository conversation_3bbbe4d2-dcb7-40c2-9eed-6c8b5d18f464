/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-domain
 * 文件名称:ArtWeeklyReviewDetail.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.domain.audit;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yuxin.entity.BaseEntity;
import com.yuxin.search.annotation.EsIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <p>
 * 进周报告项目审核明细
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "ArtWeeklyReviewDetail", description = "进周报告项目审核明细")
public class ArtWeeklyReviewDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 周期主键ID
     */
    @ApiModelProperty(value = "周期主键ID")
    private Long cycleId;
    /**
     * ART预约ID
     */
    @ApiModelProperty(value = "ART预约ID")
    private Long artAppointmentId;
    /**
     * 审查项目ID
     */
    @ApiModelProperty(value = "审查项目ID")
    private Long itemId;
    /**
     * 适用性别 0:女 1:男
     */
    @ApiModelProperty(value = "适用性别 0:女 1:男")
    private Integer applicableGender;
    /**
     * 是否已检 0:未检 1:已检 2.忽略
     */
    @ApiModelProperty(value = "是否已检 0:未检 1:已检 2.忽略")
    private Integer checked;
    /**
     * 是否异常  0:否 1:是 2.忽略
     */
    @ApiModelProperty(value = "是否异常  0:否 1:是 2.忽略")
    private Integer abnormal;
    /**
     * 证件类型 0-非证件,1-指纹,2-身份证,3-快照,4-结婚证
     */
    @ApiModelProperty(value = "证件类型 0-非证件,1-指纹,2-身份证,3-快照,4-结婚证")
    private Integer type;
    /**
     * 忽略已检ID
     */
    @ApiModelProperty(value = "忽略已检ID")
    private Long ignoreCheckedId;
    /**
     * 忽略已检姓名
     */
    @ApiModelProperty(value = "忽略已检姓名")
    private String ignoreCheckedName;
    /**
     * 忽略已检日期
     */
    @ApiModelProperty(value = "忽略已检日期")
    private Date ignoreCheckedDate;
    /**
     * 忽略异常ID
     */
    @ApiModelProperty(value = "忽略异常ID")
    private Long ignoreAbnormalId;
    /**
     * 忽略异常姓名
     */
    @ApiModelProperty(value = "忽略异常姓名")
    private String ignoreAbnormalName;
    /**
     * 忽略异常日期
     */
    @ApiModelProperty(value = "忽略异常日期")
    private Date ignoreAbnormalDate;
    /**
     * 报告ID
     */
    @ApiModelProperty(value = "报告ID")
    private Long reportId;
    /**
     * 报告日期
     */
    @ApiModelProperty(value = "报告日期")
    private Date reportDate;
    /**
     * 审查项目名称
     */
    @ApiModelProperty(value = "审查项目名称")
    private String itemName;
    /**
     * 必检项结果类型
     */
    @ApiModelProperty(value = "必检项结果类型 0：报告 1：自选")
    @TableField(exist = false)
    @EsIgnore
    private Integer checkType;
    /**
     * 异常项结果类型
     */
    @ApiModelProperty(value = "异常项结果类型 0：报告 1：自选")
    @TableField(exist = false)
    @EsIgnore
    private Integer exceptionType;
}
