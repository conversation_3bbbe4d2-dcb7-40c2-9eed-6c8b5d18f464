/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-domain
 * 文件名称:SurgerySpermatorrheaDetail.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.domain.surgery;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yuxin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021-11-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@TableName("surgery_spermatorrhea_detail")
@ApiModel(value = "SurgerySpermatorrheaDetail", description = "")
public class SurgerySpermatorrheaDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 手淫手术ID
     */
    @ApiModelProperty(value = "手淫手术ID")
    private Long spermatorrheaId;
    /**
     * 手术方式：0:PESA 1.TESA 2.TESE 3.TESE(穿刺)
     */
    @ApiModelProperty(value = "手术方式：0:PESA 1.TESA 2.TESE 3.TESE(穿刺)")
    private Integer surgeryType;
    /**
     * 手术部位：0：睾丸 1：附睾
     */
    @ApiModelProperty(value = "手术部位：0：睾丸 1：附睾")
    private Integer surgerySite;
    /**
     * 麻醉类型 0:无 1:局部麻醉 2:静脉麻醉 3:连硬
     */
    @ApiModelProperty(value = "麻醉类型 0:无 1:局部麻醉 2:静脉麻醉 3:连硬")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer anesthesiaType;
    /**
     * 病理检查
     */
    @ApiModelProperty(value = "病理检查")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String pathologicalExamination;
    /**
     * 附睾液 0：无 1：有
     */
    @ApiModelProperty(value = "附睾液 0：无 1：有")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer epididymalFluid;
    /**
     * 曲细精管组织 0: 无 1：有
     */
    @ApiModelProperty(value = "曲细精管组织 0: 无 1：有")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer seminiferousTubules;
    /**
     * 液体 0 乳白色 1蛋血性 2血性 3脓性
     */
    @ApiModelProperty(value = "液体 0 乳白色 1蛋血性 2血性 3脓性")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer liquid;

}
