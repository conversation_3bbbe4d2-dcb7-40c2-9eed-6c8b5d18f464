/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-domain
 * 文件名称:ArtPlanAi.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.domain.art.plan;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.yuxin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <p>
 * ART计划详情-人工授精
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "ArtPlanAi", description = "ART计划详情-人工授精")
public class ArtPlanAi extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 计划单ID
     */
    @ApiModelProperty(value = "计划单ID")
    private Long planId;
    /**
     * 人工授精时间
     */
    @ApiModelProperty(value = "人工授精时间")
    private Date aiTime;
    /**
     * 取精来源，使用组合的方式，区分顺序，使用英文逗号隔开。0:手淫取精 1:手术取精 2:自身冷冻 3:供精
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    @ApiModelProperty(value = "取精来源，使用组合的方式，区分顺序，使用英文逗号隔开。0:手淫取精 1:手术取精 2:自身冷冻 3:供精")
    private String spermSource;
    /**
     * 取精时间
     */
    @ApiModelProperty(value = "取精时间")
    private Date spermCollectionTime;
    /**
     * 人工授精建议
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    @ApiModelProperty(value = "人工授精建议")
    private String aiProposal;
    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 编辑人ID
     */
    @ApiModelProperty(value = "编辑人ID")
    private Long recorderId;
    /**
     * 编辑人姓名
     */
    @ApiModelProperty(value = "编辑人姓名")
    private String recorderName;
    /**
     * 编辑日期
     */
    @ApiModelProperty(value = "编辑日期")
    private Date recorderTime;
    /**
     * 是否手术取精麻醉 0:否 1:是
     */
    @ApiModelProperty(value = "是否手术取精麻醉 0:否 1:是")
    private Integer isAnaesthesia;
}
