/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-domain
 * 文件名称:FtEmbryoRecoveryDetail.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.domain.fz;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.yuxin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <p>
 * 胚胎复苏评分详情
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "FtEmbryoRecoveryDetail", description = "胚胎复苏评分详情")
public class FtEmbryoRecoveryDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 胚胎解冻ID
     */
    @ApiModelProperty(value = "胚胎解冻ID")
    private Long thawEmbryoId;
    /**
     * 麦管解冻ID
     */
    @ApiModelProperty(value = "麦管解冻ID")
    private Long thawStrawEmbryoId;

    /**
     * 胚胎解冻详情ID
     */
    @ApiModelProperty(value = "胚胎解冻详情ID")
    private Long thawEmbryoDetailId;

    /**
     * 复苏ID
     */
    @ApiModelProperty(value = "复苏ID")
    private Long recoveryId;
    /**
     * 胚胎ID
     */
    @ApiModelProperty(value = "胚胎ID")
    private Long embryoId;

    /**
     * 最新胚胎ID
     */
    @ApiModelProperty(value = "最新胚胎ID")
    private Long latestEmbryoId;
    /**
     * 胚胎号
     */
    @ApiModelProperty(value = "胚胎号")
    private Integer embryoNo;
    /**
     * 冷冻日
     */
    @ApiModelProperty(value = "冷冻日：0：D1、1：D2、2：D3、3：D4、4：D5、5：D6、6：D7")
    private Integer frozenDay;
    /**
     * 冷冻方法 0:玻璃化 1:程序
     */
    @ApiModelProperty(value = "冷冻方法 0:玻璃化 1:程序")
    private Integer frozenMode;
    /**
     * 冷冻分类 0：MⅡ 1：MⅠ 2：GV 3：退化 4：异常 5：破裂 6：畸形 7：其他
     */
    @ApiModelProperty(value = "冷冻分类 0：MⅡ 1：MⅠ 2：GV 3：退化 4：异常 5：破裂 6：畸形 7：其他")
    private Integer frozenClassify;
    /**
     * 复苏分类 0：MⅡ 1：MⅠ 2：GV 3：退化 4：异常 5：破裂 6：畸形 7：其他
     */
    @ApiModelProperty(value = "复苏分类 0：MⅡ 1：MⅠ 2：GV 3：退化 4：异常 5：破裂 6：畸形 7：其他")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer recoveryClassify;

    /**
     * 去向（转归）阶段评分
     */
    @ApiModelProperty(value = "去向（转归）阶段评分")
    private String outcomeStageScore;

    /**
     * 去向（转归）阶段分类
     */
    @ApiModelProperty(value = "去向（转归）阶段分类")
    private String outcomeStageType;
    /**
     * 复苏评分
     */
    @ApiModelProperty(value = "复苏评分")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String recoveryScore;

    /**
     * 卵子来源
     */
    @ApiModelProperty(value = "卵子来源 ")
    private Integer ovumSource;

    /**
     * 精子来源 0-自身 1-供精
     */
    @ApiModelProperty(value = "精子来源 0-自身 1-供精")
    private Integer spermSource;
    /**
     * 复苏成功 0：成功  1：失败
     */
    @ApiModelProperty(value = "复苏成功 0：成功  1：失败")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer isSuccess;

    /**
     * 是否可用
     */
    @ApiModelProperty(value = "是否可用:0.不可用，1.可用")
    private Integer available;

    /**
     * 是否优质
     */
    @ApiModelProperty(value = "是否优质:0.否，1.是")
    private Integer highQuality;

    /**
     * 是否扩张
     */
    @ApiModelProperty(value = "是否扩张：0.否，1：是")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer expansion;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String remark;
    /**
     * 转归
     */
    @ApiModelProperty(value = "转归")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer outcome;
    /**
     * 观察人ID
     */
    @ApiModelProperty(value = "观察人ID")
    private Long observeId;
    /**
     * 观察人名称
     */
    @ApiModelProperty(value = "观察人名称")
    private String observeName;
    /**
     * 解冻时间
     */
    @ApiModelProperty(value = "解冻时间")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date thawTime;
    /**
     * 冷冻时间(yyyy-MM-dd HH:mm)
     */
    @ApiModelProperty(value = "冷冻时间(yyyy-MM-dd HH:mm)")
    private Date frozenerTime;

    /**
     * 评分时间(yyyy-MM-dd HH:mm)
     */
    @ApiModelProperty(value = "评分时间(yyyy-MM-dd HH:mm)")
    private Date scoreTime;

    /**
     * 新增状态
     */
    @ApiModelProperty(value = "新增状态.0：未新增、1：已新增")
    private Integer addStatus;

    /**
     * 是否可以修改.0:否、1：是
     */
    @ApiModelProperty(value = "是否可以修改.0:否、1：是")
    private Integer changeStatus;

    /**
     * 评分次数
     */
    @ApiModelProperty(value = "评分次数")
    private Integer scoreNumber;

    /**
     * 记录人ID
     */
    @ApiModelProperty(value = "记录人ID")
    private Long recorderId;
    /**
     * 记录人姓名
     */
    @ApiModelProperty(value = "记录人姓名")
    private String recorderName;
    /**
     * 记录日期
     */
    @ApiModelProperty(value = "记录日期")
    private Date recorderTime;

    /**
     * 是否PGT 0：否 1：是
     */
    @ApiModelProperty(value = "是否PGT 0：否 1：是")
    private Integer isPgt;
    /**
     * gpt建议 0-可移植 1-不可移植 2-待定 3-NA
     */
    @ApiModelProperty(value = "gpt建议 0-可移植 1-不可移植 2-待定 3-NA")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer pgtAdvice;

    /**
     * 是否AH
     */
    @ApiModelProperty(value = "是否AH：0.不做，1：做")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer isAh;
    /**
     * AH方法
     */
    @ApiModelProperty(value = "AH方法：0:激光1，1：激光2，2:激光3")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer ahMethod;
    /**
     * AH操作人ID
     */
    @ApiModelProperty(value = "AH操作人ID")
    private Long ahOperatorId;
    /**
     * AH操作人姓名
     */
    @ApiModelProperty(value = "AH操作人姓名")
    private String ahOperatorName;

}
