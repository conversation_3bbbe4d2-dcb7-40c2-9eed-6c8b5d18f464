/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-domain
 * 文件名称:ArtMalePhysical.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.domain.art.medicalhistory;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.yuxin.entity.BaseSysEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <p>
 * 男患者ART体格检查
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "ArtMalePhysical", description = "男患者ART体格检查")
public class ArtMalePhysical extends BaseSysEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 周期主键ID
     */
    @ApiModelProperty(value = "周期主键ID")
    private Long cycleId;
    /**
     * 患者主键ID
     */
    @ApiModelProperty(value = "患者主键ID")
    private Long patientId;
    /**
     * 一般检查-身高(cm)
     */
    @ApiModelProperty(value = "一般检查-身高(cm)")
    private BigDecimal giHeight;
    /**
     * 一般检查-体重（kg）
     */
    @ApiModelProperty(value = "一般检查-体重（kg）")
    private BigDecimal giWeight;
    /**
     * 一般检查-指距（cm）
     */
    @ApiModelProperty(value = "一般检查-指距（cm）")
    private BigDecimal giFingerDistance;
    /**
     * 一般检查-收缩压（mmHg）
     */
    @ApiModelProperty(value = "一般检查-收缩压（mmHg）")
    private BigDecimal giSystolicPressure;
    /**
     * 一般检查-扩张压（mmHg）
     */
    @ApiModelProperty(value = "一般检查-扩张压（mmHg）")
    private BigDecimal giExpansionPressure;
    /**
     * 一般检查-上下身比
     */
    @ApiModelProperty(value = "一般检查-上下身比")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal giBodyRatio;
    /**
     * 第二性征-喉结：正常1、异常-1、未查0；正常值：正常
     */
    @ApiModelProperty(value = "第二性征-喉结：正常1、异常-1、未查0；正常值：正常")
    private Integer secondaryProminentiaLaryngea;
    /**
     * 第二性征-阴毛：正常1、异常-1、未查0；正常值：正常
     */
    @ApiModelProperty(value = "第二性征-阴毛：正常1、异常-1、未查0；正常值：正常")
    private Integer secondaryPubicHair;
    /**
     * 第二性征-乳房：正常1、异常-1、未查0；正常值：正常
     */
    @ApiModelProperty(value = "第二性征-乳房：正常1、异常-1、未查0；正常值：正常")
    private Integer secondaryBreast;
    /**
     * 第二性征-胡须：有1、无-1、未查0；正常值：有
     */
    @ApiModelProperty(value = "第二性征-胡须：有1、无-1、未查0；正常值：有")
    private Integer secondaryBeard;
    /**
     * 专科检查-阴茎长度（0.成人型、1.儿童型、2.尿道下裂、3.未查）
     */
    @ApiModelProperty(value = "专科检查-阴茎长度（0.成人型、1.儿童型、2.尿道下裂、3.未查）")
    private Integer sePenisSize;
    /**
     * 专科检查-前列腺：正常1、异常-1、未查0；正常值：正常
     */
    @ApiModelProperty(value = "专科检查-前列腺：正常1、异常-1、未查0；正常值：正常")
    private Integer seProstate;
    /**
     * 专科检查-左睾丸大小
     */
    @ApiModelProperty(value = "专科检查-左睾丸大小")
    private Integer seLeftTesticleSize;
    /**
     * 专科检查-左睾丸质地：正常1、异常-1、未查0
     */
    @ApiModelProperty(value = "专科检查-左睾丸质地：正常1、异常-1、未查0")
    private Integer seLeftTesticleTexture;
    /**
     * 专科检查-左附睾：正常1、异常-1、未查0
     */
    @ApiModelProperty(value = "专科检查-左附睾：正常1、异常-1、未查0")
    private Integer seLeftEpididymis;
    /**
     * 专科检查-左输精管：正常1、异常-1、未查0
     */
    @ApiModelProperty(value = "专科检查-左输精管：正常1、异常-1、未查0")
    private Integer seLeftVasDeferens;
    /**
     * 专科检查-左精索静脉曲张：未查:0、正常：1、I：2、II：3、III：4
     */
    @ApiModelProperty(value = "专科检查-左精索静脉曲张：未查:0、正常：1、I：2、II：3、III：4")
    private Integer seLeftVaricocele;
    /**
     * 专科检查-右睾丸大小
     */
    @ApiModelProperty(value = "专科检查-右睾丸大小")
    private Integer seRightTesticleSize;
    /**
     * 专科检查-右睾丸质地：正常1、异常-1、未查0
     */
    @ApiModelProperty(value = "专科检查-右睾丸质地：正常1、异常-1、未查0")
    private Integer seRightTesticleTexture;
    /**
     * 专科检查-左附睾：正常1、异常-1、未查0
     */
    @ApiModelProperty(value = "专科检查-左附睾：正常1、异常-1、未查0")
    private Integer seRightEpididymis;
    /**
     * 专科检查-右输精管：正常1、异常-1、未查0
     */
    @ApiModelProperty(value = "专科检查-右输精管：正常1、异常-1、未查0")
    private Integer seRightVasDeferens;
    /**
     * 专科检查-右精索静脉曲张：未查:0、正常：1、I：2、II：3、III：4
     */
    @ApiModelProperty(value = "专科检查-右精索静脉曲张：未查:0、正常：1、I：2、II：3、III：4")
    private Integer seRightVaricocele;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String remark;

}
