/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-domain
 * 文件名称:FtThawStrawEmbryo.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.domain.fz;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuxin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <p>
 * 胚胎麦管解冻
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "FtThawStrawEmbryo", description = "胚胎麦管解冻")
public class FtThawStrawEmbryo extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 卵子解冻ID
     */
    @ApiModelProperty(value = "胚胎解冻ID")
    private Long thawEmbryoId;

    /**
     * 胚胎id
     */
    @ApiModelProperty(value = "胚胎ids,json")
    private String embryoIds;
    /**
     * 胚胎号
     */
    @ApiModelProperty(value = "胚胎号,json")
    private String embryoNos;
    /**
     * 冷冻日
     */
    @ApiModelProperty(value = "冷冻日：0：D1、1：D2、2：D3、3：D4、4：D5、5：D6、6：D7")
    private String frozenDays;
    /**
     * 胚胎评分
     */
    @ApiModelProperty(value = "去向（转归）阶段评分,json")
    private String outcomeStageScores;

    /**
     * 去向（转归）阶段分类
     */
    @ApiModelProperty(value = "去向（转归）阶段分类,json")
    private String outcomeStageTypes;
    /**
     * 精子来源
     */
    @ApiModelProperty(value = "精子来源.0：自身，1：供精")
    private Integer spermSource;
    /**
     * 受精方式
     */
    @ApiModelProperty(value = "受精方式")
    private Integer fertilizationMode;
    /**
     * 卵子来源
     */
    @ApiModelProperty(value = "卵子来源 ")
    private Integer ovumSource;
    /**
     * 卵子数量
     */
    @ApiModelProperty(value = "卵子数量")
    private Integer embryoTotal;
    /**
     * 麦管号
     */
    @ApiModelProperty(value = "麦管号")
    private String strawNo;
    /**
     * 载具颜色（字典）
     */
    @ApiModelProperty(value = "载具颜色（字典）")
    private String strawColor;
    /**
     * 位置
     */
    @ApiModelProperty(value = "位置")
    private String frozenPosition;
    /**
     * 状态.0：待确定、1：待取出:2：待解冻:3：已解冻
     */
    @ApiModelProperty(value = "状态.0：待确定、1：待取出:2：待解冻:3：已解冻")
    private Integer status;
    /**
     * 是否复苏
     */
    @ApiModelProperty(value = "是否复苏:0.否，1.是")
    private Integer isRecovery;

    /**
     * 临床建议
     */
    @ApiModelProperty(value = "临床建议")
    private String clinicalOpinions;

    /**
     * 指定解冻胚胎ID
     */
    @ApiModelProperty(value = "指定解冻胚胎IDs,json")
    private String chooseEmbryoIds;

    /**
     * 指定解冻胚胎号
     */
    @ApiModelProperty(value = "指定解冻胚胎号,json")
    private String chooseEmbryoNos;

    /**
     * 拟解冻数量
     */
    @ApiModelProperty(value = "拟解冻数量")
    private Integer draftThawNumber;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String remark;
    /**
     * 确定人ID
     */
    @ApiModelProperty(value = "确定人ID")
    private Long operatorId;
    /**
     * 确定人名称
     */
    @ApiModelProperty(value = "确定人名称")
    private String operatorName;
    /**
     * 确定日期
     */
    @ApiModelProperty(value = "确定日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date operatorDate;
    /**
     * 确定审核人ID
     */
    @ApiModelProperty(value = "确定审核人ID")
    private Long operatorReviewId;
    /**
     * 确定审核人名称
     */
    @ApiModelProperty(value = "确定审核人名称")
    private String operatorReviewName;
    /**
     * 确定审核日期
     */
    @ApiModelProperty(value = "确定审核日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date operatorReviewDate;
    /**
     * 取消确定人ID
     */
    @ApiModelProperty(value = "取消确定人ID")
    private Long cancelOperatorId;
    /**
     * 取消确定人名称
     */
    @ApiModelProperty(value = "取消确定人名称")
    private String cancelOperatorName;
    /**
     * 取消确定时间
     */
    @ApiModelProperty(value = "取消确定时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date cancelOperatorTime;
    /**
     * 取消确定审核人ID
     */
    @ApiModelProperty(value = "取消确定审核人ID")
    private Long cancelOperatorReviewId;
    /**
     * 取消确定审核人名称
     */
    @ApiModelProperty(value = "取消确定审核人名称")
    private String cancelOperatorReviewName;
    /**
     * 取消确定审核日期
     */
    @ApiModelProperty(value = "取消确定审核日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date cancelOperatorReviewDate;
    /**
     * 解冻人ID
     */
    @ApiModelProperty(value = "解冻人ID")
    private Long thawId;
    /**
     * 解冻人名称
     */
    @ApiModelProperty(value = "解冻人名称")
    private String thawName;
    /**
     * 解冻时间(HH:mm)
     */
    @ApiModelProperty(value = "解冻时间(yyyy-MM-dd)")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date thawTime;
    /**
     * 解冻审核人ID
     */
    @ApiModelProperty(value = "解冻审核人ID")
    private Long thawReviewId;
    /**
     * 解冻审核人名称
     */
    @ApiModelProperty(value = "解冻审核人名称")
    private String thawReviewName;
    /**
     * 解冻审核日期
     */
    @ApiModelProperty(value = "解冻审核日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date thawReviewDate;
    /**
     * 取消解冻人ID
     */
    @ApiModelProperty(value = "取消解冻人ID")
    private Long cancelThawId;
    /**
     * 取消解冻人名称
     */
    @ApiModelProperty(value = "取消解冻人名称")
    private String cancelThawName;
    /**
     * 取消解冻日期
     */
    @ApiModelProperty(value = "取消解冻日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date cancelThawDate;
    /**
     * 取消解冻审核人ID
     */
    @ApiModelProperty(value = "取消解冻审核人ID")
    private Long cancelThawReviewId;
    /**
     * 取消解冻审核人名称
     */
    @ApiModelProperty(value = "取消解冻审核人名称")
    private String cancelThawReviewName;
    /**
     * 取消解冻审核日期
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "取消解冻审核日期")
    private Date cancelThawReviewDate;
    /**
     * 取出人ID
     */
    @ApiModelProperty(value = "取出人ID")
    private Long fetchId;
    /**
     * 取出人名称
     */
    @ApiModelProperty(value = "取出人名称")
    private String fetchName;
    /**
     * 取出时间
     */
    @ApiModelProperty(value = "取出时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date fetchDate;
    /**
     * 取出审核人ID
     */
    @ApiModelProperty(value = "取出审核人ID")
    private Long fetchReviewId;
    /**
     * 取出审核人名称
     */
    @ApiModelProperty(value = "取出审核人名称")
    private String fetchReviewName;
    /**
     * 取出审核日期
     */
    @ApiModelProperty(value = "取出审核日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date fetchReviewDate;
    /**
     * 取出解冻人ID
     */
    @ApiModelProperty(value = "取出解冻人ID")
    private Long cancelFetchId;
    /**
     * 取出解冻人名称
     */
    @ApiModelProperty(value = "取出解冻人名称")
    private String cancelFetchName;
    /**
     * 取出解冻日期
     */
    @ApiModelProperty(value = "取出解冻日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date cancelFetchDate;
    /**
     * 取出解冻审核人ID
     */
    @ApiModelProperty(value = "取出解冻审核人ID")
    private Long cancelFetchReviewId;
    /**
     * 取出解冻审核人名称
     */
    @ApiModelProperty(value = "取出解冻审核人名称")
    private String cancelFetchReviewName;
    /**
     * 取出解冻审核日期
     */
    @ApiModelProperty(value = "取出解冻审核日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date cancelFetchReviewDate;

    /**
     * 胚胎pgt标记集合
     */
    @ApiModelProperty(value = "胚胎pgt标记集合,json")
    private String pgtStatus;

    /**
     * 胚胎pgt建议标记集合
     */
    @ApiModelProperty(value = "胚胎pgt建议标记集合,json")
    private String pgtAdvices;

}
