/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-domain
 * 文件名称:TodoClinical.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.domain.upcoming;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.yuxin.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <p>
 * 临床待办列表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "TodoClinical", description = "临床待办列表")
public class TodoClinical extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 周期主键ID
     */
    @ApiModelProperty(value = "周期主键ID")
    private Long cycleId;
    /**
     * 待办项目 0:扳机 1:取卵 2手淫取精 3:人工授精
     */
    @ApiModelProperty(value = "待办项目 0:扳机 1:取卵 2手淫取精 3:人工授精")
    private Integer item;
    /**
     * 女方ID
     */
    @ApiModelProperty(value = "女方ID")
    private Long patientFemaleId;
    /**
     * 女方姓名
     */
    @ApiModelProperty(value = "女方姓名")
    private String patientFemaleName;
    /**
     * 女方卡号
     */
    @ApiModelProperty(value = "女方卡号")
    private String patientFemaleCardNo;
    /**
     * 男方ID
     */
    @ApiModelProperty(value = "男方ID")
    private Long patientMaleId;
    /**
     * 男方姓名
     */
    @ApiModelProperty(value = "男方姓名")
    private String patientMaleName;
    /**
     * 男方卡号
     */
    @ApiModelProperty(value = "男方卡号")
    private String patientMaleCardNo;
    /**
     * ART医生ID
     */
    @ApiModelProperty(value = "ART医生ID")
    private Long artDoctorId;
    /**
     * ART医生姓名
     */
    @ApiModelProperty(value = "ART医生姓名")
    private String artDoctorName;
    /**
     * 治疗方案 0:AIH 1:AID 2:IVF 3:SIVF 4:ICSI 5:HICSI 6:FET 7:PESA_TESA 8:IVM 9:PGD
     */
    @ApiModelProperty(value = "治疗方案 0:AIH 1:AID 2:IVF 3:SIVF 4:ICSI 5:HICSI 6:FET 7:PESA_TESA 8:IVM 9:PGD")
    private Integer treatmentPlan;
    /**
     * 促排方案 0:AIH促排 1:黄体期长方案 2:OC后长方案 3:卵泡期长方案 4:超长方案 5:拮抗剂方案 6:短方案 7:超短方案 8:自然周期 9:黄体期促排 10:微刺激方案 11:替代周期 12:GnRH-HRT 13:GnRH-a后Gn周期
     */
    @ApiModelProperty(
        value = "促排方案 0:AIH促排 1:黄体期长方案 2:OC后长方案 3:卵泡期长方案 4:超长方案 5:拮抗剂方案 6:短方案 7:超短方案 8:自然周期 9:黄体期促排 10:微刺激方案 11:替代周期 12:GnRH-HRT 13:GnRH-a后Gn周期")
    private Integer cohPlan;
    /**
     * 执行时间
     */
    @ApiModelProperty(value = "执行时间")
    private Date executeTime;
    /**
     * 辅助处理
     */
    @ApiModelProperty(value = "辅助处理")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String auxiliary;
    /**
     * 状态 0:未完成 1:已确认 2:已完成 3:已作废
     */
    @ApiModelProperty(value = "状态 0:未完成 1:已确认 2:已完成 3:已作废")
    private Integer status;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String remark;
    /**
     * 数据来源模块 0:ART计划 1:其他
     */
    @ApiModelProperty(value = "数据来源模块 0:ART计划 1:其他")
    private Integer sourceModule;
    /**
     * 数据来源模块的ID，配合来源模块使用
     */
    @ApiModelProperty(value = "数据来源模块的ID，配合来源模块使用")
    private Long sourceModuleId;
    /**
     * 单号
     */
    @ApiModelProperty(value = "单号")
    private String orderNo;
    /**
     * 女性患者ID号
     */
    @ApiModelProperty(value = "女性患者ID号(20)")
    private String patientFemaleIdentifyId;
    /**
     * 男性患者ID号
     */
    @ApiModelProperty(value = "男性患者ID号(20)")
    private String patientMaleIdentifyId;
    /**
     * 治疗组ID
     */
    @ApiModelProperty(value = "治疗组ID")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long teamId;
    /**
     * 治疗组名(50)
     */
    @ApiModelProperty(value = "治疗组名(50)")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String teamName;

    /**
     * 处理建议
     */
    @ApiModelProperty(value = "处理建议 0-未处理 1-已处理 2-不处理")
    private Integer treatmentSuggestion;

    /**
     * 处理备注
     */
    @ApiModelProperty(value = "处理备注")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String treatmentRemark;

    /**
     * 记录人ID
     */
    @ApiModelProperty(value = "记录人ID")
    private Long recorderId;
    /**
     * 记录人姓名
     */
    @ApiModelProperty(value = "记录人姓名")
    private String recorderName;
    /**
     * 记录日期
     */
    @ApiModelProperty(value = "记录日期")
    private Date recorderTime;

    /**
     * 实际治疗方案
     */
    @ApiModelProperty(value = "实际治疗方案")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer actualTreatmentPlan;

    /**
     * 床位号
     */
    @ApiModelProperty(value = "床位号")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String bedNo;

    /**
     * 取精来源，使用组合的方式，区分顺序，使用英文逗号隔开。0:手淫取精 1:手术取精 2:自身冷冻 3:供精
     */
    @ApiModelProperty(value = "取精来源，使用组合的方式，区分顺序，使用英文逗号隔开。0:手淫取精 1:手术取精 2:自身冷冻 3:供精")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String spermSource;
}
