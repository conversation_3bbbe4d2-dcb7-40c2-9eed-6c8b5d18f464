/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-domain
 * 文件名称:ArtChoiceInspect.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.domain.art.medicalhistory;

import com.yuxin.entity.BaseSysEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * ART已选检查报告
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "ArtChoiceInspect", description = "ART已选检查报告")
public class ArtChoiceInspect extends BaseSysEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 周期主键ID
     */
    @ApiModelProperty(value = "周期主键ID")
    private Long cycleId;
    /**
     * 患者ID
     */
    @ApiModelProperty(value = "患者ID")
    private Long patientId;
    /**
     * 报告ID
     */
    @ApiModelProperty(value = "报告ID")
    private Long reportId;
    /**
     * 报告检查ID
     */
    @ApiModelProperty(value = "报告检查ID")
    private Long reportInspectId;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 数据来源.0：B超及检查；1：病史小结-特殊检查
     */
    @ApiModelProperty(value = "数据来源.0：B超及检查；1：病史小结-特殊检查")
    private Integer moduleType;

}
