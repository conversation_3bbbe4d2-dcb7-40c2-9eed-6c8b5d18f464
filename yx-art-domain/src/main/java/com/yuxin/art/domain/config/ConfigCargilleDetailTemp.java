/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-domain
 * 文件名称:ConfigCargilleDetailTemp.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.domain.config;

import com.yuxin.entity.BaseSysEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 配液模板详情
 *
 * <AUTHOR>
 * @date 2022-03-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "ConfigCargilleDetailTemp", description = "配液模板详情")
public class ConfigCargilleDetailTemp extends BaseSysEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 配液列表ID
     */
    @ApiModelProperty(value = "配液列表ID")
    private Long cargilleListId;
    /**
     * 模板项目  0为取卵日 1为D0日（IVF） 2为D0日（ICSI） 3为D0日（IVF/ICSI） 4为D0日（短时受精） 5为D3日 6为解冻 7为囊胚培养 8为移植日 9为FET移植日 10为精液处理
     */
    @ApiModelProperty(
        value = "模板项目  0为取卵日 1为D0日（IVF） 2为D0日（ICSI） 3为D0日（IVF/ICSI） 4为D0日（短时受精） 5为D3日 6为解冻 7为囊胚培养 8为移植日 9为FET移植日 10为精液处理")
    private Integer itemType;
    /**
     * 试剂ID
     */
    @ApiModelProperty(value = "试剂ID")
    private Long agentiaId;
    /**
     * 试剂量(字典)
     */
    @ApiModelProperty(value = "试剂量(字典)")
    private String agentiaAmount;
    /**
     * 皿ID
     */
    @ApiModelProperty(value = "皿ID")
    private Long vesselId;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer amount;
    /**
     * 用途(字典)
     */
    @ApiModelProperty(value = "用途(字典)")
    private String cargilleUse;
    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createrId;
    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String createrName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人ID
     */
    @ApiModelProperty(value = "修改人ID")
    private Long modifierId;
    /**
     * 修改人姓名
     */
    @ApiModelProperty(value = "修改人姓名")
    private String modifierName;
    /**
     * 修改日期
     */
    @ApiModelProperty(value = "修改日期")
    private Date modifyTime;
}
