/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-domain
 * 文件名称:ConfigInformeConsentTemp.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.domain.config;

import com.yuxin.entity.BaseSysEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <p>
 * 知情同意书模板
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "ConfigInformeConsentTemp", description = "知情同意书模板")
public class ConfigInformeConsentTemp extends BaseSysEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 模板类别ID
     */
    @ApiModelProperty(value = "模板类别ID")
    private Long consentTypeId;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String version;
    /**
     * 启用日期
     */
    @ApiModelProperty(value = "启用日期")
    private Date enableDate;
    /**
     * 停用日期
     */
    @ApiModelProperty(value = "停用日期")
    private Date deactivationDate;
    /**
     * 知情同意书内容
     */
    @ApiModelProperty(value = "知情同意书内容")
    private String content;
    /**
     * 是否可用 0：否 1：是
     */
    @ApiModelProperty(value = "是否可用 0：否 1：是")
    private Integer status;
    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String consentName;
}
