<!DOCTYPE HTML>
<html lang="">
<head>
    <style>
        @page {
            size: A5 landscape;
            /* top right bottom left */
            margin: 10px 40px 30px 50px;
        }

        .center {
            text-align: center;
        }

        body {
            font-size: 0.75em;
            font-family: Sim<PERSON><PERSON>, serif;
            line-height: 20px;
            margin: 0;
            padding: 0;
            width: 100%;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            page-break-inside: auto;
        }

        tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }

        hr {
            color: rgb(207, 200, 207);
            font-weight: lighter;
            margin: 10px 0 10px 0;
        }

        .padding3 {
            padding: 3px;
            vertical-align: top;
        }
    </style>
</head>
<body>
<div class="body">
    <div>
        <table>
            <#list merges as page>
                <tr>
                    <td colspan="7">
                        <div class="center" style="font-size: 1.4em; font-weight: bold;">医嘱单</div>
                    </td>
                </tr>
                <tr>
                    <td colspan="7">
                        <#if hospitalName??>
                            <div class="center"
                                 style="font-size: 1.2em; font-weight: bold; padding-top: 10px;">${hospitalName!}</div>
                        </#if>
                    </td>
                </tr>
                <tr>
                    <td style="font-size: 1.3em; font-weight: bold; padding-top: 20px;">其他用药</td>
                    <td colspan="6">
                        <div style="float: right; padding-top: 20px;">
                            <span style="margin-left: 5px;">病案号：${caseNo!}</span>
                            <span style="margin-left: 5px;">女方：${patientFemaleName!}(${patientFemaleAge!})</span>
                            <span style="margin-left: 5px;">ID：${patientFemaleIdentifyId!}</span>
                            <span style="margin-left: 5px;">男方：${patientMaleName!}(${patientMaleAge!})</span>
                            <span style="margin-left: 5px;">ID：${patientMaleIdentifyId!}</span>
                            <span style="margin-left: 5px;">床位号：${bedNo!}</span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="7">
                        <hr/>
                    </td>
                </tr>
                <tr>
                    <td style="width: 10%;"></td>
                    <td style="width: 15%;">用药日期</td>
                    <td style="width: 20%;">药品名称</td>
                    <td>&nbsp;</td>
                    <td style="width: 12%;text-align: right;margin-right: -20px;">用法</td>
                    <td style="width: 14%;"></td>
                    <td style="width: 10%;">医嘱医生</td>
                </tr>
                <tr>
                    <td colspan="7">
                        <hr/>
                    </td>
                </tr>
                <#list page as detail>
                <#--<#if detail.adviceType?? && detail.adviceType == 'otherAdvice'>
                    <tr>
                        <td colspan="7">
                            <hr/>
                        </td>
                    </tr>
                </#if>-->
                    <tr>
                        <td class="padding3">
                            <#if detail.adviceType??>
                                <#if detail.adviceType == 'trigger'>
                                    扳机用药
                                <#elseif detail.adviceType == 'change'>
                                    黄体转化
                                <#elseif detail.adviceType == 'support'>
                                    黄体支持
                                <#elseif detail.adviceType == 'otherDrug'>
                                    其他用药
                                <#elseif detail.adviceType == 'otherAdvice'>
                                    其他医嘱
                                </#if>
                            <#else>&nbsp;</#if>
                        </td>
                        <td class="padding3"><#if detail.date??>${detail.date!}<#else>&nbsp;</#if></td>
                        <#if detail.adviceTypeFlag?? && detail.adviceTypeFlag>
                            <td class="padding3" colspan="5">${detail.content!}</td>
                        <#else>
                            <td class="padding3">
                                <#if detail.drugNamePrint??>
                                    ${detail.drugNamePrint!}
                                <#else>
                                    ${detail.drugName!}
                                </#if>
                            </td>
                            <td class="padding3">${detail.content!}</td>
                            <td class="padding3">${detail.medicationMethod!}</td>
                            <td class="padding3">${detail.days!}</td>
                            <td class="padding3"><#if detail.doctorSign??>
                                <#if detail.doctorSign?starts_with("http")>
                                    <img src="${detail.doctorSign!}" style="width: 50px;height: 14px"/>
                                <#else>
                                    ${detail.doctorSign!}
                                </#if>
                            </#if></td>
                        </#if>
                    </tr>
                </#list>
                <tr>
                    <td colspan="7">
                        <hr/>
                    </td>
                </tr>
                <tr style="font-weight: bold;">
                    <td colspan="4" style="padding-top: 10px;">下次就诊日期：${nextVisit!} &nbsp;抽血B超</td>
                    <td colspan="3" style="padding-top: 10px;">
                        <div style="float: right;">
                            <span>就诊诊室：11楼${teamName!}B超室</span>
                        </div>
                    </td>
                </tr>
                <#if opuShow??>
                    <tr>
                        <td colspan="7" style="font-weight: bold; padding-top: 10px;">${opuShow!}</td>
                    </tr>
                </#if>
                <#if etShow??>
                    <tr>
                        <td colspan="7" style="font-weight: bold; padding-top: 10px;">${etShow!}</td>
                    </tr>
                </#if>

                <tr>
                    <td colspan="7" style="font-weight: bold; padding-top: 10px;">
                        【注意事项】：如药房处方上的用药方法与本医嘱冲突，以本医嘱单上的用药方法为准
                    </td>
                </tr>
                <#if page_has_next>
                    <tr>
                        <td>
                            <div style="page-break-after: always;"></div>
                        </td>
                    </tr>
                </#if>
            </#list>
        </table>
    </div>
</div>
</body>
</html>
