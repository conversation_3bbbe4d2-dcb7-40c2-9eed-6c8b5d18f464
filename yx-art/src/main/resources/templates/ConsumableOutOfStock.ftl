<!DOCTYPE HTML>
<html>
<head>
    <style>
        body {
            font-size: 0.75em;
        }

        @page {
            size: A4 landscape;
            margin-left: 20px;
            margin-right: 10px;
            /*margin: 0 auto;*/
        }

        .tr4 {
            width: 25%;
        }

        h4 {
            margin: 3px
        }

        table {
            page-break-inside: auto;
        }

        tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }

        hr {
            height: 1px;
        }

        .tableborder {
            width: 100%;
            border-collapse: collapse;
            border-style: solid;
            border-width: 0.5px;
            border-color: #000000;
        }

        .tableborder tr td {
            border-width: 0.5px;
            border-style: solid;
            border-color: #000000;
            padding: 9px 5px;
        }

        .bigtitle {
            margin: 0px;
            padding: 0px;
        }
    </style>
</head>
<body style="font-family: SimSun;line-height:1;margin: 0px;padding: 0px;width: 100%">
<div style="width:100%;">
    <h2 style="text-align: center">耗材缺货汇总</h2>
    <table class="tableborder">
        <tr>
            <td>缺货名称</td>
            <td>使用完毕日期</td>
            <td>最后出库日期</td>
            <td>分类</td>
            <td>使用完毕记录者</td>
        </tr>
        <#if list??&&(list?size>0)>
            <#list list as consumableStockIn>
                <tr>
                    <td>${consumableStockIn.consumableName!}</td>
                    <td>${consumableStockIn.usedUpDate!}</td>
                    <td>${consumableStockIn.lastStockOutDate!}</td>
                    <td>${consumableStockIn.consumableTypeName!}</td>
                    <td>${consumableStockIn.usedUpRecorder!}</td>
                </tr>
            </#list>
        <#else>
            <tr>
                <td> </td>
                <td> </td>
                <td> </td>
                <td> </td>
                <td> </td>
            </tr>
        </#if>
    </table>
</div>
</body>
</html>