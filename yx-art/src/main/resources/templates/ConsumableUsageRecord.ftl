<!DOCTYPE HTML>
<html>
<head>
    <style>
        body {
            font-size: 0.75em;
        }

        @page {
            size: A4 landscape;
            margin-left: 20px;
            margin-right: 10px;
            /*margin: 0 auto;*/
        }

        .tr4 {
            width: 25%;
        }

        h4 {
            margin: 3px
        }

        table {
            page-break-inside: auto;
        }

        tr {
            page-break-inside: avoid;
            page-break-after: auto;
        }

        hr {
            height: 1px;
        }

        .tableborder {
            width: 100%;
            border-collapse: collapse;
            border-style: solid;
            border-width: 0.5px;
            border-color: #000000;
        }

        .tableborder tr td {
            border-width: 0.5px;
            border-style: solid;
            border-color: #000000;
            padding: 9px 5px;
        }

        .bigtitle {
            margin: 0px;
            padding: 0px;
        }
    </style>
</head>
<body style="font-family: SimSun;line-height:1;margin: 0px;padding: 0px;width: 100%">
<div style="width:100%;">
    <h2 style="text-align: center">耗材使用记录汇总</h2>
    <table class="tableborder">
        <tr>
            <td>到货日期</td>
            <td>名称</td>
            <td>分类</td>
            <td>厂商</td>
            <td>供应商</td>
            <td>规格</td>
            <td>批号</td>
            <td>有效日期</td>
            <td>开瓶数量</td>
            <td>开瓶日期</td>
            <td>使用效期</td>
            <td>使用状态</td>
            <td>操作人</td>
            <td>核对人</td>
        </tr>
        <#if list??&&(list?size>0)>
            <#list list as consumableUsage>
                <tr>
                    <td><#if consumableUsage.arrivalDate??>${consumableUsage.arrivalDate?date}</#if></td>
                    <td>${consumableUsage.consumableName!}</td>
                    <td>${consumableUsage.consumableTypeName!}</td>
                    <td>${consumableUsage.manufacturerName!}</td>
                    <td>${consumableUsage.supplierName!}</td>
                    <#if  consumableUsage.specCustom??>
                        <td>${consumableUsage.specCustom!}</td>
                    <#else>
                        <td>${consumableUsage.specNum!}/${consumableUsage.specUnit!}/${consumableUsage.specPackUnit!}</td>
                    </#if>
                    <td>${consumableUsage.batchNumber!}</td>
                    <td><#if consumableUsage.expiryDate??>${consumableUsage.expiryDate?date}</#if></td>

                    <td>${consumableUsage.usageNum!}</td>
                    <td><#if consumableUsage.usageDate??>${consumableUsage.usageDate?date}</#if></td>

                    <td> <#if consumableUsage.usageExpireDate??>${consumableUsage.usageExpireDate?date}</#if></td>
                    <td>
                        <#if consumableUsage.usageStatus??>
                            <#if consumableUsage.usageStatus==0>
                                使用中
                            <#elseif consumableUsage.usageStatus==1>
                                使用完毕
                            <#else>
                                不入库
                            </#if>
                        </#if>
                    </td>
                    <td>${consumableUsage.operatorName!}</td>
                    <td>${consumableUsage.checkerName!}</td>


                </tr>
            </#list>
        <#else>
            <tr>
                <td> </td>
                <td> </td>
                <td> </td>
                <td> </td>
                <td> </td>
                <td> </td>
                <td> </td>
                <td> </td>
                <td> </td>
                <td> </td>
                <td> </td>
                <td> </td>
                <td> </td>
                <td> </td>
            </tr>
        </#if>
    </table>
</div>
</body>
</html>
