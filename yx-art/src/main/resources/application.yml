spring:
  application:
    name: yx-art
  profiles:
    active: ${profiles:local} #local:本地环境，dev:开发环境,test:测试环境,prod:生产环境
    include: sync
  mvc:
    throw-exception-if-no-handler-found: true  #出现404错误时，直接抛出异常
  servlet:
    multipart:
      enabled: true
      max-file-size: 104857600
      max-request-size: 104857600
  devtools:
    add-properties: false
  flyway:
    enabled: false
    clean-disabled: true
    clean-on-validation-error: false
  main:
    allow-bean-definition-overriding: true
#  main:
#    allow-bean-definition-overriding: true
#    lazy-initialization: true
server:
  shutdown: graceful #开启优雅停机,默认是IMMEDIATE,立即停机
  servlet:
    encoding:
      charset: UTF-8
      force: true

management:
  endpoint:
    health:
      show-details: always # 显示健康详情
    shutdown:
      enabled: true # 开启shutdown端点访问
  endpoints:
    web:
      base-path: /actuator # 访问根路径
      exposure:
        include: "*" # 开启除了shutdown以外的所有端点访问

mybatis-plus:
  global-config:
    banner: false
    db-config:
      id-type: auto
  type-aliases-package: com.yuxin.art
  mapper-locations: classpath:mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true

#框架配置
yuxin:
  json:
    enable-xss-deserializer: false
    enable-date-format: false
  filter:
    cors:
      enabled: true
    request:
      enabled: true
      order: 1
    xss:
      enabled: true
      order: 2
  security:
    multi-terminal-login: true
    single-valid-login: false
    jwt:
      audience: yx-art-user
      issuer: yx-art
      subject: auth
      expire-second: 36000
      refresh-token-expire-second: 72000
    anon-urls:
      - /
      - /assets/**
      - /css/**
      - /static/**
      - /statics/**
      - /upcoming/todoCommon/getStat
      - /upcoming/todoCommon/getIndexStat
      - /config/clientVersion/getNewVersion
      - /commons/getTimestamp
      - /commons/reloadResource
      - /upcoming/todoCommon/getPadStat
      - /external/**
      - /printPdf/**
      - /inspection/report/inspect
      - /inspection/report/examine
      - /fz/ftFrozenEmbryo/exportTagWithExcel
      - /sso/auth/**
      - /his/doctor/**
      - /common/artCycle/**
      - /actuator/**
      - /consumables/arrivals/exportPdf
      - /consumables/test/exportPdf
      - /consumables/test/exportSpermSurvivalTrial
      - /consumables/stock/in/exportPdf
      - /consumables/stock/summary/exportPdf
      - /consumables/stock/out-of-stock/export
      - /consumables/stock/out/out-of-stock/export
      - /consumables/usage/exportPdf
  doc:
    enabled: false
    contact-author: yuxin
    contact-url: http://dev.yuxin.net
    contact-email:
    title: 誉欣辅助生殖系统
    description: 誉欣辅助生殖系统
    url: http://dev.yuxin.net
    version: V1.0
    groups:
      - name: 认证授权
        base-package: com.yuxin.art.modules.auth
      - name: 通用接口
        base-package: com.yuxin.art.modules.common
      - name: 系统管理
        base-package: com.yuxin.art.modules.sys
      - name: 基础资料
        base-package: com.yuxin.art.modules.base
      - name: 配置管理
        base-package: com.yuxin.art.modules.config
      # - name: 耗材管理
      #  base-package: com.yuxin.art.modules.consumable
      - name: 耗材管理
        #base-package: com.yuxin.art.modules.consumable
        base-package: com.yuxin.art.modules.material
      - name: 患者资料
        base-package: com.yuxin.art.modules.patient
      - name: 特殊手术
        base-package: com.yuxin.art.modules.surgery
      - name: 审查
        base-package: com.yuxin.art.modules.audit
      - name: Art周期
        base-package: com.yuxin.art.modules.art.cycle
      - name: Art病程
        base-package: com.yuxin.art.modules.art.course
      - name: Art病史
        base-package: com.yuxin.art.modules.art.medicalhistory
      - name: 精子管理
        base-package: com.yuxin.art.modules.art.sperm
      - name: 医嘱
        base-package: com.yuxin.art.modules.doctororder
      - name: 检查检验报告
        base-package: com.yuxin.art.modules.inspection
      - name: 冻融管理
        base-package: com.yuxin.art.modules.fz
      - name: 用药
        base-package: com.yuxin.art.modules.medication
      - name: B超监测
        base-package: com.yuxin.art.modules.ultrasound
      - name: 卵子管理
        base-package: com.yuxin.art.modules.art.egg
      - name: 待办列表
        base-package: com.yuxin.art.modules.upcoming
      - name: Art计划
        base-package: com.yuxin.art.modules.art.plan
      - name: 体外授精
        base-package: com.yuxin.art.modules.art.ivf
      - name: 人工授精
        base-package: com.yuxin.art.modules.art.ai
      - name: 胚胎
        base-package: com.yuxin.art.modules.art.embryo
      - name: 胚胎观察
        base-package: com.yuxin.art.modules.art.embryoobservation
      - name: 移植
        base-package: com.yuxin.art.modules.art.transplant
      - name: 随访
        base-package: com.yuxin.art.modules.followup
      - name: 病案管理
        base-package: com.yuxin.art.modules.medicalrecord
      - name: 提醒管理
        base-package: com.yuxin.art.modules.remind
      - name: 打印服务
        base-package: com.yuxin.art.modules.print
      - name: 作业
        base-package: com.yuxin.art.job
      - name: 关联信息
        base-package: com.yuxin.art.relevance
      - name: 对外接口信息
        base-package: com.yuxin.art.external
      - name: his接口
        base-package: com.yuxin.art.his
      - name: 配液
        base-package: com.yuxin.art.modules.cargille
      - name: 麻醉
        base-package: com.yuxin.art.modules.anaesthesia
      - name: 收费清单
        base-package: com.yuxin.art.modules.fees
      - name: 胚胎pgt活检
        base-package: com.yuxin.art.modules.embryopgt
      - name: 门诊病历
        base-package: com.yuxin.art.modules.clinic
      - name: 日志
        base-package: com.yuxin.art.modules.logs
      - name: 冷冻物销毁
        base-package: com.yuxin.art.modules.destroy
  syslog:
    ignore-urls:
      - /commons/oss/upload

# Word文档生成配置
yuxin:
  word:
    template:
      base-path: "classpath:templates"
      female-history: "FemaleHistory.docx"
      male-history: "MaleHistory.docx"
    preview:
      base-url: "http://localhost:8080/preview"
      temp-path: "/tmp/word-preview"
      retention-minutes: 60
    comment:
      enabled: true
      author: "数据留痕系统"
      font-family: "宋体"
      font-size: 9
      color: "0066CC"
