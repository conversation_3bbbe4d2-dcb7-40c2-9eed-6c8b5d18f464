<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
  ~ 项目名称:yx-art-project
  ~ 模块名称：yx-art
  ~ 文件名称:UltrasoundInspectDetailMapper.xml
  ~ 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuxin.art.modules.ultrasound.mapper.UltrasoundInspectDetailMapper">

    <resultMap id="BaseResultMap" type="com.yuxin.art.domain.ultrasound.UltrasoundInspectDetail">
        <result column="id" property="id"/>
        <result column="inspect_id" property="inspectId"/>
        <result column="position" property="position"/>
        <result column="length" property="length"/>
        <result column="width" property="width"/>
        <result column="average" property="average"/>
        <result column="sort" property="sort"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , inspect_id, position, length, width, average, sort
    </sql>

    <select id="selectByInspectId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from ultrasound_inspect_detail where inspect_id=#{id}
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into ultrasound_inspect_detail (inspect_id, position, length, width, average, sort)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.inspectId}, #{entity.position}, #{entity.length}, #{entity.width},
            #{entity.average}, #{entity.sort})
        </foreach>
    </insert>

</mapper>
