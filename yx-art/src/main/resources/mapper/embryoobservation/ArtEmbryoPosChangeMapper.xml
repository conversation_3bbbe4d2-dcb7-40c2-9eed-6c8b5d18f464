<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
  ~ 项目名称:yx-art-project
  ~ 模块名称：yx-art
  ~ 文件名称:ArtEmbryoPosChangeMapper.xml
  ~ 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuxin.art.modules.art.embryoobservation.mapper.ArtEmbryoPosChangeMapper">

    <resultMap id="BaseResultMap" type="com.yuxin.art.domain.art.embryoobservation.ArtEmbryoPosChange">
        <result column="id" property="id"/>
        <result column="cycle_id" property="cycleId"/>
        <result column="ob_type" property="obType"/>
        <result column="embryo_id" property="embryoId"/>
        <result column="embryo_date" property="embryoDate"/>
        <result column="ob_flag" property="obFlag"/>
        <result column="incubator_no" property="incubatorNo"/>
        <result column="plate_number" property="plateNumber"/>
        <result column="plate_sort" property="plateSort"/>
        <result column="recorder_id" property="recorderId"/>
        <result column="recorder_name" property="recorderName"/>
        <result column="recorder_time" property="recorderTime"/>
        <result column="thaw_day" property="thawDay"/>
        <result column="observe_time" property="observeTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , cycle_id, ob_type, embryo_id, embryo_date, ob_flag,
        incubator_no, plate_number, plate_sort, recorder_id,
        recorder_name, recorder_time, thaw_day, observe_time
    </sql>

</mapper>
