<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
  ~ 项目名称:yx-art-project
  ~ 模块名称：yx-art
  ~ 文件名称:ArtEmbryoPgtMapper.xml
  ~ 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuxin.art.modules.art.embryoobservation.mapper.ArtEmbryoPgtMapper">

    <resultMap id="BaseResultMap" type="com.yuxin.art.domain.art.embryoobservation.ArtEmbryoPgt">
        <result column="id" property="id"/>
        <result column="cycle_id" property="cycleId"/>
        <result column="embryo_id" property="embryoId"/>
        <result column="ob_date" property="obDate"/>
        <result column="recorder_id" property="recorderId"/>
        <result column="recorder_name" property="recorderName"/>
        <result column="recorder_date" property="recorderDate"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator_name" property="operatorName"/>
        <result column="ob_day" property="obDay"/>
        <result column="embryo_no" property="embryoNo"/>
        <result column="embryo_type" property="embryoType"/>
        <result column="thaw_day" property="thawDay"/>
        <result column="biopsy_date" property="biopsyDate"/>
        <result column="inspect_date" property="inspectDate"/>
        <result column="receive_date" property="receiveDate"/>
        <result column="recipient" property="recipient"/>
        <result column="result" property="result"/>
        <result column="remark" property="remark"/>
        <result column="outcome_stage_score" property="outcomeStageScore"/>
        <result column="pgt_advice" property="pgtAdvice"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , cycle_id, embryo_id, ob_date, recorder_id, recorder_name,
        recorder_date, operator_id, operator_name, ob_day, embryo_no, embryo_type, thaw_day,
        biopsy_date,inspect_date,receive_date,recipient,result,remark,outcome_stage_score,pgt_advice
    </sql>

</mapper>
