<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
  ~ 项目名称:yx-art-project
  ~ 模块名称：yx-art
  ~ 文件名称:ConsumableTestSpermMapper.xml
  ~ 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuxin.art.modules.consumable.mapper.ConsumableTestSpermMapper">

    <resultMap id="BaseResultMap" type="com.yuxin.art.domain.consumable.ConsumableTestSperm">
        <result column="id" property="id"/>
        <result column="test_id" property="testId"/>
        <result column="exp_consumable_id" property="expConsumableId"/>
        <result column="exp_consumable_name" property="expConsumableName"/>
        <result column="exp_manufacturer_id" property="expManufacturerId"/>
        <result column="exp_manufacturer_name" property="expManufacturerName"/>
        <result column="exp_batch_number" property="expBatchNumber"/>
        <result column="exp_arrival_date" property="expArrivalDate"/>
        <result column="exp_expiry_date" property="expExpiryDate"/>
        <result column="ctrl_consumable_id" property="ctrlConsumableId"/>
        <result column="ctrl_consumable_name" property="ctrlConsumableName"/>
        <result column="ctrl_manufacturer_id" property="ctrlManufacturerId"/>
        <result column="ctrl_manufacturer_name" property="ctrlManufacturerName"/>
        <result column="ctrl_batch_number" property="ctrlBatchNumber"/>
        <result column="ctrl_arrival_date" property="ctrlArrivalDate"/>
        <result column="ctrl_expiry_date" property="ctrlExpiryDate"/>
        <result column="experimental_time" property="experimentalTime"/>
        <result column="experimental_temperature" property="experimentalTemperature"/>
        <result column="experimental_humidity" property="experimentalHumidity"/>
        <result column="sperm_source" property="spermSource"/>
        <result column="sperm_concentration" property="spermConcentration"/>
        <result column="sperm_survival" property="spermSurvival"/>
        <result column="test_result" property="testResult"/>
        <result column="experimental_result" property="experimentalResult"/>
        <result column="conclusion" property="conclusion"/>
        <result column="status" property="status"/>
        <result column="creater_id" property="createrId"/>
        <result column="creater_name" property="createrName"/>
        <result column="create_time" property="createTime"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="modifier_name" property="modifierName"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        test_id,
        exp_consumable_id,
        exp_consumable_name,
        exp_manufacturer_id,
        exp_manufacturer_name,
        exp_batch_number,
        exp_arrival_date,
        exp_expiry_date,
        ctrl_consumable_id,
        ctrl_consumable_name,
        ctrl_manufacturer_id,
        ctrl_manufacturer_name,
        ctrl_batch_number,
        ctrl_arrival_date,
        ctrl_expiry_date,
        experimental_time,
        experimental_temperature,
        experimental_humidity,
        sperm_source,
        sperm_concentration,
        sperm_survival,
        test_result,
        experimental_result,
        conclusion,
        status,
        creater_id,
        creater_name,
        create_time,
        modifier_id,
        modifier_name,
        modify_time
    </sql>



</mapper>
