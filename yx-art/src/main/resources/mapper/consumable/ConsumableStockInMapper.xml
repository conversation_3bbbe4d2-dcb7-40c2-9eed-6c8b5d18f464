<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
  ~ 项目名称:yx-art-project
  ~ 模块名称：yx-art
  ~ 文件名称:ConsumableStockInMapper.xml
  ~ 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuxin.art.modules.consumable.mapper.ConsumableStockInMapper">

    <resultMap id="BaseResultMap" type="com.yuxin.art.domain.consumable.ConsumableStockIn">
        <result column="id" property="id"/>
        <result column="arrival_id" property="arrivalId"/>
        <result column="test_id" property="testId"/>
        <result column="test_item" property="testItem"/>
        <result column="test_status" property="testStatus"/>
        <result column="stock_in_num" property="stockInNum"/>
        <result column="stock_in_date" property="stockInDate"/>
        <result column="stock_in_status" property="stockInStatus"/>
        <result column="stock_out_count" property="stockOutCount"/>
        <result column="stock_out_total_num" property="stockOutTotalNum"/>
        <result column="stock_num" property="stockNum"/>
        <result column="stock_status" property="stockStatus"/>
        <result column="remark" property="remark"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator_name" property="operatorName"/>
        <result column="operate_date" property="operateDate"/>
        <result column="checker_id" property="checkerId"/>
        <result column="checker_name" property="checkerName"/>
        <result column="checked_date" property="checkedDate"/>
        <result column="creater_id" property="createrId"/>
        <result column="creater_name" property="createrName"/>
        <result column="create_time" property="createTime"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="modifier_name" property="modifierName"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        arrival_id,
        test_id,
        test_item,
        test_status,
        stock_in_num,
        stock_in_date,
        stock_in_status,
        stock_out_count,
        stock_out_total_num,
        stock_num,
        stock_status,
        remark,
        operator_id,
        operator_name,
        operate_date,
        checker_id,
        checker_name,
        checked_date,
        creater_id,
        creater_name,
        create_time,
        modifier_id,
        modifier_name,
        modify_time
    </sql>
</mapper>
