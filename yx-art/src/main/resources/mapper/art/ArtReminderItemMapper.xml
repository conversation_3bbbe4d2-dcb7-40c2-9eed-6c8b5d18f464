<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
  ~ 项目名称:yx-art-project
  ~ 模块名称：yx-art
  ~ 文件名称:ArtReminderItemMapper.xml
  ~ 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuxin.art.modules.remind.mapper.ArtReminderItemMapper">

    <resultMap id="BaseResultMap" type="com.yuxin.art.domain.remind.ArtReminderItem">
        <result column="id" property="id"/>
        <result column="target" property="target"/>
        <result column="content" property="content"/>
        <result column="level" property="level"/>
        <result column="creater_id" property="createrId"/>
        <result column="creater_name" property="createrName"/>
        <result column="create_time" property="createTime"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="modifier_name" property="modifierName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="source_id" property="sourceId"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , target, content, level, creater_id, creater_name, create_time, modifier_id, modifier_name, modify_time,source_id
    </sql>


    <insert id="insertBatch" parameterType="java.util.List">
        insert into art_reminder_item (
        target,
        content,
        level,
        creater_id,
        creater_name,
        create_time,
        modifier_id,
        modifier_name,
        modify_time,
        source_id
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.target},
            #{item.content},
            #{item.level},
            #{item.createrId},
            #{item.createrName},
            #{item.createTime},
            #{item.modifierId},
            #{item.modifierName},
            #{item.modifyTime},
            #{item.sourceId}
            )
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update art_reminder_item
            <set>
                target=#{item.target},
                content=#{item.content},
                level=#{item.level},
                modifier_id=#{item.modifierId},
                modifier_name=#{item.modifierName},
                modify_time=#{item.modifyTime},
                source_id=#{item.sourceId}
            </set>
            where id = #{item.id}
        </foreach>
    </update>
</mapper>
