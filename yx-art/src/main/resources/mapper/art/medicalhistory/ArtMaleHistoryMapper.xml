<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
  ~ 项目名称:yx-art-project
  ~ 模块名称：yx-art
  ~ 文件名称:ArtMaleHistoryMapper.xml
  ~ 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuxin.art.modules.art.medicalhistory.mapper.ArtMaleHistoryMapper">

    <resultMap id="BaseResultMap" type="com.yuxin.art.domain.art.medicalhistory.ArtMaleHistory">
        <result column="id" property="id"/>
        <result column="cycle_id" property="cycleId"/>
        <result column="patient_id" property="patientId"/>
        <result column="chief_complaint" property="chiefComplaint"/>
        <result column="history_present_illness" property="historyPresentIllness"/>
        <result column="ph_erection" property="phErection"/>
        <result column="ph_ejaculation" property="phEjaculation"/>
        <result column="ph_min_coitus" property="phMinCoitus"/>
        <result column="ph_max_coitus" property="phMaxCoitus"/>
        <result column="ph_smoke" property="phSmoke"/>
        <result column="ph_alcohol" property="phAlcohol"/>
        <result column="ph_drug" property="phDrug"/>
        <result column="ph_mental_stimulation" property="phMentalStimulation"/>
        <result column="ph_drug_allergy" property="phDrugAllergy"/>
        <result column="ph_birth_defect" property="phBirthDefect"/>
        <result column="ph_habit_medication" property="phHabitMedication"/>
        <result column="ph_job_env_factor" property="phJobEnvFactor"/>
        <result column="fh_genetic" property="fhGenetic"/>
        <result column="fh_similar_diseases" property="fhSimilarDiseases"/>
        <result column="pmh_hepatitis" property="pmhHepatitis"/>
        <result column="pmh_tuberculosis" property="pmhTuberculosis"/>
        <result column="pmh_urinary_system" property="pmhUrinarySystem"/>
        <result column="pmh_cardiovascular" property="pmhCardiovascular"/>
        <result column="pmh_sexual_radiation" property="pmhSexualRadiation"/>
        <result column="pmh_diabetes" property="pmhDiabetes"/>
        <result column="pmh_thyroid_disease" property="pmhThyroidDisease"/>
        <result column="pmh_parotid_gland_disease" property="pmhParotidGlandDisease"/>
        <result column="pmh_epididymitis" property="pmhEpididymitis"/>
        <result column="pmh_prostatitis" property="pmhProstatitis"/>
        <result column="pmh_radiation" property="pmhRadiation"/>
        <result column="pmh_surgery" property="pmhSurgery"/>
        <result column="pmh_medicine" property="pmhMedicine"/>
        <result column="mrh_intermarry" property="mrhIntermarry"/>
        <result column="mrh_remarry" property="mrhRemarry"/>
        <result column="mrh_infertility_years" property="mrhInfertilityYears"/>
        <result column="remark" property="remark"/>
        <result column="recorder_id" property="recorderId"/>
        <result column="recorder_name" property="recorderName"/>
        <result column="recorder_date" property="recorderDate"/>
        <result column="mr_id" property="mrId"/>
        <result column="mr_name" property="mrName"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , cycle_id, patient_id, chief_complaint, history_present_illness, ph_smoke, ph_alcohol, ph_drug, ph_mental_stimulation, ph_drug_allergy, ph_birth_defect, ph_habit_medication, ph_job_env_factor, fh_genetic, fh_similar_diseases, pmh_hepatitis, pmh_tuberculosis, pmh_urinary_system, pmh_cardiovascular, pmh_sexual_radiation, mrh_intermarry, mrh_remarry, mrh_infertility_years, remark, recorder_id, recorder_name, recorder_date,
        ph_erection, ph_ejaculation, ph_min_coitus, ph_max_coitus, pmh_diabetes, pmh_thyroid_disease,pmh_parotid_gland_disease,pmh_epididymitis,
        pmh_prostatitis,pmh_radiation,pmh_surgery,pmh_medicine,mr_id,mr_name
    </sql>

    <sql id="Insert_Column_List">
        cycle_id, patient_id, chief_complaint, history_present_illness, ph_smoke, ph_alcohol, ph_drug, ph_mental_stimulation, ph_drug_allergy, ph_birth_defect, ph_habit_medication, ph_job_env_factor, fh_genetic, fh_similar_diseases, pmh_hepatitis, pmh_tuberculosis, pmh_urinary_system, pmh_cardiovascular, pmh_sexual_radiation, mrh_intermarry, mrh_remarry, mrh_infertility_years, remark, recorder_id, recorder_name, recorder_date,
        ph_erection, ph_ejaculation, ph_min_coitus, ph_max_coitus, pmh_diabetes, pmh_thyroid_disease,pmh_parotid_gland_disease,pmh_epididymitis,
        pmh_prostatitis,pmh_radiation,pmh_surgery,pmh_medicine,mr_id,mr_name
    </sql>

    <insert id="insertView" parameterType="com.yuxin.art.domain.art.medicalhistory.ArtMaleHistory" useGeneratedKeys="true" keyProperty="id">
        insert IGNORE into art_male_history (<include refid="Insert_Column_List"/>)
        values

        (
        #{cycleId},
        #{patientId},
        #{chiefComplaint},
        #{historyPresentIllness},
        #{phSmoke},
        #{phAlcohol},
        #{phDrug},
        #{phMentalStimulation},
        #{phDrugAllergy},
        #{phBirthDefect},
        #{phHabitMedication},
        #{phJobEnvFactor},
        #{fhGenetic},
        #{fhSimilarDiseases},
        #{pmhHepatitis},
        #{pmhTuberculosis},
        #{pmhUrinarySystem},
        #{pmhCardiovascular},
        #{pmhSexualRadiation},
        #{mrhIntermarry},
        #{mrhRemarry},
        #{mrhInfertilityYears},
        #{remark},
        #{recorderId},
        #{recorderName},
        #{recorderDate},
        #{phErection},
        #{phEjaculation},
        #{phMinCoitus},
        #{phMaxCoitus},
        #{pmhDiabetes},
        #{pmhThyroidDisease},
        #{pmhParotidGlandDisease},
        #{pmhEpididymitis},
        #{pmhProstatitis},
        #{pmhRadiation},
        #{pmhSurgery},
        #{pmhMedicine},
        #{mrId},
        #{mrName}
        )
    </insert>

</mapper>
