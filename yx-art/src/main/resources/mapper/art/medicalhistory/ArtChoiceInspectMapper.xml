<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
  ~ 项目名称:yx-art-project
  ~ 模块名称：yx-art
  ~ 文件名称:ArtChoiceInspectMapper.xml
  ~ 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuxin.art.modules.art.medicalhistory.mapper.ArtChoiceInspectMapper">

    <resultMap id="BaseResultMap" type="com.yuxin.art.domain.art.medicalhistory.ArtChoiceInspect">
        <result column="id" property="id"/>
        <result column="cycle_id" property="cycleId"/>
        <result column="patient_id" property="patientId"/>
        <result column="report_id" property="reportId"/>
        <result column="report_inspect_id" property="reportInspectId"/>
        <result column="sort" property="sort"/>
        <result column="creater_id" property="createrId"/>
        <result column="creater_name" property="createrName"/>
        <result column="create_time" property="createTime"/>
        <result column="modifier_id" property="modifierId"/>
        <result column="modifier_name" property="modifierName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="module_type" property="moduleType"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , cycle_id, patient_id, report_id, report_inspect_id, sort, creater_id, creater_name, create_time, modifier_id, modifier_name, modify_time,module_type
    </sql>


    <insert id="insertBatch" parameterType="java.util.List">
        insert into art_choice_inspect (
        cycle_id,
        patient_id,
        report_id,
        report_inspect_id,
        sort,
        creater_id,
        creater_name,
        create_time,
        modifier_id,
        modifier_name,
        modify_time,
        module_type
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.cycleId},
            #{item.patientId},
            #{item.reportId},
            #{item.reportInspectId},
            #{item.sort},
            #{item.createrId},
            #{item.createrName},
            #{item.createTime},
            #{item.modifierId},
            #{item.modifierName},
            #{item.modifyTime},
            #{item.moduleType}
            )
        </foreach>
    </insert>

    <select id="getBusinessList" resultType="com.yuxin.art.modules.art.medicalhistory.vo.ArtChoiceInspectVO">
        SELECT
        a.id,
        a.cycle_id,
        c.patient_id,
        c.patient_card_no,
        c.patient_identify_id,
        c.patient_name,
        c.patient_gender,
        c.patient_age,
        c.report_type,
        c.id as report_id,
        c.his_report_id,
        c.item_id as inspect_item_id,
        c.item_name as inspect_item_name,
        c.item_serial_no as inspect_item_serial_no,
        c.item_internal_type_code,
        a.report_inspect_id,
        c.have_image,
        c.our_hospital,
        c.report_hospital,
        c.report_doctor_id,
        c.report_doctor_name,
        c.report_date,
        c.report_validity,
        c.report_expiration_date,
        c.abnormal,
        a.module_type,
        a.sort,
        b.part,
        b.result,
        b.diagnosis,
        b.modify_diagnosis
        FROM
        art_choice_inspect a
        LEFT JOIN inspection_report_inspect b ON b.id=a.report_inspect_id
        LEFT JOIN inspection_report c ON a.report_id = c.id
        <where>
            <if test="cycleId!= null">
                and a.cycle_id=#{cycleId}
            </if>
            <if test="patientId!= null">
                and a.patient_id=#{patientId}
            </if>
            <if test="moduleType!= null">
                and a.module_type=#{moduleType}
            </if>
        </where>
        ORDER BY a.sort,a.create_time


    </select>
</mapper>
