<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
  ~ 项目名称:yx-art-project
  ~ 模块名称：yx-art
  ~ 文件名称:SurgeryFollicularPunctureDetailMapper.xml
  ~ 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuxin.art.modules.surgery.mapper.SurgeryFollicularPunctureDetailMapper">

    <resultMap id="BaseResultMap" type="com.yuxin.art.domain.surgery.SurgeryFollicularPunctureDetail">
        <result column="id" property="id"/>
        <result column="follicular_puncture_id" property="follicularPunctureId"/>
        <result column="follicle_size" property="follicleSize"/>
        <result column="left_puncture" property="leftPuncture"/>
        <result column="left_durplus" property="leftDurplus"/>
        <result column="left_total" property="leftTotal"/>
        <result column="right_puncture" property="rightPuncture"/>
        <result column="right_durplus" property="rightDurplus"/>
        <result column="right_total" property="rightTotal"/>
        <result column="puncture_total" property="punctureTotal"/>
        <result column="durplus_total" property="durplusTotal"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , follicular_puncture_id, follicle_size, left_puncture, left_durplus, left_total, right_puncture, right_durplus, right_total, puncture_total, durplus_total
    </sql>

</mapper>
