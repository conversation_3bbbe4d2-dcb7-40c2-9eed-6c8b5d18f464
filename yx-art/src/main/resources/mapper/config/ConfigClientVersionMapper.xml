<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
  ~ 项目名称:yx-art-project
  ~ 模块名称：yx-art
  ~ 文件名称:ConfigClientVersionMapper.xml
  ~ 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuxin.art.modules.config.mapper.ConfigClientVersionMapper">

    <resultMap id="BaseResultMap" type="com.yuxin.art.domain.config.ConfigClientVersion">
        <result column="id" property="id"/>
        <result column="client_version" property="clientVersion"/>
        <result column="client_info" property="clientInfo"/>
        <result column="client_address" property="clientAddress"/>
        <result column="operator_id" property="operatorId"/>
        <result column="operator_name" property="operatorName"/>
        <result column="operator_time" property="operatorTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , client_version, client_info, client_address, operator_id, operator_name, operator_time
    </sql>


    <insert id="insertBatch" parameterType="java.util.List">
        insert into config_client_version (client_version, client_info, client_address, operator_id, operator_name,
        operator_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.clientVersion}, #{entity.clientInfo}, #{entity.clientAddress}, #{entity.operatorId},
            #{entity.operatorName}, #{entity.operatorTime})
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="entity" index="index" open="" close="" separator=";">
            update config_client_version
            <set>
                client_version = #{entity.clientVersion},
                client_info = #{entity.clientInfo},
                client_address = #{entity.clientAddress},
                operator_id = #{entity.operatorId},
                operator_name = #{entity.operatorName},
                operator_time = #{entity.operatorTime}
            </set>
            where id = #{entity.id}
        </foreach>
    </update>
</mapper>
