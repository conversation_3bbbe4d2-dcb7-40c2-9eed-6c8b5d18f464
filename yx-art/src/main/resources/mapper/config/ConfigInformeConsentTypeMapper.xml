<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
  ~ 项目名称:yx-art-project
  ~ 模块名称：yx-art
  ~ 文件名称:ConfigInformeConsentTypeMapper.xml
  ~ 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuxin.art.modules.config.mapper.ConfigInformeConsentTypeMapper">

    <resultMap id="BaseResultMap" type="com.yuxin.art.domain.config.ConfigInformeConsentType">
        <result column="id" property="id"/>
        <result column="sort" property="sort"/>
        <result column="name" property="name"/>
        <result column="creater_id" property="createrId"/>
        <result column="creater_name" property="createrName"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , sort, name, creater_id, creater_name, create_time
    </sql>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update config_informe_consent_type
            <set>
                sort=#{item.sort},
                name=#{item.name}
            </set>
            where id = #{item.id}
        </foreach>
    </update>
</mapper>
