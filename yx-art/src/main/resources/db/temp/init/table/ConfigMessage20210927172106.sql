/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称：yx-art
 * 文件名称:ConfigMessage20210927172106.sql
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */

--
SET
@config_message := REPLACE(UUID(),"-","");
INSERT INTO `sys_resource`
(id, name, client_route, method, type, sort, parent_id, creater_id, creater_name, create_time, modifier_id,
 modifier_name, modify_time)
VALUES (@config_message, '', 'messages', 1, 0, 1, '0', '10000', 'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'),
        '10000', 'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'));
INSERT INTO `sys_resource`
(id, name, code, resource_url, method, type, sort, parent_id, creater_id, creater_name, create_time, modifier_id,
 modifier_name, modify_time)
VALUES (REPLACE(UUID(), "-", ""), '新增', 'config:message:create', '/config/message', 0, 1, 1, @config_message, '10000',
        'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'), '10000', 'superadmin',
        DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')),
       (REPLACE(UUID(), "-", ""), '删除', 'config:message:delete', '/config/message', 2, 1, 2, @config_message, '10000',
        'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'), '10000', 'superadmin',
        DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')),
       (REPLACE(UUID(), "-", ""), '编辑', 'config:message:edit', '/config/message', 3, 1, 3, @config_message, '10000',
        'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'), '10000', 'superadmin',
        DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')),
       (REPLACE(UUID(), "-", ""), '查询', 'config:message:get', '/config/message', 1, 1, 4, @config_message, '10000',
        'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'), '10000', 'superadmin',
        DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')),
       (REPLACE(UUID(), "-", ""), '分页查询', 'config:message:getPage', '/config/message', 1, 1, 5, @config_message,
        '10000', 'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'), '10000', 'superadmin',
        DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')),
       (REPLACE(UUID(), "-", ""), '批量删除', 'config:message:mDelete', '/config/message', 2, 1, 6, @config_message,
        '10000', 'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'), '10000', 'superadmin',
        DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')),
       (REPLACE(UUID(), "-", ""), '列表查询', 'config:message:getList', '/config/message', 1, 1, 7, @config_message,
        '10000', 'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'), '10000', 'superadmin',
        DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'));
