/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称：yx-art
 * 文件名称:SurgerySpermatorrheaDetail20211124102255.sql
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */

--
SET
@surgery_spermatorrhea_detail := REPLACE(UUID(),"-","");
INSERT INTO `sys_resource`
(id, name, client_route, method, type, sort, parent_id, creater_id, creater_name, create_time, modifier_id,
 modifier_name, modify_time)
VALUES (@surgery_spermatorrhea_detail, '', 'spermatorrhea-details', 1, 0, 1, '0', '10000', 'superadmin',
        DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'), '10000', 'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'));
INSERT INTO `sys_resource`
(id, name, code, resource_url, method, type, sort, parent_id, creater_id, creater_name, create_time, modifier_id,
 modifier_name, modify_time)
VALUES (REPLACE(UUID(), "-", ""), '新增', 'surgery:spermatorrhea-detail:create', '/surgery/spermatorrhea-detail', 0, 1, 1,
        @surgery_spermatorrhea_detail, '10000', 'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'), '10000',
        'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')),
       (REPLACE(UUID(), "-", ""), '删除', 'surgery:spermatorrhea-detail:delete', '/surgery/spermatorrhea-detail', 2, 1, 2,
        @surgery_spermatorrhea_detail, '10000', 'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'), '10000',
        'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')),
       (REPLACE(UUID(), "-", ""), '编辑', 'surgery:spermatorrhea-detail:edit', '/surgery/spermatorrhea-detail', 3, 1, 3,
        @surgery_spermatorrhea_detail, '10000', 'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'), '10000',
        'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')),
       (REPLACE(UUID(), "-", ""), '查询', 'surgery:spermatorrhea-detail:get', '/surgery/spermatorrhea-detail', 1, 1, 4,
        @surgery_spermatorrhea_detail, '10000', 'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'), '10000',
        'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')),
       (REPLACE(UUID(), "-", ""), '分页查询', 'surgery:spermatorrhea-detail:getPage', '/surgery/spermatorrhea-detail', 1, 1,
        5, @surgery_spermatorrhea_detail, '10000', 'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'), '10000',
        'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')),
       (REPLACE(UUID(), "-", ""), '批量删除', 'surgery:spermatorrhea-detail:mDelete', '/surgery/spermatorrhea-detail', 2, 1,
        6, @surgery_spermatorrhea_detail, '10000', 'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'), '10000',
        'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s')),
       (REPLACE(UUID(), "-", ""), '列表查询', 'surgery:spermatorrhea-detail:getList', '/surgery/spermatorrhea-detail', 1, 1,
        7, @surgery_spermatorrhea_detail, '10000', 'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'), '10000',
        'superadmin', DATE_FORMAT(NOW(), '%Y-%m-%d %H:%i:%s'));
