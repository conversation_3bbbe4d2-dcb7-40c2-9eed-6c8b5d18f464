/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:TodoReminderVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.upcoming.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 待办提醒VO
 *
 * <AUTHOR>
 * @date 2020-09-11
 */
@Data
@NoArgsConstructor
@ApiModel(value = "TodoReminderVO", description = "待办提醒VO")
public class TodoReminderVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 待办项目类型 0:COH待办 1:临床待办 2:移植待办 3:特殊手术待办 4:实验室待办
     */
    @ApiModelProperty(value = "待办项目类型 0:COH待办 1:临床待办 2:移植待办 3:特殊手术待办 4:实验室待办")
    private Integer itemType;

    /**
     * 待办项目
     */
    @ApiModelProperty(value = "待办项目，具体看所返回的Detail里面的item")
    private Integer item;
    /**
     * 临床待办详情
     */
    @ApiModelProperty(value = "临床待办详情（查询临床待办时返回）")
    private List<CommonTodoDetailVO> details;
}
