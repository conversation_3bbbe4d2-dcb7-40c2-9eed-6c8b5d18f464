/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:FtDoOvumVo.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.fz.vo;

import com.yuxin.art.domain.fz.FtDoOvum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 卵子冷冻
 *
 * <AUTHOR>
 * @date 2023-03-20
 */
@Data
@NoArgsConstructor
@ApiModel(value = "FtDoOvumVo", description = "卵子冷冻")
public class FtDoOvumVo extends FtDoOvum {
    /**
     * 配偶情况  0-前配偶 1-现配偶
     */
    @ApiModelProperty(value = "配偶情况  0-前配偶 1-现配偶")
    private Integer coupleStatus;
}
