/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:EditClinicFemaleHistoryVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.clinic.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 女患者门诊病史
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-10
 */
@Data
@NoArgsConstructor
@ApiModel(value = "EditClinicFemaleHistoryVO", description = "新增女患者门诊病史")
public class EditClinicFemaleHistoryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", required = true)
    @NotNull(message = "id不能为空")
    public Long id;

    /**
     * 门诊病历主键ID
     */
    @ApiModelProperty(value = "门诊病历主键ID", position = 2, required = true)
    @NotNull(message = "门诊病历主键ID不能为空")
    private Long recordId;
    /**
     * 患者主键ID
     */
    @ApiModelProperty(value = "患者主键ID", required = true)
    @NotNull(message = "患者主键ID不能为空")
    private Long patientId;
    /**
     * 主诉-主诉
     */
    @ApiModelProperty(value = "主诉(500)", required = false)
    @Length(max = 500, message = "主诉不能超过500个字符")
    private String chiefComplaint;
    /**
     * 主诉-现病史
     */
    @ApiModelProperty(value = "现病史(1000)", required = false)
    @Length(max = 1000, message = "现病史不能超过1000个字符")
    private String historyPresentIllness;
    /**
     * 月经史-月经初潮（岁）
     */
    @ApiModelProperty(value = "月经初潮（岁）", required = false)
    @Min(value = 10, message = "月经初潮（岁）不能小于10")
    private Integer mhMenarche;
    /**
     * 月经史-月经周期间隔min
     */
    @ApiModelProperty(value = "月经周期间隔min", required = false)
    @Min(value = 0, message = "月经周期间隔min不能小于0")
    private Integer mhMenstrualCycleMin;
    /**
     * 月经史-月经周期间隔max
     */
    @ApiModelProperty(value = "月经周期间隔max", required = false)
    @Min(value = 0, message = "月经周期间隔max不能小于0")
    private Integer mhMenstrualCycleMax;
    /**
     * 月经史-经期min
     */
    @ApiModelProperty(value = "经期min", required = false)
    @Min(value = 0, message = "经期min不能小于0")
    private Integer mhPeriodlMin;
    /**
     * 月经史-经期max
     */
    @ApiModelProperty(value = "经期max", required = false)
    @Min(value = 0, message = "经期max不能小于0")
    private Integer mhPeriodMax;
    /**
     * 末次月经
     */
    @ApiModelProperty(value = "末次月经", required = false)
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date mhLastMenstrualPeriod;
    /**
     * 月经史-痛经：0：无  1：I° 2：II° 3：III°
     */
    @ApiModelProperty(value = "月经史-痛经：0：无  1：I° 2：II° 3：III°", required = false)
    @Range(min = 0, max = 3, message = "无法识别的痛经状态")
    private Integer mhMenstrualColic;
    /**
     * 嗜烟（年）
     */
    @ApiModelProperty(value = "嗜烟（年）", required = false)
    @Min(value = 0, message = "嗜烟（年）不能小于0")
    private Integer phSmoke;
    /**
     * 嗜酒（年）
     */
    @ApiModelProperty(value = "嗜酒（年）", required = false)
    @Min(value = 0, message = "嗜酒（年）不能小于0")
    private Integer phAlcohol;
    /**
     * 吸毒（年）
     */
    @ApiModelProperty(value = "吸毒（年）", required = false)

    @Min(value = 0, message = "吸毒（年）不能小于0")
    private Integer phDrug;
    /**
     * 习惯用药：无、有
     * 正常值：无
     */
    @ApiModelProperty(value = "习惯用药：无、有 正常值：无", required = false)

    @Range(min = 0, max = 1, message = "无法识别的习惯用药")
    private Integer phHabitualMedication;
    /**
     * 精神刺激：无、有
     * 正常值：无
     */
    @ApiModelProperty(value = "精神刺激：无、有 正常值：无", required = false)
    @Range(min = 0, max = 1, message = "无法识别的精神刺激")
    private Integer phMentalStimulation;
    /**
     * 药物过敏史：无、有
     * 正常值：无
     */
    @ApiModelProperty(value = "药物过敏史：无、有 正常值：无", required = false)
    @Range(min = 0, max = 1, message = "无法识别的药物过敏史")
    private Integer phDrugAllergy;
    /**
     * 出生缺陷：无、有
     */
    @ApiModelProperty(value = "出生缺陷：无、有", required = false)
    @Range(min = 0, max = 1, message = "无法识别的出生缺陷")
    private Integer phBirthDefect;
    /**
     * 职业环境因素：无、有
     */
    @ApiModelProperty(value = "职业环境因素：无、有", required = false)
    @Range(min = 0, max = 1, message = "无法识别的职业环境因素")
    private Integer phJobEnvFactor;
    /**
     * 遗传史：无、有
     * 正常值：无
     */
    @ApiModelProperty(value = "遗传史：无、有 正常值：无", required = false)
    @Range(min = 0, max = 1, message = "无法识别的遗传史")
    private Integer fhGenetic;
    /**
     * 家庭类似疾病史：无、有
     * 正常值：无
     */
    @ApiModelProperty(value = "家庭类似疾病史：无、有 正常值：无", required = false)
    @Range(min = 0, max = 1, message = "无法识别的家庭类似疾病史")
    private Integer fhSimilarDiseases;
    /**
     * 肝炎：无、有
     * 正常值：无
     */
    @ApiModelProperty(value = "肝炎：无、有 正常值：无", required = false)
    @Range(min = 0, max = 1, message = "无法识别的肝炎")
    private Integer pmhHepatitis;
    /**
     * 结核病：无、有
     * 正常值：无
     */
    @ApiModelProperty(value = "结核病：无、有 正常值：无", required = false)
    @Range(min = 0, max = 1, message = "无法识别的结核病")
    private Integer pmhTuberculosis;
    /**
     * 肾脏病史：无、有
     * 正常值：无
     */
    @ApiModelProperty(value = "肾脏病史：无、有 正常值：无", required = false)
    @Range(min = 0, max = 1, message = "无法识别的肾脏病史")
    private Integer pmhKidney;
    /**
     * 心血管病：无、有
     * 正常值：无
     */
    @ApiModelProperty(value = "心血管病：无、有 正常值：无", required = false)
    @Range(min = 0, max = 1, message = "无法识别的心血管病")
    private Integer pmhCardiovascular;
    /**
     * 泌尿系统感染史：无、有
     * 正常值：无
     */
    @ApiModelProperty(value = "泌尿系统感染史：无、有 正常值：无", required = false)
    @Range(min = 0, max = 1, message = "无法识别的泌尿系统感染史")
    private Integer pmhUrinarySystem;
    /**
     * 性传播病：无、有
     * 正常值：无
     */
    @ApiModelProperty(value = "性传播病：无、有 正常值：无", required = false)
    @Range(min = 0, max = 1, message = "无法识别的性传播病")
    private Integer pmhSexualRadiation;
    /**
     * 盆腔炎史：无、有
     * 正常值：无
     */
    @ApiModelProperty(value = "盆腔炎史：无、有 正常值：无", required = false)
    @Range(min = 0, max = 1, message = "无法识别的盆腔炎史")
    private Integer pmhPelvicInfection;
    /**
     * 阑尾炎史：无、有
     * 正常值：无
     */
    @ApiModelProperty(value = "阑尾炎史：无、有 正常值：无", required = false)
    @Range(min = 0, max = 1, message = "无法识别的阑尾炎史")
    private Integer pmhAppendicitis;
    /**
     * 甲状腺疾病史：无、有
     */
    @ApiModelProperty(value = "甲状腺疾病史：无、有", required = false)
    @Range(min = 0, max = 1, message = "无法识别的甲状腺疾病史")
    private Integer pmhThyroidDisease;
    /**
     * 风湿免疫病史：无、有
     */
    @ApiModelProperty(value = "风湿免疫病史：无、有", required = false)
    @Range(min = 0, max = 1, message = "无法识别的风湿免疫病史")
    private Integer pmhRheumatismFree;
    /**
     * 血栓疾病史：无、有
     */
    @ApiModelProperty(value = "血栓疾病史：无、有", required = false)
    @Range(min = 0, max = 1, message = "无法识别的血栓疾病史")
    private Integer pmhThrombosis;
    /**
     * 特殊服药史：无、有
     */
    @ApiModelProperty(value = "特殊服药史：无、有", required = false)
    @Range(min = 0, max = 1, message = "无法识别的特殊服药史")
    private Integer pmhSpecialMedication;
    /**
     * 放射治疗史：无、有
     */
    @ApiModelProperty(value = "放射治疗史：无、有", required = false)
    @Range(min = 0, max = 1, message = "无法识别的放射治疗史")
    private Integer pmhRadiotherapy;
    /**
     * 手术治疗史：无、有
     * 正常值：无
     */
    @ApiModelProperty(value = "手术治疗史：无、有 正常值：无", required = false)
    @Range(min = 0, max = 1, message = "无法识别的手术治疗史")
    private Integer pmhSurgicalTreatment;
    /**
     * 近亲结婚：无、有
     * 正常值：无
     */
    @ApiModelProperty(value = "近亲结婚：无、有 正常值：无", required = false)
    @Range(min = 0, max = 1, message = "无法识别的近亲结婚")
    private Integer mrhIntermarry;
    /**
     * 再婚：无、有
     * 正常值：无
     */
    @ApiModelProperty(value = "再婚：无、有 正常值：无", required = false)
    @Range(min = 0, max = 1, message = "无法识别的再婚")
    private Integer mrhRemarry;
    /**
     * 再婚年限
     */
    @ApiModelProperty(value = "再婚年限", required = false)
    @Min(value = 0, message = "再婚年限不能小于0")
    private Integer mrhRemarryYears;
    /**
     * 怀孕次数
     */
    @ApiModelProperty(value = "怀孕次数", required = false)
    @Min(value = 0, message = "怀孕次数不能小于0")
    private Integer mrhPregnantNumber;
    /**
     * 生产次数
     */
    @ApiModelProperty(value = "生产次数", required = false)
    @Min(value = 0, message = "生产次数不能小于0")
    private Integer mrhFertilityNumber;
    /**
     * 足月产次数
     */
    @ApiModelProperty(value = "足月产次数", required = false)
    @Min(value = 0, message = "足月产次数不能小于0")
    private Integer mrhFullTermNumber;
    /**
     * 早产次数
     */
    @ApiModelProperty(value = "早产次数", required = false)
    @Min(value = 0, message = "早产次数不能小于0")
    private Integer mrhPretermBirthNumber;
    /**
     * 引产次数
     */
    @ApiModelProperty(value = "引产次数", required = false)
    @Min(value = 0, message = "引产次数不能小于0")
    private Integer mrhInducedLaborNumber;
    /**
     * 药流次数
     */
    @ApiModelProperty(value = "药流次数", required = false)
    @Min(value = 0, message = "药流次数不能小于0")
    private Integer mrhMedicalAbortionNumber;
    /**
     * 自然流产
     */
    @ApiModelProperty(value = "自然流产", required = false)
    @Min(value = 0, message = "自然流产不能小于0")
    private Integer mrhNaturalAbortionNumber;
    /**
     * 人工流产
     */
    @ApiModelProperty(value = "人工流产", required = false)
    @Min(value = 0, message = "人工流产不能小于0")
    private Integer mrhArtificialAbortionNumber;
    /**
     * 左宫外孕
     */
    @ApiModelProperty(value = "左宫外孕", required = false)
    @Min(value = 0, message = "左宫外孕不能小于0")
    private Integer mrhLeftExfetationNumber;
    /**
     * 右宫外孕
     */
    @ApiModelProperty(value = "右宫外孕", required = false)
    @Min(value = 0, message = "右宫外孕不能小于0")
    private Integer mrhRightExfetationNumber;
    /**
     * 现有子女
     */
    @ApiModelProperty(value = "现有子女", required = false)
    @Min(value = 0, message = "现有子女不能小于0")
    private Integer mrhExistingChildrenNumber;

    /**
     * 末次妊娠日期
     */
    @ApiModelProperty(value = "末次妊娠日期", required = false)
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date mrhLastPregnancyDate;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注(500)", required = false)
    @Length(max = 500, message = "备注不能超过500个字符")
    private String remark;

    /**
     * 不孕年限
     */
    @ApiModelProperty(value = "不孕年限")
    @Digits(integer = 3, fraction = 2, message = "不孕年限只能包含3位整数,2位小数")
    private BigDecimal infertilityYears;

    /**
     * 婚育史-避孕措施，0：安全期避孕，1：体外排精、2：避孕套、3：口服避孕药，4：宫内避孕药、5：长效避孕针、6：皮下埋植，多个使用英文逗号隔开
     */
    @ApiModelProperty(
        value = "婚育史-避孕措施，0：安全期避孕 1：体外排精 2：避孕套 3：口服避孕药 4：宫内避孕药 5：长效避孕针 6：皮下埋植，多个使用英文逗号隔开")
    @Length(max = 500, message = "避孕措施不能超过20个字符")
    private String mrhContraceptionMeasures;

    /**
     * 婚育史-生化妊娠次数
     */
    @ApiModelProperty(value = "婚育史-生化妊娠次数", required = false)
    @Range(min = 0, max = 99, message = "不能识别的生化妊娠次数")
    private Integer mrhBiochemicalPregnancy;

    /**
     * 月经史-月经量 0:少 1:中 2:多
     */
    @ApiModelProperty(value = "月经史-月经量 0:少 1:中 2:多", required = false)
    @Range(min = 0, max = 2, message = "不能识别的月经量")
    private Integer mhMenstrualVolume;

    /**
     * 病史医生ID
     */
    @ApiModelProperty(value = "记录人ID")
    private Long mrId;
    /**
     * 病史医生姓名
     */
    @ApiModelProperty(value = "记录人姓名")
    private String mrName;
}
