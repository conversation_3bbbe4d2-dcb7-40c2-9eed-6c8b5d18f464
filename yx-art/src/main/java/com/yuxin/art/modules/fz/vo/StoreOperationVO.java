/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:StoreOperationVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.fz.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 存放
 *
 * <AUTHOR>
 * @date 2020/8/6
 */
@Data
@NoArgsConstructor
@ApiModel(value = "StoreOperationVO", description = "存放")
public class StoreOperationVO extends OperationVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "麦管情况集合", required = true)
    @NotNull(message = "麦管情况集合不能为空")
    @Size(min = 1, message = "麦管情况集合不能为空")
    @Valid
    private List<StoreEggVO> storeEggVos;
}
