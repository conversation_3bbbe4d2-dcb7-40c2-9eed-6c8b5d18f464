/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:BaseExamineItemService.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.base.service;

import com.yuxin.art.domain.base.BaseExamineItem;
import com.yuxin.art.modules.base.vo.AddBaseExamineItemVO;
import com.yuxin.art.modules.base.vo.EditBaseExamineItemVO;
import com.yuxin.framework.mvc.service.BaseService;

import java.util.List;

/**
 * <p>
 * 检验项目 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
public interface BaseExamineItemService extends BaseService<BaseExamineItem> {
    /**
     * 通过项目类型获取列表
     *
     * @param reportTypeId
     * @return
     */
    List<BaseExamineItem> findListByReportTypeId(Long reportTypeId);

    /**
     * 根据是否可用查询列表
     *
     * @param status
     * @return
     */
    List<BaseExamineItem> findListByStatus(Integer status);

    /**
     * 通过项目类型和状态获取列表
     * @param reportTypeId
     * @param status
     * @return
     */
    // List<BaseExamineItem> findListByReportTypeIdAndStatus(Long reportTypeId, Integer status);

    /**
     * 修改检验项目和检验指标
     *
     * @param editBaseExamineItemVO
     */
    void businessEdit(EditBaseExamineItemVO editBaseExamineItemVO);

    /**
     * 添加检验项目
     *
     * @param addBaseExamineItemVO
     * @return
     */
    Long businessCreate(AddBaseExamineItemVO addBaseExamineItemVO);

    /**
     * 删除项目和指标
     *
     * @param id
     */
    void businessDelete(Long id);

    /**
     * 根据项目类型id删除项目和项目指标
     *
     * @param reportTypeId
     */
    void delByReportTypeId(Long reportTypeId);

    /**
     * 通过项目类型和名称查询
     * @param reportTypeId
     * @param name
     * @return
     */
    // BaseExamineItem getByReportTypeIdAndName(Long reportTypeId, String name);

    /**
     * 通过名称查询
     *
     * @param name
     * @return
     */
    BaseExamineItem getByName(String name);

    /**
     * 通过检查项目id查询项目过期信息
     * @param startDate
     * @param examineId
     * @return
     */
    // ResBaseExamineItem getByInspectId(Date startDate, String examineId);

    /**
     * 列表查询
     *
     * @return
     */
    List<BaseExamineItem> findList();

    /**
     * 列表查询
     *
     * @return
     */
    List<BaseExamineItem> getAvailableOcrList();


    /**
     * 根据性别可用检验项目查询
     *
     * @param gender
     * @return
     */
    List<BaseExamineItem> availableByGender(Integer gender);
}
