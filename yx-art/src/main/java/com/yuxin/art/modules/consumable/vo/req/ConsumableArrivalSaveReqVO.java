/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ConsumableArrivalSaveReqVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */

package com.yuxin.art.modules.consumable.vo.req;

import com.yuxin.validation.ValidateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 耗材到货VO
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@NoArgsConstructor
@ApiModel(value = "ConsumableArrivalSaveReqVO", description = "耗材到货VO")
public class ConsumableArrivalSaveReqVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    @NotNull(message = "ID不能为空", groups = {ValidateGroup.Update.class})
    protected Long id;

    /**
     * 耗材类型ID
     */
    @ApiModelProperty(value = "耗材类型ID", required = true)
    @NotNull(message = "耗材类型ID不能为空")
    private Long consumableTypeId;
    /**
     * 耗材类型名称
     */
    @ApiModelProperty(value = "耗材类型名称", required = true)
    @NotBlank(message = "耗材类型名称不能为空")
    @Length(max = 50, message = "耗材类型名称不能超过50个字符")
    private String consumableTypeName;
    /**
     * 耗材ID
     */
    @ApiModelProperty(value = "耗材ID", required = true)
    @NotNull(message = "耗材ID不能为空")
    private Long consumableId;
    /**
     * 耗材名称
     */
    @ApiModelProperty(value = "耗材名称", required = true)
    @NotBlank(message = "耗材名称不能为空")
    @Length(max = 50, message = "耗材名称不能超过50个字符")
    private String consumableName;
    /**
     * 厂商ID
     */
    @ApiModelProperty(value = "厂商ID", required = true)
    @NotNull(message = "厂商ID不能为空")
    private Long manufacturerId;
    /**
     * 厂商名称
     */
    @ApiModelProperty(value = "厂商名称", required = true)
    @NotBlank(message = "厂商名称不能为空")
    @Length(max = 50, message = "厂商名称不能超过50个字符")
    private String manufacturerName;

    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID", required = true)
    @NotNull(message = "供应商ID不能为空")
    private Long supplierId;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称", required = true)
    @NotBlank(message = "供应商名称不能为空")
    @Length(max = 50, message = "供应商名称不能超过50个字符")
    private String supplierName;
    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位", required = true)
    @NotBlank(message = "计量单位不能为空")
    private String unit;
    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @DecimalMin(value = "0", message = "单价不能小于0")
    @Digits(integer = 8, fraction = 2, message = "单价只能包含8位整数,2位小数")
    private BigDecimal unitPrice;
    /**
     * 自定义单价
     */
    @ApiModelProperty(value = "自定义单价")
    @Length(max = 50, message = "自定义单价不能超过50个字符")
    private String unitPriceCustom;

    /**
     * 规格-数量
     */
    @ApiModelProperty(value = "规格-数量")
    @DecimalMin(value = "0", message = "规格-数量不能小于0")
    @Digits(integer = 8, fraction = 2, message = "规格-数量只能包含8位整数,2位小数")
    private BigDecimal specNum;

    /**
     * 规格-单位
     */
    @ApiModelProperty(value = "规格-单位")
    @Length(max = 20, message = "规格-单位不能超过20个字符")
    private String specUnit;

    /**
     * 规格-包装单位
     */
    @ApiModelProperty(value = "规格-包装单位")
    @Length(max = 20, message = "规格-包装单位不能超过20个字符")
    private String specPackUnit;

    /**
     * 规格-自定义
     */
    @ApiModelProperty(value = "规格-自定义")
    @Length(max = 50, message = "自定义规格不能超过50个字符")
    private String specCustom;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号", required = true)
    @NotNull(message = "批次号不能为空")
    @Length(max = 50, message = "批次号不能超过50个字符")
    private String batchNumber;

    /**
     * 有效期
     */
    @ApiModelProperty(value = "有效期", required = true)
    @NotNull(message = "有效期不能为空")
    private Date expiryDate;

    /**
     * 到货数量
     */
    @ApiModelProperty(value = "到货数量", required = true)
    @NotNull(message = "到货数量不能为空")
    private Integer arrivalNum;

    /**
     * 到货计量单位
     */
    @ApiModelProperty(value = "到货计量单位", required = true)
    @NotBlank(message = "到货计量单位不能为空")
    @Length(max = 20, message = "到货计量单位不能超过20个字符")
    private String arrivalUnit;

    /**
     * 库存阈值
     */
    @ApiModelProperty(value = "库存阈值")
    private Integer threshold;

    /**
     * 检测项目 0：精子存活 1：鼠胚试验
     */
    @ApiModelProperty(value = "检测项目 0：精子存活 1：鼠胚试验")
    @Range(min = 0, max = 1, message = "无法识别的检测项目")
    private String testItem;

    /**
     * 测试温度
     */
    @ApiModelProperty(value = "测试温度")
    @DecimalMin(value = "0", message = "测试温度不能小于0")
    @Digits(integer = 3, fraction = 2, message = "测试温度只能包含3位整数,2位小数")
    private BigDecimal testTemperature;

    /**
     * 接收人ID
     */
    @ApiModelProperty(value = "接收人ID", required = true)
    @NotNull(message = "接收人ID不能为空")
    private Long receiverId;

    /**
     * 接收人姓名
     */
    @ApiModelProperty(value = "接收人姓名", required = true)
    @NotBlank(message = "接收人姓名不能为空")
    @Length(max = 50, message = "接收人姓名不能超过50个字符")
    private String receiverName;

    /**
     * 接收时物品状态.0:完整;1:破损;2:未溶解;3:已溶解
     */
    @ApiModelProperty(value = "接收时物品状态.0:完整;1:破损;2:未溶解;3:已溶解")
    @Range(min = 0, max = 3, message = "无法识别的物品状态")
    private Integer receivedStatus;

    /**
     * 接收时间
     */
    @ApiModelProperty(value = "接收时间", required = true)
    @NotNull(message = "接收时间不能为空")
    private Date receivedDate;

    /**
     * 核对人ID
     */
    @ApiModelProperty(value = "核对人ID", required = true)
    @NotNull(message = "核对人ID不能为空")
    private Long checkerId;

    /**
     * 核对人姓名
     */
    @ApiModelProperty(value = "核对人姓名", required = true)
    @NotBlank(message = "核对人姓名不能为空")
    @Length(max = 50, message = "核对人姓名不能超过50个字符")
    private String checkerName;
}
