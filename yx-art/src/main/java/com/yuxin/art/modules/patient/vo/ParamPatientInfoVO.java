/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ParamPatientInfoVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.patient.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 患者信息查询入参
 *
 * <AUTHOR>
 * @date 2020/6/16
 */
@Data
@NoArgsConstructor
@ApiModel(value = "ParamPatientInfoVO", description = "患者信息查询入参")
public class ParamPatientInfoVO extends ParamPatientUniquenessVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "姓名", required = false)
    private String name;

    @ApiModelProperty(value = "性别", required = false)
    @Range(min = 0, max = 1, message = "无法识别性别")
    private Integer gender;

    @ApiModelProperty(value = "是否有配偶(0:没有 1:有)", required = false)
    private Integer coupleStatus;
    /**
     * 绑定开始时间
     */
    @ApiModelProperty(value = "绑定开始时间")
    private String startBindDate;
    /**
     * 绑定结束时间
     */
    @ApiModelProperty(value = "绑定结束时间")
    private String endBindDate;

    /**
     * 页码，从1开始
     */
    @ApiModelProperty(value = "页码,从1开始", required = true)
    @NotNull(message = "页码不能为空且页码不能小于1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer page = 1;
    /**
     * 每页的数量,默认10
     */
    @ApiModelProperty(value = "每页显示数量", required = true)
    @NotNull
    @Min(value = 1, message = "每页显示数量不能小于1")
    @Max(value = 1000, message = "每页显示数量不能大于1000")
    private Integer limit = 10;
}
