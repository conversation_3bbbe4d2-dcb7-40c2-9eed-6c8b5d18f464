/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:BaseConsumableTypeSaveReqVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.base.vo.req;

import com.yuxin.validation.ValidateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 耗材类型
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-30
 */
@Data
@NoArgsConstructor
@ApiModel(value = "BaseConsumableTypeSaveReqVO", description = "耗材类型")
public class BaseConsumableTypeSaveReqVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @NotNull(message = "id不能为空", groups = {ValidateGroup.Update.class})
    private Long id;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称", required = true)
    @NotBlank(message = "类型名称不能为空")
    @Length(max = 20, message = "类型名称不能超过20个字符")
    private String name;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @Range(min = 1, message = "排序不能小于1")
    private Integer sort;
}
