/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:FtFrozenStockOvumDetailController.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.fz.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.yuxin.api.Result;
import com.yuxin.art.domain.fz.FtFrozenStockOvumDetail;
import com.yuxin.art.modules.fz.service.FtFrozenStockOvumDetailService;
import com.yuxin.art.modules.fz.vo.DiscardOperationVO;
import com.yuxin.art.modules.fz.vo.FrozenOvumPatientVO;
import com.yuxin.art.modules.fz.vo.PositionOperationVO;
import com.yuxin.framework.mvc.controller.BaseController;
import com.yuxin.framework.mvc.vo.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 卵子冷冻库存详情 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-07
 */
@RestController
@RequestMapping(value = "/fz/ftFrozenStockOvumDetail", produces = MediaType.APPLICATION_JSON_VALUE)
@Validated
@Api(tags = "A2卵子冷冻库存详情")
@ApiSort(1)
public class FtFrozenStockOvumDetailController extends BaseController {
    @Autowired
    private FtFrozenStockOvumDetailService ftFrozenStockOvumDetailService;

    /**
     * 冷冻卵子病人列表
     *
     * @return
     */
    @GetMapping("/getPage")
    @ApiOperation(value = "冷冻卵子病人列表")
    @ApiOperationSupport(order = 1)
    // @RequiresPermissions("fz:ftFrozenStockOvumDetail:getPage")
    public Result<IPage<FrozenOvumPatientVO>> getPage(@Validated PageParams page) {
        IPage<FrozenOvumPatientVO> list = ftFrozenStockOvumDetailService.getFrozenPatientList(page);
        return Result.success(list);
    }

    /**
     * 变更位置
     *
     * @param vo
     */
    @PutMapping(value = "/editPosition", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "变更位置", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 3)
    // @RequiresPermissions("fz:ftFrozenStockOvumDetail:editPosition")
    public Result<Void> editPosition(@RequestBody @Validated PositionOperationVO vo) {
        ftFrozenStockOvumDetailService.editPosition(vo.getId(), vo.getStrawNo(), vo.getPosition());
        return Result.success();
    }

    /**
     * 丢弃
     *
     * @param discardOperationVo
     */
    @PutMapping(value = "/editDiscard", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "丢弃", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 3)
    // @RequiresPermissions("fz:ftFrozenStockOvumDetail:editDiscard")
    public Result<Void> editDiscard(@RequestBody @Validated DiscardOperationVO discardOperationVo) {
        ftFrozenStockOvumDetailService.editDiscard(discardOperationVo);
        return Result.success();
    }

    /**
     * 销毁
     *
     * @param discardOperationVo
     */
    @PutMapping(value = "/editDestroy", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "销毁", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 4)
    public Result<Void> editDestroy(@RequestBody @Validated DiscardOperationVO discardOperationVo) {
        ftFrozenStockOvumDetailService.editDestroy(discardOperationVo);
        return Result.success();
    }

    /**
     * 通过配偶ID和状态查询冷冻卵子列表
     *
     * @return
     */
    @GetMapping("/getFrozenStrawListByPairBondIdAndStatus")
    @ApiOperation(value = "通过配偶ID和状态查询冷冻卵子列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "pairBondId", value = "配偶id", required = true, paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "status", value = "0：可使用；1：已预约；2：已解冻；3：丢弃；4：退还；", required = true, paramType = "query",
            dataType = "Integer")})
    @ApiOperationSupport(order = 1)
    // @RequiresPermissions("fz:ftFrozenStockOvumDetail:getFrozenStrawListByPairBondIdAndStatus")
    public Result<List<FtFrozenStockOvumDetail>> getFrozenStrawListByPairBondIdAndStatus(@NotNull(message = "配偶id") Long pairBondId,
        @NotNull(message = "状态不能为空") Integer status) {
        List<FtFrozenStockOvumDetail> list = ftFrozenStockOvumDetailService.getListByPairBondIdAndStatus(pairBondId, status);
        return Result.success(list);
    }

    /**
     * 通过女性患者ID和状态查询列表
     *
     * @return
     */
    @GetMapping("/getListByPatientFemaleIdAndStatus")
    @ApiOperation(value = "通过女性患者ID和状态查询列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "patientFemaleId", value = "女性id", required = true, paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "status", value = "0：可使用；1：已预约；2：已解冻；3：丢弃；4：退还；", required = true, paramType = "query",
            dataType = "Integer")})
    @ApiOperationSupport(order = 1)
    // @RequiresPermissions("fz:ftFrozenStockOvumDetail:getListByPatientFemaleIdAndStatus")
    public Result<List<FtFrozenStockOvumDetail>> getListByPatientFemaleIdAndStatus(@NotNull(message = "女性id不能为空") Long patientFemaleId,
        @NotNull(message = "状态不能为空") Integer status) {
        List<FtFrozenStockOvumDetail> list = ftFrozenStockOvumDetailService.getListByPatientFemaleIdAndStatus(patientFemaleId, status);
        return Result.success(list);
    }

    /**
     * 通过配偶ID查询冷冻卵子列表
     *
     * @return
     */
    @GetMapping("/getFrozenStrawListByPairBondId")
    @ApiOperation(value = "通过配偶ID查询冷冻卵子列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "pairBondId", value = "配偶id", required = true, paramType = "query", dataType = "String")})
    @ApiOperationSupport(order = 1)
    public Result<List<FtFrozenStockOvumDetail>> getFrozenStrawListByPairBondId(@NotNull(message = "配偶id") Long pairBondId) {
        List<FtFrozenStockOvumDetail> list = ftFrozenStockOvumDetailService.getListByPairBondId(pairBondId);
        return Result.success(list);
    }

    /**
     * 通过女性患者ID查询列表
     *
     * @return
     */
    @GetMapping("/getListByPatientFemaleId")
    @ApiOperation(value = "通过女性患者ID查询列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "patientFemaleId", value = "女性id", required = true, paramType = "query", dataType = "String")})
    @ApiOperationSupport(order = 1)
    public Result<List<FtFrozenStockOvumDetail>> getListByPatientFemaleId(@NotNull(message = "女性id不能为空") Long patientFemaleId) {
        List<FtFrozenStockOvumDetail> list = ftFrozenStockOvumDetailService.getListByPatientFemaleId(patientFemaleId);
        return Result.success(list);
    }

}
