/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:TodoClinicalController.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.upcoming.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.yuxin.api.Result;
import com.yuxin.art.domain.upcoming.TodoClinical;
import com.yuxin.art.modules.upcoming.service.TodoClinicalService;
import com.yuxin.art.modules.upcoming.vo.EditTreatmentVO;
import com.yuxin.art.modules.upcoming.vo.QueryHcgTodoVO;
import com.yuxin.art.modules.upcoming.vo.QueryTodoVO;
import com.yuxin.art.modules.upcoming.vo.TodoGroup;
import com.yuxin.framework.mvc.controller.BaseController;
import com.yuxin.framework.mvc.vo.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 临床待办列表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-27
 */
@RestController
@RequestMapping(value = "/upcoming/todoClinical", produces = MediaType.APPLICATION_JSON_VALUE)
@Validated
@Api(tags = "临床待办列表")
@ApiSort(1)
public class TodoClinicalController extends BaseController {
    @Autowired
    private TodoClinicalService todoClinicalService;

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "查询")
    @ApiImplicitParam(name = "id", value = "id", required = true, paramType = "path", dataType = "Long")
    @ApiOperationSupport(order = 4)
    // @RequiresPermissions("upcoming:todoClinical:get")
    public Result<TodoClinical> get(@NotNull(message = "id不能为空") @PathVariable("id") Long id) {
        TodoClinical todoClinical = todoClinicalService.get(id);
        return Result.success(todoClinical);
    }

    /**
     * 分页查询
     *
     * @param page 分页查询参数
     * @return
     */
    @GetMapping("/getPage")
    @ApiOperation(value = "分页查询")
    @ApiOperationSupport(order = 5)
    // @RequiresPermissions("upcoming:todoClinical:getPage")
    public Result<IPage<TodoClinical>> getPage(@Validated PageParams page) {
        IPage<TodoClinical> pageInfo = todoClinicalService.getList(page);
        return Result.success(pageInfo);
    }

    /**
     * 分页查询
     *
     * @param page 分页查询参数
     * @return
     */
    @GetMapping("/getPageNew")
    @ApiOperation(value = "分页查询")
    @ApiOperationSupport(order = 5)
    // @RequiresPermissions("upcoming:todoClinical:getPage")
    public Result<IPage<TodoClinical>> getPageNew(@Validated PageParams page) {
        IPage<TodoClinical> pageInfo = todoClinicalService.getListNew(page);
        return Result.success(pageInfo);
    }

    /**
     * 列表查询
     *
     * @return
     */
    @GetMapping("/getList")
    @ApiOperation(value = "列表查询")
    @ApiOperationSupport(order = 7)
    // @RequiresPermissions("upcoming:todoClinical:getList")
    public Result<List<TodoClinical>> getList(@Validated QueryTodoVO vo) {
        return Result.success(todoClinicalService.getList(vo, false));
    }

    /**
     * 分组列表查询
     *
     * @return
     */
    @GetMapping("/getGroupList")
    @ApiOperation(value = "分组列表查询")
    @ApiOperationSupport(order = 8)
    // @RequiresPermissions("upcoming:todoClinical:getGroupList")
    public Result<List<TodoGroup>> getGroupList(@Validated QueryTodoVO vo) {
        List<TodoGroup> list = todoClinicalService.getGroupList(vo);
        return Result.success(list);
    }

    /**
     * 编辑处理建议
     *
     * @param editTreatmentVO
     */
    @PutMapping(value = "/editTreatment", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "编辑", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 9)
    // @RequiresPermissions("upcoming:todoClinical:editTreatment")
    public Result<Void> editTreatment(@RequestBody @Validated EditTreatmentVO editTreatmentVO) {
        todoClinicalService.setTreatment(editTreatmentVO);
        return Result.success();
    }

    /**
     * 列表查询
     *
     * @return
     */
    @GetMapping("/getHcgList")
    @ApiOperation(value = "列表查询")
    @ApiOperationSupport(order = 10)
    public Result<List<TodoClinical>> getHcgList(@Validated QueryHcgTodoVO vo) {
        return Result.success(todoClinicalService.getHcgList(vo));
    }

}
