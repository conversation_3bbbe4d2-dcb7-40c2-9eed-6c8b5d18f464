/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:AddConfigEmbryoObservationDetailVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.config.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <p>
 * 胚胎计算规则明细
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
@Data
@NoArgsConstructor
@ApiModel(value = "AddConfigEmbryoObservationDetailVO", description = "新增胚胎计算规则明细")
public class AddConfigEmbryoObservationDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 条件字段,JSON格式：{'classify':'条件','code':'size','value':'1','replaceValue':'细胞大小'}
     */
    @ApiModelProperty(value = "条件字段,JSON格式：:{'classify':'条件','code':'size','value':'1','replaceValue':'细胞大小'}(200)", required = true)
    @NotBlank(message = "条件字段不能为空")
    @Length(max = 200, message = "条件字段不能超过200个字符")
    private String conditionField;
    /**
     * 运算符
     */
    @ApiModelProperty(value = "运算符(10)", required = true)
    @NotBlank(message = "运算符不能为空")
    @Length(max = 10, message = "运算符不能超过10个字符")
    private String operator;
    /**
     * 序号
     */
    @ApiModelProperty(value = "序号(1-1000)")
    @Min(value = 1, message = "无法识别的序号")
    @Max(value = 1000, message = "无法识别的序号")
    private Integer sort;

    /**
     * 值
     */
    @ApiModelProperty(value = "值(10)", required = true)
    @NotBlank(message = "值不能为空")
    @Length(max = 10, message = "值不能超过10个字符")
    private String num;

    /**
     * 右值
     */
    @ApiModelProperty(value = "右值,当操作值为between时，必填(10)")
    @Length(max = 10, message = "右值不能超过10个字符")
    private String rightNum;

    /**
     * 规则合并
     */
    @ApiModelProperty(value = "规则合并(100)", required = true)
    @NotBlank(message = "规则合并不能为空")
    @Length(max = 100, message = "规则合并不能超过100个字符")
    private String ruleMerge;
}
