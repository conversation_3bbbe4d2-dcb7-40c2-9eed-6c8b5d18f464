/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ArtDiagnosIndicationController.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.art.medicalhistory.controller;

import cn.hutool.core.bean.BeanUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.yuxin.api.Result;
import com.yuxin.art.domain.art.medicalhistory.ArtDiagnos;
import com.yuxin.art.domain.art.medicalhistory.ArtIndication;
import com.yuxin.art.modules.art.medicalhistory.service.ArtDiagnosService;
import com.yuxin.art.modules.art.medicalhistory.service.ArtIndicationService;
import com.yuxin.art.modules.art.medicalhistory.vo.*;
import com.yuxin.framework.mvc.controller.BaseController;
import com.yuxin.framework.mvc.vo.IdVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 诊断及指征 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-13
 */
@RestController
@RequestMapping(value = "/art/medicalhistory/artDiagnosIndication", produces = MediaType.APPLICATION_JSON_VALUE)
@Validated
@Api(tags = "诊断及指征")
@ApiSort(1)
public class ArtDiagnosIndicationController extends BaseController {
    @Autowired
    private ArtDiagnosService artDiagnosService;
    @Autowired
    private ArtIndicationService artIndicationService;

    /**
     * 新增女性诊断(首页)
     *
     * @param addArtDiagnosVOS
     * @return
     */
    @PostMapping(value = "/mCreateFemaleDiagnos", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "新增女性诊断(首页)", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 1)
    // @RequiresPermissions("art:medicalhistory:artDiagnos:mCreateFemaleDiagnos")
    public Result<Void> mCreateFemaleDiagnos(@RequestBody @Valid List<AddArtDiagnosVO> addArtDiagnosVOS) {
        addArtDiagnosVOS.forEach(addArtDiagnosVO -> {
            ArtDiagnos diagnos = new ArtDiagnos();
            BeanUtil.copyProperties(addArtDiagnosVO, diagnos);
            artDiagnosService.saveDiagnos(diagnos);
        });
        return Result.success();
    }

    /**
     * 修改女性诊断(首页)
     *
     * @param editArtDiagnosVOS
     * @return
     */
    @PostMapping(value = "/editFemaleDiagnos", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "修改女性诊断(首页)", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 2)
    // @RequiresPermissions("art:medicalhistory:artDiagnos:editFemaleDiagnos")
    public Result<Void> editFemaleDiagnos(@RequestBody @Valid List<EditArtDiagnosVO> editArtDiagnosVOS) {
        editArtDiagnosVOS.forEach(editArtDiagnosVO -> {
            ArtDiagnos diagnos = new ArtDiagnos();
            BeanUtil.copyProperties(editArtDiagnosVO, diagnos);
            artDiagnosService.saveDiagnos(diagnos);
        });
        return Result.success();
    }

    /**
     * 修订女性诊断(首页)
     *
     * @param reviseArtDiagnosVO
     * @return
     */
    @PostMapping(value = "/reviseFemaleDiagnos", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "修订女性诊断(首页)", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 3)
    // @RequiresPermissions("art:medicalhistory:artDiagnos:reviseFemaleDiagnos")
    public Result<IdVO> reviseFemaleDiagnos(@RequestBody @Validated ReviseArtDiagnosVO reviseArtDiagnosVO) {
        ArtDiagnos diagnos = new ArtDiagnos();
        BeanUtil.copyProperties(reviseArtDiagnosVO, diagnos);
        Long id = artDiagnosService.saveDiagnos(diagnos);
        return Result.success(new IdVO(id));
    }

    /**
     * 删除女性诊断(首页)
     *
     * @param id
     */
    @DeleteMapping("/deleteFemaleDiagnos")
    @ApiOperation(value = "删除女性诊断(首页)")
    @ApiImplicitParam(name = "id", value = "id", required = true, paramType = "query", dataType = "String")
    @ApiOperationSupport(order = 4)
    // @RequiresPermissions("art:medicalhistory:artDiagnos:deleteFemaleDiagnos")
    public Result<Void> deleteFemaleDiagnos(@NotNull(message = "id不能为空") Long id) {
        artDiagnosService.deleteDiagnos(id);
        return Result.success();
    }

    /**
     * 新增男性诊断(首页)
     *
     * @param addArtDiagnosVOS
     * @return
     */
    @PostMapping(value = "/mCreateMaleDiagnos", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "新增男性诊断(首页)", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 1)
    // @RequiresPermissions("art:medicalhistory:artDiagnos:mCreateMaleDiagnos")
    public Result<Void> mCreateMaleDiagnos(@RequestBody @Valid List<AddArtDiagnosVO> addArtDiagnosVOS) {
        addArtDiagnosVOS.forEach(addArtDiagnosVO -> {
            ArtDiagnos diagnos = new ArtDiagnos();
            BeanUtil.copyProperties(addArtDiagnosVO, diagnos);
            artDiagnosService.saveDiagnos(diagnos);
        });
        return Result.success();
    }

    /**
     * 修改男性诊断(首页)
     *
     * @param editArtDiagnosVOS
     * @return
     */
    @PostMapping(value = "/editMaleDiagnos", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "修改男性诊断(首页)", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 2)
    // @RequiresPermissions("art:medicalhistory:artDiagnos:editMaleDiagnos")
    public Result<Void> editMaleDiagnos(@RequestBody @Valid List<EditArtDiagnosVO> editArtDiagnosVOS) {
        editArtDiagnosVOS.forEach(editArtDiagnosVO -> {
            ArtDiagnos diagnos = new ArtDiagnos();
            BeanUtil.copyProperties(editArtDiagnosVO, diagnos);
            artDiagnosService.saveDiagnos(diagnos);
        });
        return Result.success();
    }

    /**
     * 修订男性诊断(首页)
     *
     * @param reviseArtDiagnosVO
     * @return
     */
    @PostMapping(value = "/reviseMaleDiagnos", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "修订男性诊断(首页)", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 3)
    // @RequiresPermissions("art:medicalhistory:artDiagnos:reviseMaleDiagnos")
    public Result<IdVO> reviseMaleDiagnos(@RequestBody @Validated ReviseArtDiagnosVO reviseArtDiagnosVO) {
        ArtDiagnos diagnos = new ArtDiagnos();
        BeanUtil.copyProperties(reviseArtDiagnosVO, diagnos);
        Long id = artDiagnosService.saveDiagnos(diagnos);
        return Result.success(new IdVO(id));
    }

    /**
     * s
     * 删除男性诊断(首页)
     *
     * @param id
     */
    @DeleteMapping("/deleteMaleDiagnos")
    @ApiOperation(value = "删除男性诊断(首页)")
    @ApiImplicitParam(name = "id", value = "id", required = true, paramType = "query", dataType = "String")
    @ApiOperationSupport(order = 4)
    // @RequiresPermissions("art:medicalhistory:artDiagnos:deleteMaleDiagnos")
    public Result<Void> deleteMaleDiagnos(@NotNull(message = "id不能为空") Long id) {
        artDiagnosService.deleteDiagnos(id);
        return Result.success();
    }

    /**
     * 新增指征
     *
     * @param addArtIndicationVO
     * @return
     */
    @PostMapping(value = "/createIndication", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "新增指征", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 5)
    // @RequiresPermissions("art:medicalhistory:artDiagnosIndication:createIndication")
    public Result<IdVO> createIndication(@RequestBody AddArtIndicationVO addArtIndicationVO) {
        ArtIndication artIndication = new ArtIndication();
        BeanUtil.copyProperties(addArtIndicationVO, artIndication);
        Long id = artIndicationService.saveIndication(artIndication);
        return Result.success(new IdVO(id));
    }

    /**
     * 编辑指征
     *
     * @param editArtDiagnosIndicationVO
     */
    @PutMapping(value = "/editIndication", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "编辑指征", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 6)
    // @RequiresPermissions("art:medicalhistory:artDiagnosIndication:editIndication")
    public Result<Void> editIndication(@RequestBody EditArtIndicationVO editArtDiagnosIndicationVO) {
        ArtIndication artIndication = new ArtIndication();
        BeanUtil.copyProperties(editArtDiagnosIndicationVO, artIndication);
        artIndicationService.saveIndication(artIndication);
        return Result.success();
    }

    /**
     * 根据周期查询诊断及指针信息
     *
     * @param cycleId
     * @return
     */
    @GetMapping("/getByCycleId")
    @ApiOperation(value = "根据周期查询诊断及指针信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "cycleId", value = "周期Id", required = true, paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "isShow", value = "是否显示", paramType = "query", dataType = "Integer")})
    @ApiOperationSupport(order = 8)
    // @RequiresPermissions("art:medicalhistory:artDiagnosIndication:getByCycleId")
    public Result<ArtDiagnosIndicationVO> getByCycleId(@NotNull(message = "cycleId不能为空") Long cycleId, Integer isShow) {
        List<ArtDiagnos> artDiagnos = artDiagnosService.getByCycleId(cycleId, isShow);
        ArtIndication artIndication = artIndicationService.getByCycleId(cycleId);
        ArtDiagnosIndicationVO artDiagnosIndicationVO = new ArtDiagnosIndicationVO();
        artDiagnosIndicationVO.setArtDiagnos(artDiagnos);
        artDiagnosIndicationVO.setIndication(artIndication);
        return Result.success(artDiagnosIndicationVO);
    }

    /**
     * 根据患者ID和周期ID查询诊断集合
     *
     * @param cycleId
     * @param patientId
     * @return
     */
    @GetMapping("/getListByCycleIdAndPatientId")
    @ApiOperation(value = "根据患者ID和周期ID查询诊断集合")
    @ApiImplicitParams({@ApiImplicitParam(name = "cycleId", value = "周期ID", required = true, paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "patientId", value = "患者ID", required = true, paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "isShow", value = "是否显示", paramType = "query", dataType = "Integer")})
    @ApiOperationSupport(order = 9)
    // @RequiresPermissions("art:medicalhistory:artDiagnosIndication:getListByCycleIdAndPatientId")
    public Result<List<ArtDiagnos>> getListByCycleIdAndPatientId(@NotNull(message = "周期ID不能为空") Long cycleId,
        @NotNull(message = "患者ID不能为空") Long patientId, Integer isShow) {
        List<ArtDiagnos> list = artDiagnosService.getListByCycleIdAndPatientId(cycleId, patientId, isShow);
        return Result.success(list);
    }

    /**
     * 根据周期ID查询指征
     *
     * @param cycleId
     * @return
     */
    @GetMapping("/getArtIndicationByCycleId")
    @ApiOperation(value = "根据周期ID查询指征")
    @ApiImplicitParam(name = "cycleId", value = "周期ID", required = true, paramType = "query", dataType = "String")
    @ApiOperationSupport(order = 10)
    // @RequiresPermissions("art:medicalhistory:artDiagnosIndication:getArtIndicationByCycleId")
    public Result<ArtIndication> getArtIndicationByCycleId(@NotNull(message = "周期ID不能为空") Long cycleId) {
        ArtIndication artIndication = artIndicationService.getByCycleId(cycleId);
        return Result.success(artIndication);
    }

    /**
     * 根据患者ID和周期ID查询既往诊断
     *
     * @param patientId
     * @return
     */
    @GetMapping("/getPastDiagnosByCycleIdAndPatientId")
    @ApiOperation(value = "根据患者ID和周期ID查询既往诊断")
    @ApiImplicitParams({@ApiImplicitParam(name = "cycleId", value = "周期ID", required = true, paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "patientId", value = "患者ID", required = true, paramType = "query", dataType = "String")})
    @ApiOperationSupport(order = 11)
    // @RequiresPermissions("art:medicalhistory:artDiagnos:getPastDiagnosByCycleIdAndPatientId")
    public Result<List<PastDiagnosVO>> getPastDiagnosByCycleIdAndPatientId(@NotNull(message = "周期ID不能为空") Long cycleId,
        @NotNull(message = "患者ID不能为空") Long patientId, Integer isShow) {
        List<PastDiagnosVO> list = artDiagnosService.getPastDiagnosByCycleIdAndPatientId(cycleId, patientId, isShow);
        return Result.success(list);
    }

}
