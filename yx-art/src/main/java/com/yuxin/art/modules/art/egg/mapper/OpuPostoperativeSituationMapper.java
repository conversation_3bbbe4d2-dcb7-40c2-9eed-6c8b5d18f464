/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:OpuPostoperativeSituationMapper.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.art.egg.mapper;

import com.yuxin.art.domain.art.egg.OpuPostoperativeSituation;
import com.yuxin.art.modules.art.egg.vo.OpuPostoperativeSituationVO;
import com.yuxin.framework.mvc.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 取卵术后情况 Mapper
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-01
 */
@Mapper
public interface OpuPostoperativeSituationMapper extends BaseMapper<OpuPostoperativeSituation> {
    /**
     * 通过记录ID关联获取术后情况数据
     *
     * @param recordId
     * @return
     */
    OpuPostoperativeSituationVO getByRecordId(Long recordId);
}
