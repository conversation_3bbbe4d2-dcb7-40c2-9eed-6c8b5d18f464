/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称：yx-art
 * 文件名称:BizModuleEnum.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.trace.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务模块枚举
 * <p>
 * 定义需要进行数据留痕的业务模块，包括医疗病史、实验室操作、手术记录等17个核心业务模块。
 * 每个模块包含表名、中文描述、对应的Freemarker模板文件名等信息。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum BizModuleEnum {

    /**
     * 女方病史
     */
    FEMALE_HISTORY("art_female_history", "女方病史", "FemaleHistory.ftl", "com.yuxin.art.domain.art.medicalhistory.ArtFemaleHistory"),
    /**
     * 男方病史
     */
    MALE_HISTORY("art_male_history", "男方病史", "MaleHistory.ftl", "com.yuxin.art.domain.art.medicalhistory.ArtMaleHistory"),

    /**
     * 取卵记录
     */
    EGG_SURGERY("egg_surgery", "取卵记录", "Opu.ftl", "com.yuxin.art.domain.art.surgery.EggSurgery"),
    /**
     * 捡卵记录
     */
    PICK_EGG("art_ovum", "捡卵记录", "PickEgg.ftl", "com.yuxin.art.domain.art.ovum.ArtOvum"),
    /**
     * 精子处理记录
     */
    SPERM_PROCESSING("art_sperm_processing", "精液处理记录", "SpermProcess.ftl", "com.yuxin.art.domain.art.sperm.ArtSpermProcessing"),
    /**
     * 早胚观察记录
     */
    EARLY_EMBRYO_OB("art_embryo_ob_early", "早胚观察记录", "EarlyOb.ftl", "com.yuxin.art.domain.art.embryoobservation.ArtEmbryoObEarly"),
    /**
     * 中胚观察记录
     */
    MID_EMBRYO_OB("art_embryo_ob_mid", "中胚观察记录", "EarlyOb.ftl", "com.yuxin.art.domain.art.embryoobservation.ArtEmbryoObMid"),
    /**
     * 囊胚观察记录
     */
    LATER_EMBRYO_OB("art_embryo_ob_later", "囊胚观察记录", "LaterOb.ftl", "com.yuxin.art.domain.art.embryoobservation.ArtEmbryoObLater"),

    /**
     * 胚胎冷冻解冻记录
     */
    EMBRYO_FREEZING("ft_embryo_recovery_detail", "胚胎冷解冻记录", "EmbryoFreezing.ftl", "com.yuxin.art.domain.art.freezing.FtEmbryoRecoveryDetail"),
    /**
     * 精子冷冻解冻记录
     */
    SPERM_FREEZING("ft_sperm_recovery_detail", "精子冷解冻记录", "SpermFreezing.ftl", "com.yuxin.art.domain.art.freezing.FtSpermRecoveryDetail"),

    /**
     * 移植记录
     */
    EMBRYO_TRANSFER("art_embryo_transfer", "移植记录", "Et.ftl", "com.yuxin.art.domain.art.transplant.ArtEmbryoTransfer"),
    /**
     * 人工授精记录
     */
    ARTIFICIAL_INSEMINATION("art_artificial_insemination", "人工授精记录", "Ai.ftl", "com.yuxin.art.domain.art.ai.ArtArtificialInsemination"),
    /**
     * 其他手术记录
     */
    SURGERY_SPECIAL("surgery_special_order", "其他手术记录", "Surgery.ftl", "com.yuxin.art.domain.art.surgery.SurgerySpecialOrder"),

    /**
     * 术前讨论
     */
    WEEKLY_REVIEW("art_weekly_review_process", "术前讨论", "WeeklyReviewProcess.ftl", "com.yuxin.art.domain.art.review.ArtWeeklyReviewProcess"),
    /**
     * 病历讨论
     */
    MEDICAL_DISCUSSION("art_medical_discussion", "病历讨论", "MedicalDiscussion.ftl", "com.yuxin.art.domain.art.discussion.ArtMedicalDiscussion"),
    /**
     * 疑难病例讨论
     */
    DIFFICULT_CASE("art_difficult_case_discussion", "疑难病例讨论", "DifficultCase.ftl",
        "com.yuxin.art.domain.art.discussion.ArtDifficultCaseDiscussion"),
    /**
     * 医患沟通
     */
    PATIENT_COMMUNICATION("art_patient_communication", "医患沟通", "geneticCounseling.ftl",
        "com.yuxin.art.domain.art.communication.ArtPatientCommunication"),

    /**
     * OHSS并发症随访
     */
    OHSS_FOLLOWUP("follow_up_ohss", "OHSS记录", "FollowUpOhss.ftl", "com.yuxin.art.domain.art.followup.FollowUpOhss");

    /**
     * 数据库表名
     */
    private final String tableName;

    /**
     * 业务模块中文名称
     */
    private final String displayName;

    /**
     * 对应的Freemarker模板文件名
     */
    private final String templateName;

    /**
     * 对应的实体类全限定名
     */
    private final String entityClassName;

    /**
     * 构造函数
     */
    BizModuleEnum(String tableName, String displayName, String templateName, String entityClassName) {
        this.tableName = tableName;
        this.displayName = displayName;
        this.templateName = templateName;
        this.entityClassName = entityClassName;
    }

    /**
     * 获取表名
     */
    public String getTableName() {
        return tableName;
    }

    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取模板名称
     */
    public String getTemplateName() {
        return templateName;
    }

    /**
     * 获取实体类名
     */
    public String getEntityClassName() {
        return entityClassName;
    }

    /**
     * 根据表名获取业务模块
     *
     * @param tableName 表名
     * @return 业务模块枚举，如果未找到返回null
     */
    public static BizModuleEnum getByTableName(String tableName) {
        if (tableName == null) {
            return null;
        }

        for (BizModuleEnum module : values()) {
            if (module.getTableName().equals(tableName)) {
                return module;
            }
        }
        return null;
    }

    /**
     * 根据实体类名获取业务模块
     *
     * @param entityClassName 实体类全限定名
     * @return 业务模块枚举，如果未找到返回null
     */
    public static BizModuleEnum getByEntityClassName(String entityClassName) {
        if (entityClassName == null) {
            return null;
        }

        for (BizModuleEnum module : values()) {
            if (module.getEntityClassName().equals(entityClassName)) {
                return module;
            }
        }
        return null;
    }

    /**
     * 获取Word模板文件名（将.ftl替换为.docx）
     *
     * @return Word模板文件名
     */
    public String getWordTemplateName() {
        return templateName.replace(".ftl", ".docx");
    }

    /**
     * 判断是否为医疗病史类模块
     *
     * @return true表示是医疗病史类模块
     */
    public boolean isMedicalHistoryModule() {
        return this == FEMALE_HISTORY || this == MALE_HISTORY;
    }

    /**
     * 判断是否为实验室操作类模块
     *
     * @return true表示是实验室操作类模块
     */
    public boolean isLabOperationModule() {
        return this == EGG_SURGERY || this == PICK_EGG || this == SPERM_PROCESSING || this == EARLY_EMBRYO_OB || this == MID_EMBRYO_OB || this == LATER_EMBRYO_OB;
    }

    /**
     * 判断是否为冷冻解冻类模块
     *
     * @return true表示是冷冻解冻类模块
     */
    public boolean isFreezingModule() {
        return this == EMBRYO_FREEZING || this == SPERM_FREEZING;
    }

    /**
     * 判断是否为手术操作类模块
     *
     * @return true表示是手术操作类模块
     */
    public boolean isSurgeryModule() {
        return this == EMBRYO_TRANSFER || this == ARTIFICIAL_INSEMINATION || this == SURGERY_SPECIAL;
    }

    /**
     * 判断是否为讨论沟通类模块
     *
     * @return true表示是讨论沟通类模块
     */
    public boolean isDiscussionModule() {
        return this == WEEKLY_REVIEW || this == MEDICAL_DISCUSSION || this == DIFFICULT_CASE || this == PATIENT_COMMUNICATION;
    }

    /**
     * 判断是否为并发症管理类模块
     *
     * @return true表示是并发症管理类模块
     */
    public boolean isComplicationModule() {
        return this == OHSS_FOLLOWUP;
    }
}
