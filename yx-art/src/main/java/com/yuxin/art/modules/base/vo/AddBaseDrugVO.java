/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:AddBaseDrugVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.base.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 药品
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
@Data
@NoArgsConstructor
@ApiModel(value = "AddBaseDrugVO", description = "新增药品")
public class AddBaseDrugVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 药品名称
     */
    @ApiModelProperty(value = "药品名称（50）", required = true)
    @NotBlank(message = "药品名称不能为空")
    @Length(max = 50, message = "药品名称不能超过50个字符")
    private String name;

    /**
     * 药品规格/数量
     */
    @ApiModelProperty(value = "药品规格/数量（8,2）", required = true)
    @NotNull(message = "药品规格/数量不能为空")
    @Digits(integer = 8, fraction = 2, message = "药品规格/数量只能包含8位整数,2位小数")
    private BigDecimal specsNum;
    /**
     * 药品规格/单位
     */
    @ApiModelProperty(value = "药品规格/单位（50）", required = true)
    @NotBlank(message = "药品规格/单位不能为空")
    @Length(max = 50, message = "药品规格/单位不能超过50个字符")
    private String specsUnit;
    /**
     * 药品规格/规格
     */
    @ApiModelProperty(value = "药品规格/规格（50）", required = true)
    @NotBlank(message = "药品规格/规格不能为空")
    @Length(max = 50, message = "药品规格/规格不能超过50个字符")
    private String specs;

    /**
     * 用药方法
     */
    @ApiModelProperty(value = "用药方法(数据字典.json)（200）", required = true)
    @NotBlank(message = "用药方法不能为空")
    @Length(max = 200, message = "用药方法不能超过200个字符")
    private String medicationMethod;

    /**
     * 默认剂量
     */
    @ApiModelProperty(value = "默认剂量（10,2）", required = true)
    @DecimalMin(value = "0.01", message = "默认剂量不能小于等于0")
    @NotNull(message = "默认剂量不能为空")
    @Digits(integer = 10, fraction = 2, message = "默认剂量只能包含8位整数,2位小数")
    private BigDecimal defaultDosage;

    /**
     * 厂商 ID
     */
    @ApiModelProperty(value = "厂商 ID（32）", required = false)
    private Long manufacturerId;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序（不能小于1）", required = true)
    @Range(min = 1, message = "排序不能小于1")
    @NotNull(message = "排序不能为空")
    private Integer sort;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注（200）", required = false)
    @Length(max = 200, message = "备注不能超过200个字符")
    private String remark;

    /** 药品类型 */
    @ApiModelProperty(
        value = "药品类型(0.长效GnRH-a、1.短效GnRH-a、2.GnRH-anta、3.u-FSH、4.r-FSH 、5.HMG、6.口服促排、7.黄体酮制剂、8.生长激素、9.HCG、10.雌激素、11.OC、12.抗菌药物、13.其他西药、14.中成药、15.rHcg）",
        required = true)
    @NotNull(message = "药品类型不能为空")
    @Range(min = 0, max = 21, message = "无法识别药品类型")
    private Integer drugType;

    /**
     * 用药频率
     */
    @ApiModelProperty(value = "用药频率（数据字典.json）（200）")
    @Length(max = 200, message = "用药频率不能超过200个字符")
    private String frequency;

    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位（50）", required = true)
    @NotBlank(message = "计量单位不能为空")
    @Length(max = 50, message = "计量单位不能超过50个字符")
    private String unit;

    /**
     * 默认给药时间
     */
    @ApiModelProperty(value = "默认给药时间", required = false)
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "HH:mm")
    private Date administrationTime;

    /**
     * 品牌ID
     */
    @ApiModelProperty(value = "品牌ID（32）", required = false)
    private Long brandId;

    /**
     * 是否可用.0:否;1:是
     */
    @ApiModelProperty(value = "是否可用.0:否;1:是", required = true)
    @NotNull(message = "是否可用不能为空")
    @Range(min = 0, max = 1, message = "不匹配资源")
    private Integer status;

    /**
     * 快速定位码
     */
    @ApiModelProperty(value = "快速定位码（50）", required = true)
    @NotBlank(message = "快速定位码不能为空")
    @Length(max = 50, message = "快速定位码不能超过50个字符")
    private String alignmentCode;

    /**
     * 全量
     */
    @ApiModelProperty(value = "全量(10,2)", required = false)
    @DecimalMin(value = "0.01", message = "全量不能小于等于0")
    @Digits(integer = 10, fraction = 2, message = "全量只能包含8位整数,2位小数")
    private BigDecimal fullDose;

    /**
     * 半量
     */
    @ApiModelProperty(value = "半量(10,2)", required = false)
    @DecimalMin(value = "0.01", message = "半量不能小于等于0")
    @Digits(integer = 10, fraction = 2, message = "半量只能包含8位整数,2位小数")
    private BigDecimal halfDose;

    /**
     * 小剂量
     */
    @ApiModelProperty(value = "小剂量(10,2)", required = false)
    @DecimalMin(value = "0.01", message = "小剂量不能小于等于0")
    @Digits(integer = 10, fraction = 2, message = "小剂量只能包含8位整数,2位小数")
    private BigDecimal smallDose;

    /**
     * 医嘱只打印规格 0：否，1：是
     */
    @ApiModelProperty(value = "医嘱只打印规格 0：否，1：是")
    @Range(min = 0, max = 1, message = "无法识别的医嘱只打印规格")
    private Integer onlyPrintSpec;

    /**
     * 加入促排药 0：否，1：是
     */
    @ApiModelProperty(value = "加入促排药 0：否，1：是")
    @Range(min = 0, max = 1, message = "无法识别的加入促排药")
    private Integer isCoh;
}
