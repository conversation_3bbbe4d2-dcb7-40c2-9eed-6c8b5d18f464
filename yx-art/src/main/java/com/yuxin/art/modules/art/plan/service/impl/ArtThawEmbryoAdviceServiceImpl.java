/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ArtThawEmbryoAdviceServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.art.plan.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuxin.art.domain.art.plan.ArtThawEmbryoAdvice;
import com.yuxin.art.modules.art.plan.mapper.ArtThawEmbryoAdviceMapper;
import com.yuxin.art.modules.art.plan.service.ArtThawEmbryoAdviceService;
import com.yuxin.domain.AuthInfo;
import com.yuxin.framework.mvc.mapper.BaseMapper;
import com.yuxin.framework.mvc.service.impl.BaseServiceImpl;
import com.yuxin.security.util.AuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * Art计划-解冻建议 ServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-24
 */
@Service
public class ArtThawEmbryoAdviceServiceImpl extends BaseServiceImpl<ArtThawEmbryoAdvice> implements ArtThawEmbryoAdviceService {
    @Autowired
    private ArtThawEmbryoAdviceMapper mapper;

    @Override
    protected BaseMapper<ArtThawEmbryoAdvice> getMapper() {
        return mapper;
    }

    @Override
    public List<ArtThawEmbryoAdvice> getListByThawEmbryoPlanId(Long thawEmbryoPlanId) {
        return super.getList(Wrappers.lambdaQuery(ArtThawEmbryoAdvice.class).eq(ArtThawEmbryoAdvice::getPlanThawEmbryoId, thawEmbryoPlanId)
            .orderByAsc(ArtThawEmbryoAdvice::getRecorderDate));
    }

    @Override
    public ArtThawEmbryoAdvice getAdvice(Long id, Long thawEmbryoPlanId) {
        ArtThawEmbryoAdvice entity = null;
        if (id != null) {
            entity = mapper.selectById(id);
        } else {
            if (thawEmbryoPlanId != null) {
                List<ArtThawEmbryoAdvice> list = mapper.selectList(
                    Wrappers.lambdaQuery(ArtThawEmbryoAdvice.class).eq(ArtThawEmbryoAdvice::getPlanThawEmbryoId, thawEmbryoPlanId)
                        .orderByDesc(ArtThawEmbryoAdvice::getRecorderDate));
                if (CollUtil.isNotEmpty(list)) {
                    entity = list.get(0);
                }
            }
        }
        return entity;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sUpdate(ArtThawEmbryoAdvice entity) {
        AuthInfo curInfo = AuthUtil.getCurrentAuthInfo();
        entity.setRecorderDate(new Date());
        entity.setRecorderId(curInfo.getUserId());
        entity.setRecorderName(curInfo.getName());
        super.update(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long sInsert(ArtThawEmbryoAdvice entity) {
        AuthInfo curInfo = AuthUtil.getCurrentAuthInfo();
        entity.setRecorderDate(new Date());
        entity.setRecorderId(curInfo.getUserId());
        entity.setRecorderName(curInfo.getName());
        return super.insert(entity);
    }
}
