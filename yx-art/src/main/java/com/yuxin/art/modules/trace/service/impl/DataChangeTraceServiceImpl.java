/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称：yx-art
 * 文件名称:DataChangeTraceServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.trace.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.yuxin.art.modules.trace.constant.TraceConstants;
import com.yuxin.art.modules.trace.dto.DataChangeTraceDTO;
import com.yuxin.art.modules.trace.entity.DataChangeTrace;
import com.yuxin.art.modules.trace.enums.BizModuleEnum;
import com.yuxin.art.modules.trace.service.DataChangeTraceService;
import com.yuxin.art.modules.trace.util.TraceDataUtils;
import com.yuxin.search.entity.ElasticEntity;
import com.yuxin.search.service.ElasticSearchService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 数据变更留痕服务实现类
 * <p>
 * 实现业务数据变更的留痕记录功能，通过RabbitMQ异步处理，确保不影响主业务性能。
 * 支持数据压缩存储，减少ES存储空间占用。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class DataChangeTraceServiceImpl implements DataChangeTraceService {

    @Autowired
    private ElasticSearchService elasticSearchService;

    @Override
    public void recordDataChange(DataChangeTraceDTO dataChangeTraceDTO) {
        log.debug("开始处理队列中的留痕数据：bizId={}, bizModule={}, operation={}", dataChangeTraceDTO.getBizId(), dataChangeTraceDTO.getBizModule(),
            dataChangeTraceDTO.getOperationType());

        try {
            // 将DTO转换为Entity
            DataChangeTrace traceEntity = convertDtoToEntity(dataChangeTraceDTO);

            // 1. 进行数据比较和过滤
            if (shouldSkipRecord(traceEntity)) {
                log.debug("数据未发生变化，跳过存储：bizId={}, bizModule={}, operation={}", dataChangeTraceDTO.getBizId(),
                    dataChangeTraceDTO.getBizModule(), dataChangeTraceDTO.getOperationType());
                return;
            }

            // 2. 数据发生变化，进行存储
            storeTraceData(traceEntity);

            log.debug("留痕数据处理完成：bizId={}, bizModule={}, operation={}", dataChangeTraceDTO.getBizId(), dataChangeTraceDTO.getBizModule(),
                dataChangeTraceDTO.getOperationType());

        } catch (Exception e) {
            log.error("处理队列留痕数据失败：bizId={}, bizModule={}, operation={}", dataChangeTraceDTO.getBizId(), dataChangeTraceDTO.getBizModule(),
                dataChangeTraceDTO.getOperationType(), e);
        }
    }

    /**
     * 判断是否应该跳过记录（数据未发生变化）
     *
     * @param rawTraceEntity 原始留痕实体
     * @return true表示应该跳过记录，false表示需要记录
     */
    private boolean shouldSkipRecord(DataChangeTrace rawTraceEntity) {
        try {
            // 对于删除操作，总是记录
            if (TraceConstants.OperationType.DELETE.equals(rawTraceEntity.getOperationType())) {
                log.debug("删除操作，不跳过记录");
                return false;
            }

            // 获取业务模块枚举
            BizModuleEnum bizModuleEnum = getBizModuleEnum(rawTraceEntity.getBizModule());
            if (bizModuleEnum == null) {
                log.debug("无法识别业务模块，不跳过记录：{}", rawTraceEntity.getBizModule());
                return false;
            }

            // 获取最新的数据快照
            DataChangeTrace dataChangeTrace = getLatestTraceRecord(rawTraceEntity.getBizId(), bizModuleEnum);
            if (dataChangeTrace == null) {
                log.debug("未找到历史数据，不跳过记录");
                return false;
            }

            // 将最新快照转换为JSON进行比较
            String latestDataJson = TraceDataUtils.convertToJsonString(dataChangeTrace.getBizData());
            String currentDataJson = rawTraceEntity.getBizData();

            // 比较数据是否相同
            boolean dataEquals = compareDataJson(currentDataJson, latestDataJson);

            if (dataEquals) {
                log.debug("数据相同，跳过记录：bizId={}, bizModule={}", rawTraceEntity.getBizId(), rawTraceEntity.getBizModule());
                return true;
            } else {
                log.debug("数据不同，需要记录：bizId={}, bizModule={}", rawTraceEntity.getBizId(), rawTraceEntity.getBizModule());
                return false;
            }

        } catch (Exception e) {
            log.warn("数据比较失败，不跳过记录：bizId={}, bizModule={}, error={}", rawTraceEntity.getBizId(), rawTraceEntity.getBizModule(),
                e.getMessage());
            return false;
        }
    }

    /**
     * 获取业务模块枚举
     */
    private BizModuleEnum getBizModuleEnum(String bizModuleName) {
        try {
            return BizModuleEnum.valueOf(bizModuleName);
        } catch (Exception e) {
            log.debug("无法转换业务模块枚举：{}", bizModuleName);
            return null;
        }
    }

    /**
     * 比较两个JSON数据是否相同
     *
     * @param currentDataJson 当前数据JSON
     * @param latestDataJson  最新数据JSON
     * @return true表示数据相同，false表示数据不同
     */
    private boolean compareDataJson(String currentDataJson, String latestDataJson) {
        try {
            // 处理空值情况
            if (StrUtil.isBlank(currentDataJson) && StrUtil.isBlank(latestDataJson)) {
                return true;
            }
            if (StrUtil.isBlank(currentDataJson) || StrUtil.isBlank(latestDataJson)) {
                return false;
            }

            // 标准化JSON字符串（移除空格、换行等）
            String normalizedCurrent = normalizeJsonString(currentDataJson);
            String normalizedLatest = normalizeJsonString(latestDataJson);

            // 直接比较字符串
            boolean equals = normalizedCurrent.equals(normalizedLatest);

            if (!equals) {
                log.debug("数据不同，当前数据长度：{}，历史数据长度：{}", normalizedCurrent.length(), normalizedLatest.length());
            }

            return equals;

        } catch (Exception e) {
            log.debug("JSON数据比较失败：{}", e.getMessage());
            return false;
        }
    }

    /**
     * 标准化JSON字符串
     *
     * @param jsonString 原始JSON字符串
     * @return 标准化后的JSON字符串
     */
    private String normalizeJsonString(String jsonString) {
        if (StrUtil.isBlank(jsonString)) {
            return "";
        }

        try {
            // 移除多余的空格、换行符等
            return jsonString.replaceAll("\\s+", "").trim();
        } catch (Exception e) {
            log.debug("JSON标准化失败：{}", e.getMessage());
            return jsonString.trim();
        }
    }

    /**
     * 存储留痕数据到ElasticSearch
     *
     * @param rawTraceEntity 原始留痕实体
     */
    private void storeTraceData(DataChangeTrace rawTraceEntity) {
        try {
            // 构建ES实体
            ElasticEntity elasticEntity = new ElasticEntity();
            elasticEntity.setId(rawTraceEntity.getId());
            elasticEntity.setIndexName(TraceConstants.TRACE_INDEX_NAME);

            // 将实体转换为Map
            Map<String, Object> dataMap = BeanUtil.beanToMap(rawTraceEntity);
            elasticEntity.setData(dataMap);

            // 存储到ElasticSearch
            elasticSearchService.insertOrUpdateOne(elasticEntity);

            log.debug("留痕数据已存储到ES：id={}, bizId={}, bizModule={}", rawTraceEntity.getId(), rawTraceEntity.getBizId(),
                rawTraceEntity.getBizModule());

        } catch (Exception e) {
            log.error("存储留痕数据到ES失败：id={}", rawTraceEntity.getId(), e);
            throw e;
        }
    }

    @Override
    public List<DataChangeTrace> getTraceRecords(Long bizId, BizModuleEnum bizModuleEnum) {
        try {
            // 构建查询条件
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            queryBuilder.must(QueryBuilders.termQuery("bizId", bizId));
            queryBuilder.must(QueryBuilders.termQuery("bizModule", bizModuleEnum.name()));

            // 构建查询
            SearchSourceBuilder builder = new SearchSourceBuilder();
            builder.query(queryBuilder);
            builder.sort("operationTime", SortOrder.DESC);
            builder.size(1000); // 限制最大返回1000条记录

            // 调用ElasticSearch查询
            Map<String, Object> searchResult = elasticSearchService.searchPage(TraceConstants.TRACE_INDEX_NAME, builder, 0, 1000);

            // 提取查询结果
            @SuppressWarnings("unchecked") List<Map<String, Object>> records =
                (List<Map<String, Object>>)searchResult.getOrDefault("records", new ArrayList<>());

            // 转换为DataChangeTrace对象
            return convertToDataChangeTraceList(records);

        } catch (Exception e) {
            log.error("查询留痕记录失败：bizId={}, bizModule={}", bizId, bizModuleEnum, e);
            return new ArrayList<>();
        }
    }

    @Override
    public DataChangeTrace getLatestTraceRecord(Long bizId, BizModuleEnum bizModuleEnum) {
        try {
            // 构建查询条件
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            queryBuilder.must(QueryBuilders.termQuery("bizId", bizId));
            queryBuilder.must(QueryBuilders.termQuery("bizModule", bizModuleEnum.name()));

            // 构建查询，只查询最新的一条记录
            SearchSourceBuilder builder = new SearchSourceBuilder();
            builder.query(queryBuilder);
            builder.sort("operationTime", SortOrder.DESC);
            builder.size(1); // 只返回最新的一条记录

            // 调用ElasticSearch查询
            Map<String, Object> searchResult = elasticSearchService.searchPage(TraceConstants.TRACE_INDEX_NAME, builder, 0, 1);

            // 提取查询结果
            @SuppressWarnings("unchecked") List<Map<String, Object>> records =
                (List<Map<String, Object>>)searchResult.getOrDefault("records", new ArrayList<>());

            // 转换为DataChangeTrace对象
            List<DataChangeTrace> traceList = convertToDataChangeTraceList(records);
            return traceList.isEmpty() ? null : traceList.get(0);

        } catch (Exception e) {
            log.error("获取最新留痕记录失败：bizId={}, bizModule={}", bizId, bizModuleEnum, e);
            return null;
        }
    }

    /**
     * 转换Map列表为DataChangeTrace对象列表
     */
    private List<DataChangeTrace> convertToDataChangeTraceList(List<Map<String, Object>> resultList) {
        if (resultList == null || resultList.isEmpty()) {
            return new ArrayList<>();
        }

        List<DataChangeTrace> traceList = new ArrayList<>();
        for (Map<String, Object> dataMap : resultList) {
            try {
                DataChangeTrace trace = convertMapToDataChangeTrace(dataMap);
                if (trace != null) {
                    traceList.add(trace);
                }
            } catch (Exception e) {
                log.warn("转换DataChangeTrace对象失败：{}", e.getMessage());
            }
        }

        return traceList;
    }

    /**
     * 转换Map为DataChangeTrace对象
     */
    private DataChangeTrace convertMapToDataChangeTrace(Map<String, Object> dataMap) {
        try {
            DataChangeTrace trace = new DataChangeTrace();

            trace.setId((String)dataMap.get("id"));
            trace.setBizId(getLongValue(dataMap, "bizId"));
            trace.setBizTableName((String)dataMap.get("bizTableName"));
            trace.setBizModule((String)dataMap.get("bizModule"));
            trace.setOperationType((String)dataMap.get("operationType"));
            trace.setOperationTime(getDateValue(dataMap, "operationTime"));
            trace.setCreateTime(getDateValue(dataMap, "createTime"));
            trace.setOperatorId(getLongValue(dataMap, "operatorId"));
            trace.setOperatorName((String)dataMap.get("operatorName"));
            trace.setOperationClientIp((String)dataMap.get("operationClientIp"));
            trace.setBizData((String)dataMap.get("bizData"));

            return trace;

        } catch (Exception e) {
            log.error("转换Map为DataChangeTrace失败", e);
            return null;
        }
    }

    /**
     * 安全获取Long值
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Long) {
            return (Long)value;
        }
        if (value instanceof Number) {
            return ((Number)value).longValue();
        }
        try {
            return Long.valueOf(value.toString());
        } catch (NumberFormatException e) {
            log.debug("转换Long值失败：key={}, value={}", key, value);
            return null;
        }
    }

    /**
     * 安全获取Date值
     */
    private Date getDateValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Date) {
            return (Date)value;
        }
        if (value instanceof Long) {
            return new Date((Long)value);
        }
        try {
            return new Date(Long.parseLong(value.toString()));
        } catch (NumberFormatException e) {
            log.debug("转换Date值失败：key={}, value={}", key, value);
            return null;
        }
    }

    /**
     * 将DTO转换为Entity
     */
    private DataChangeTrace convertDtoToEntity(DataChangeTraceDTO dto) {
        DataChangeTrace entity = new DataChangeTrace();
        BeanUtil.copyProperties(dto, entity);
        return entity;
    }

}
