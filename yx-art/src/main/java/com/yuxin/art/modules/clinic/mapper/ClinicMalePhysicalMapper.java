/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ClinicMalePhysicalMapper.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.clinic.mapper;

import com.yuxin.art.domain.clinic.ClinicMalePhysical;
import com.yuxin.art.modules.art.medicalhistory.vo.MalePhysicalDetailVO;
import com.yuxin.art.modules.art.medicalhistory.vo.QueryUniquenessVO;
import com.yuxin.framework.mvc.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 男患者门诊体格检查 Mapper
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-11
 */
@Mapper
public interface ClinicMalePhysicalMapper extends BaseMapper<ClinicMalePhysical> {
    /**
     * 根据唯一条件查询体格检查详情
     *
     * @param params
     */
    MalePhysicalDetailVO getMalePhysicalDetail(QueryUniquenessVO params);

}
