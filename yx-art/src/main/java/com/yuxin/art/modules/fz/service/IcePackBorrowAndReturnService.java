/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:IcePackBorrowAndReturnService.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */

package com.yuxin.art.modules.fz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuxin.art.domain.fz.IcePackBorrowAndReturn;
import com.yuxin.framework.mvc.service.BaseService;
import com.yuxin.framework.mvc.vo.PageParams;

import java.util.List;

/**
 * <p>
 * 冰袋借还 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-13
 */
public interface IcePackBorrowAndReturnService extends BaseService<IcePackBorrowAndReturn> {
    /**
     * 获取冰袋借还列表
     *
     * @return
     */
    IPage<IcePackBorrowAndReturn> getBusinessList(PageParams page);

    /**
     * 根据病人id查询借还记录
     *
     * @param patientId
     * @return
     */

    List<IcePackBorrowAndReturn> getPatientId(Long patientId);
}
