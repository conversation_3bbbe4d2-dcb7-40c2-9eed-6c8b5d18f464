/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:OpuPostoperativeFallService.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */

package com.yuxin.art.modules.art.egg.service;

import com.yuxin.art.domain.art.egg.OpuPostoperativeFall;
import com.yuxin.art.modules.art.egg.vo.EditOpuPostoperativeFallVO;
import com.yuxin.framework.mvc.service.BaseService;

/**
 * <p>
 * 取卵术后跌倒评分 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-18
 */
public interface OpuPostoperativeFallService extends BaseService<OpuPostoperativeFall> {

    /**
     * 更新
     *
     * @param vo
     */
    void edit(EditOpuPostoperativeFallVO vo);
}
