/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:SurgeryDiagnosticCurettageServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.surgery.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuxin.api.ResultEnum;
import com.yuxin.art.cache.base.BaseDataPrintCache;
import com.yuxin.art.cache.config.ConfigRuleCache;
import com.yuxin.art.constant.KArtConstants;
import com.yuxin.art.domain.art.course.ArtProgressNote;
import com.yuxin.art.domain.art.cycle.ArtCycle;
import com.yuxin.art.domain.config.ConfigRule;
import com.yuxin.art.domain.patient.PatientInfo;
import com.yuxin.art.domain.surgery.SurgeryDiagnosticCurettage;
import com.yuxin.art.domain.surgery.SurgerySpecialOrder;
import com.yuxin.art.domain.sys.SysUser;
import com.yuxin.art.modules.art.course.service.ArtProgressNoteService;
import com.yuxin.art.modules.auth.vo.AuthUser;
import com.yuxin.art.modules.patient.service.PatientInfoService;
import com.yuxin.art.modules.print.PrintConstants;
import com.yuxin.art.modules.surgery.mapper.SurgeryDiagnosticCurettageMapper;
import com.yuxin.art.modules.surgery.service.SurgeryDiagnosticCurettageService;
import com.yuxin.art.modules.surgery.service.SurgerySpecialOrderService;
import com.yuxin.art.modules.surgery.vo.EditSurgeryDiagnosticCurettageVO;
import com.yuxin.art.modules.surgery.vo.SurgeryDiagnosticCurettageVO;
import com.yuxin.art.modules.sys.mapper.SysUserMapper;
import com.yuxin.art.modules.upcoming.service.TodoSpecialSurgeryService;
import com.yuxin.art.modules.utils.GlobalLogicUtils;
import com.yuxin.art.modules.utils.PrintUtils;
import com.yuxin.constant.GlobalConstant;
import com.yuxin.framework.exception.BusinessException;
import com.yuxin.framework.mvc.mapper.BaseMapper;
import com.yuxin.framework.mvc.service.impl.BaseServiceImpl;
import com.yuxin.security.util.AuthUtil;
import com.yuxin.util.SpringUtil;
import com.yuxin.art.modules.print.util.SignPrintUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.util.*;

/**
 * <p>
 * 诊断性刮宫手术 ServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-15
 */
@Service
@Slf4j
public class SurgeryDiagnosticCurettageServiceImpl extends BaseServiceImpl<SurgeryDiagnosticCurettage> implements SurgeryDiagnosticCurettageService {
    @Autowired
    private SurgeryDiagnosticCurettageMapper surgeryDiagnosticCurettageMapper;
    @Autowired
    private SurgerySpecialOrderService surgerySpecialOrderService;
    @Autowired
    private TodoSpecialSurgeryService todoSpecialSurgeryService;
    @Autowired
    private BaseDataPrintCache baseDataPrintCache;
    @Autowired
    private ConfigRuleCache configRuleCache;
    @Value("${yuxin.oss.ftp.host}")
    private String imgUrl;
    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private PatientInfoService patientInfoService;

    @Override
    protected BaseMapper<SurgeryDiagnosticCurettage> getMapper() {
        return surgeryDiagnosticCurettageMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSurgeryDiagnosticCurettage(EditSurgeryDiagnosticCurettageVO editSurgeryDiagnosticCurettageVO) {
        SurgeryDiagnosticCurettage surgeryDiagnosticCurettage = surgeryDiagnosticCurettageMapper.selectById(editSurgeryDiagnosticCurettageVO.getId());
        if (null != surgeryDiagnosticCurettage) {
            SurgerySpecialOrder surgerySpecialOrder = surgerySpecialOrderService.get(surgeryDiagnosticCurettage.getSurgerySpecialId());
            if (null != surgerySpecialOrder) {
                GlobalLogicUtils.timeLogic(surgerySpecialOrder.getPlanTime(), "诊断性刮宫手术未到或者超过计划时间，无法进行操作");
                BeanUtils.copyProperties(editSurgeryDiagnosticCurettageVO, surgeryDiagnosticCurettage);
                surgeryDiagnosticCurettageMapper.updateById(surgeryDiagnosticCurettage);
                Long id = surgerySpecialOrder.getId();
                BeanUtils.copyProperties(editSurgeryDiagnosticCurettageVO, surgerySpecialOrder);
                surgerySpecialOrder.setId(id);
                surgerySpecialOrder.setStatus(GlobalConstant.YES);
                surgerySpecialOrder.setSurgeryBillingId(AuthUtil.getCurrentAuthInfo().getUserId());
                surgerySpecialOrder.setSurgeryBillingName(((AuthUser)AuthUtil.getCurrentAuthInfo().getUserInfo()).getName());
                surgerySpecialOrder.setSurgeryBillingTime(new Date());
                surgerySpecialOrderService.update(surgerySpecialOrder);
                // 修改待办状态
                todoSpecialSurgeryService.updateState(surgerySpecialOrder.getTodoId(), KArtConstants.TodoStatus.FINISH);
                ArtProgressNoteService artProgressNoteService = SpringUtil.getBean(ArtProgressNoteService.class);
                ArtProgressNote artProgressNote = artProgressNoteService.getByRelevanceId(surgerySpecialOrder.getId());
                if (null != artProgressNote) {
                    artProgressNoteService.updateByRelevanceId(surgerySpecialOrder.getId(), surgerySpecialOrder.getProcess());
                } else {
                    artProgressNoteService.saveSystemProgressNote(surgerySpecialOrder.getCycleId(), KArtConstants.ProgressNotes.DIAGNOSTICCURETTAGE,
                        surgerySpecialOrder.getProcess(), surgerySpecialOrder.getId());
                }
            }
        }
    }

    @Override
    public SurgeryDiagnosticCurettageVO getBySurgerySpecialId(Long surgerySpecialId) {
        SurgeryDiagnosticCurettageVO surgeryDiagnosticCurettageVO = null;
        SurgerySpecialOrder surgerySpecialOrder = surgerySpecialOrderService.get(surgerySpecialId);
        if (null != surgerySpecialOrder) {
            SurgeryDiagnosticCurettage surgeryDiagnosticCurettage = surgeryDiagnosticCurettageMapper.selectOne(
                new LambdaQueryWrapper<SurgeryDiagnosticCurettage>().eq(SurgeryDiagnosticCurettage::getSurgerySpecialId, surgerySpecialId));
            if (null != surgeryDiagnosticCurettage) {
                surgeryDiagnosticCurettageVO = new SurgeryDiagnosticCurettageVO();
                BeanUtils.copyProperties(surgerySpecialOrder, surgeryDiagnosticCurettageVO);
                BeanUtils.copyProperties(surgeryDiagnosticCurettage, surgeryDiagnosticCurettageVO);
                surgeryDiagnosticCurettageVO.setId(surgerySpecialOrder.getId());
                surgeryDiagnosticCurettageVO.setChildId(surgeryDiagnosticCurettage.getId());
                surgeryDiagnosticCurettageVO.setCheckType(surgeryDiagnosticCurettage.getType());
            }
        }
        return surgeryDiagnosticCurettageVO;
    }

    @Override
    public ByteArrayOutputStream exportPdf(ArtCycle artCycle, Long id, String path, int printMode) {
        Map<String, Object> map = new HashMap<>();
        ConfigRule configRule = configRuleCache.getConfigRule().get(0);
        map.put("hospitalName", configRule.getHospitalName());
        try {
            SurgeryDiagnosticCurettageVO vo = this.getBySurgerySpecialId(id);
            if (vo == null) {
                if (printMode == PrintConstants.PrintMode.STRICT_MODE) {
                    // 严格输出模式
                    throw new BusinessException(ResultEnum.FAILED.getCode(), "没有诊断性刮宫手术记录");
                } else if (printMode == PrintConstants.PrintMode.DEFAULT_IF_NULL) {
                    // 打印空白手术
                    map.putAll(PrintUtils.getCycleBasicData(artCycle));
                    return PrintUtils.printPdf(path, map);
                } else {
                    // 跳过，返回NULL
                    return null;
                }
            } else {
                map.putAll(BeanUtil.beanToMap(vo));
                map.putAll(PrintUtils.getCycleBasicData(artCycle));
                PatientInfo femaleInfo = patientInfoService.get(vo.getPatientFemaleId());
                if (femaleInfo != null) {
                    map.put("patientFemaleAge", IdcardUtil.getAgeByIdCard(femaleInfo.getIdCard(), vo.getSurgeryDate()));
                }
                // 手术时未必进周或配对
                if (ObjectUtil.isNotNull(vo.getPatientMaleId())) {
                    PatientInfo maleInfo = patientInfoService.get(vo.getPatientMaleId());
                    if (maleInfo != null) {
                        map.put("patientMaleAge", IdcardUtil.getAgeByIdCard(maleInfo.getIdCard(), vo.getSurgeryDate()));
                    }
                }
                if (vo.getVaginalBleeding() != null) {
                    map.put("vaginalBleeding", baseDataPrintCache.getByCodeAndValue("vaginalBleeding", vo.getVaginalBleeding().toString()));
                }
                if (vo.getDrug() != null) {
                    map.put("drug", vo.getDrug() == 1 ? "有" : "无");
                }
                if (vo.getUterineCavityWall() != null) {
                    map.put("uterineCavityWall", baseDataPrintCache.getByCodeAndValue("uterineCavityWall", vo.getUterineCavityWall().toString()));
                }
                // 处理送检类型
                String checkType = vo.getCheckType();
                if (StrUtil.isNotBlank(checkType)) {
                    List<String> split = StrUtil.split(checkType, CharUtil.COMMA);
                    // 排序，防止数据乱序
                    Collections.sort(split);
                    List<String> result = new ArrayList<>(split.size());
                    for (String s : split) {
                        result.add(baseDataPrintCache.getByCodeAndValue("diagnosticCurettageCheckType", s));
                    }
                    map.put("checkType", CollUtil.join(result, KArtConstants.Symbols.PAUSE));
                }

                if (vo.getSurgeryDoctorId() != null) {
                    SysUser sysUser = userMapper.selectById(vo.getSurgeryDoctorId());
                    if (sysUser != null) {
                        SignPrintUtil.putUserSignToMap(map, "doctorSign", sysUser, imgUrl);
                    }
                }

                return PrintUtils.printPdf(path, map);
            }
        } catch (Exception e) {
            log.error("打印诊断性刮宫手术PDF异常, 手术ID: {}", id, e);
            throw new BusinessException(ResultEnum.FAILED.getCode(), "打印诊断性刮宫手术PDF异常");
        }
    }
}
