/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:FollowUpAfterBirthController.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.followup.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.yuxin.api.Result;
import com.yuxin.art.domain.art.cycle.ArtCycle;
import com.yuxin.art.domain.followup.FollowUpAfterBirth;
import com.yuxin.art.modules.followup.service.FollowUpAfterBirthService;
import com.yuxin.art.modules.followup.vo.AddFollowUpAfterBirthVO;
import com.yuxin.art.modules.followup.vo.EditFollowUpAfterBirthVO;
import com.yuxin.art.modules.print.PrintConstants;
import com.yuxin.art.modules.utils.PrintUtils;
import com.yuxin.framework.mvc.controller.BaseController;
import com.yuxin.framework.mvc.vo.IdVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 出生后随访 Controller
 *
 * <AUTHOR>
 * @since 2020-12-16
 */
@RestController
@RequestMapping(value = "/followup/followupAfterBirth", produces = MediaType.APPLICATION_JSON_VALUE)
@Validated
@Api(tags = "出生后随访")
@ApiSort(1)
public class FollowUpAfterBirthController extends BaseController {
    @Autowired
    private FollowUpAfterBirthService followUpAfterBirthService;

    /**
     * 新增
     *
     * @param addFollowUpAfterBirthVO
     * @return
     */
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "新增", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 1)
    // @RequiresPermissions("followup:followupAfterBirth:create")
    public Result<IdVO> create(@RequestBody @Validated AddFollowUpAfterBirthVO addFollowUpAfterBirthVO) {
        Long id = followUpAfterBirthService.insertBirthAfter(addFollowUpAfterBirthVO);
        return Result.success(new IdVO(id));
    }

    /**
     * 删除
     *
     * @param id
     */
    @DeleteMapping
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "id", required = true, paramType = "query", dataType = "String")
    @ApiOperationSupport(order = 2)
    // @RequiresPermissions("followup:followupAfterBirth:delete")
    public Result<Void> delete(@NotNull(message = "id不能为空") Long id) {
        followUpAfterBirthService.delete(id);
        return Result.success();
    }

    /**
     * 编辑
     *
     * @param editFollowUpAfterBirthVO
     */
    @PutMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "编辑", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 3)
    // @RequiresPermissions("followup:followupAfterBirth:edit")
    public Result<Void> edit(@RequestBody @Validated EditFollowUpAfterBirthVO editFollowUpAfterBirthVO) {
        followUpAfterBirthService.updateBirthAfter(editFollowUpAfterBirthVO);
        return Result.success();
    }

    /**
     * 根据周期Id查询列表
     *
     * @return
     */
    @GetMapping("/getListByCycleId")
    @ApiOperation(value = "根据周期Id查询列表")
    @ApiImplicitParam(name = "cycleId", value = "周期Id", required = true, paramType = "query", dataType = "String")
    @ApiOperationSupport(order = 7)
    // @RequiresPermissions("followup:followupAfterBirth:getListByCycleId")
    public Result<List<FollowUpAfterBirth>> getListByCycleId(@NotNull(message = "周期Id不能为空") Long cycleId) {
        List<FollowUpAfterBirth> list = followUpAfterBirthService.getListByCycleId(cycleId);
        return Result.success(list);
    }

    /**
     * 根据周期ID获取PDF信息
     *
     * @param cycleId
     * @return
     */
    @GetMapping(value = "/getPdf")
    @ApiOperation(value = "根据周期ID获取PDF信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "cycleId", value = "周期ID", required = true, paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "printMode", value = "打印模式：0.没有数据就报错、1.没有数据就打印空PDF、2.没有就跳过", required = true,
            paramType = "query", dataType = "Integer")})
    @ApiOperationSupport(order = 5)
    public void getPdf(@NotNull(message = "周期ID不能为空") Long cycleId, @NotNull(message = "打印模式不能为空") int printMode) {
        ArtCycle artCycle = new ArtCycle();
        artCycle.setId(cycleId);
        ByteArrayOutputStream byteArrayOutputStream =
            followUpAfterBirthService.exportPdf(artCycle, PrintConstants.TemplateName.FOLLOWUP_AFTER_BIRTH, printMode);
        PrintUtils.outputPdf(byteArrayOutputStream);
    }
}
