/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:EditClinicFemalePhysicalVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.clinic.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 女患者门诊体格检查
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-11
 */
@Data
@NoArgsConstructor
@ApiModel(value = "EditClinicFemalePhysicalVO", description = "编辑女患者门诊体格检查")
public class EditClinicFemalePhysicalVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @ApiModelProperty(value = "ID", required = true)
    @NotNull(message = "id不能为空")
    public Long id;

    /**
     * 门诊病历主键ID
     */
    @ApiModelProperty(value = "门诊病历主键ID", position = 2, required = true)
    @NotNull(message = "门诊病历主键ID不能为空")
    private Long recordId;

    /**
     * 患者主键ID
     */
    @ApiModelProperty(value = "患者主键ID", required = true)
    @NotNull(message = "患者主键ID不能为空")
    private Long patientId;

    /**
     * 一般检查-身高(cm)
     */
    @ApiModelProperty(value = "一般检查-身高(cm)(3,2)", required = false)
    @Digits(integer = 3, fraction = 2, message = "一般检查-身高(cm)只能包含3位整数,2位小数")
    @DecimalMin(value = "0.01", message = "一般检查-身高(cm)不能小于等于0")
    private BigDecimal giHeight;
    /**
     * 一般检查-体重（kg）
     */
    @ApiModelProperty(value = "一般检查-体重（kg）(3,2)", required = false)
    @Digits(integer = 3, fraction = 2, message = "一般检查-体重（kg）只能包含3位整数,2位小数")
    @DecimalMin(value = "0.01", message = "一般检查-体重（kg）不能小于等于0")
    private BigDecimal giWeight;
    /**
     * 一般检查-BMI（kd/m2）
     */
    @ApiModelProperty(value = "一般检查-BMI（kd/m2）", position = 6, required = false)
    @Digits(integer = 3, fraction = 2, message = "一般检查-BMI（kd/m2）只能包含3位整数,2位小数")
    @DecimalMin(value = "0.01", message = "一般检查-BMI不能小于等于0")
    private BigDecimal giBmi;

    /**
     * 一般检查-呼吸（次/分钟）
     */
    @ApiModelProperty(value = "一般检查-呼吸（次/分钟）", position = 7, required = false)
    @Range(min = 0, message = "一般检查-呼吸不能小于0")
    private Integer giBreath;
    /**
     * 一般检查-脉搏（次/分钟）
     */
    @ApiModelProperty(value = "一般检查-脉搏（次/分钟）", position = 8, required = false)
    @Range(min = 0, message = "一般检查-脉搏不能小于0")
    private Integer giPulse;

    /**
     * 一般检查-体温（℃）
     */
    @ApiModelProperty(value = "一般检查-体温（℃）", position = 9, required = false)
    @Digits(integer = 3, fraction = 2, message = "一般检查-体温（℃）只能包含3位整数,2位小数")
    @DecimalMin(value = "0.01", message = "一般检查-体温不能小于等于0")
    private BigDecimal giTemperature;

    /**
     * 一般检查-收缩压（mmHg）
     */
    @ApiModelProperty(value = "一般检查-收缩压（mmHg）(3,2)", required = false)
    @Digits(integer = 3, fraction = 2, message = "一般检查-收缩压（mmHg）只能包含3位整数,2位小数")
    @DecimalMin(value = "0.01", message = "一般检查-收缩压（mmHg）不能小于等于0")
    private BigDecimal giSystolicPressure;
    /**
     * 一般检查-扩张压（mmHg）
     */
    @ApiModelProperty(value = "一般检查-扩张压（mmHg）(3,2)", required = false)
    @Digits(integer = 3, fraction = 2, message = "一般检查-扩张压（mmHg）只能包含3位整数,2位小数")
    @DecimalMin(value = "0.01", message = "一般检查-扩张压（mmHg）不能小于等于0")
    private BigDecimal giExpansionPressure;

    /**
     * 一般检查-腰围
     */
    @ApiModelProperty(value = "一般检查-腰围", position = 12, required = false)
    @Digits(integer = 5, fraction = 2, message = "一般检查-腰围只能包含5位整数,2位小数")
    @DecimalMin(value = "0.01", message = "一般检查-腰臀不能小于等于0")
    private BigDecimal giWaistline;

    /**
     * 一般检查-腰臀比
     */
    @ApiModelProperty(value = "一般检查-臀围", position = 12, required = false)
    @Digits(integer = 5, fraction = 2, message = "一般检查-臀围只能包含5位整数,2位小数")
    @DecimalMin(value = "0.01", message = "一般检查-臀围不能小于等于0")
    private BigDecimal giHipline;

    /**
     * 一般检查-腰臀比
     */
    @ApiModelProperty(value = "一般检查-腰臀比", position = 12, required = false)
    @Digits(integer = 3, fraction = 2, message = "一般检查-腰臀比（℃）只能包含3位整数,2位小数")
    private BigDecimal giWhr;

    /**
     * 专科检查-外阴：正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "专科检查-外阴：正常1、异常-1、未查0；正常值：正常", position = 13, required = false)
    @Range(min = -1, max = 1, message = "专科检查-外阴不匹配")
    private Integer seVulva;
    /**
     * 专科检查-阴道：正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "专科检查-阴道：正常1、异常-1、未查0；正常值：正常", position = 14, required = false)
    @Range(min = -1, max = 1, message = "专科检查-阴道不匹配")
    private Integer seVaginal;
    /**
     * 专科检查-宫颈(字典)：光滑、轻度外移、中度外移、重度外移；正常值：光滑
     */
    @ApiModelProperty(value = "专科检查-宫颈(字典se-cervix-surface)：光滑、轻度糜烂、中度糜烂、重度糜烂；正常值：光滑", position = 15, required = true)
    @NotBlank(message = "专科检查-宫颈不能为空")
    private String seCervixSurface;
    /**
     * 专科检查-纳氏囊肿：无1、有-1、未查0；正常值：无
     */
    @ApiModelProperty(value = "专科检查-纳氏囊肿：无1、有-1、未查0；正常值：无", position = 16, required = false)
    @Range(min = -1, max = 1, message = "专科检查-纳氏囊肿不匹配")
    private Integer seNanoCyst;
    /**
     * 专科检查-宫颈肥大(字典)：正常、I、II、III、VI、未查；正常值：正常
     */
    @ApiModelProperty(value = "专科检查-宫颈肥大(字典)：正常、I、II、III、VI、未查；正常值：正常")
    private String seCervicalHypertrophy;
    /**
     * 专科检查-子宫位置(字典)：前位、平位、后位、未查；正常值：前位
     */
    @ApiModelProperty(value = "专科检查-子宫位置(字典)：前位、平位、后位、未查；正常值：前位", position = 18, required = false)
    private String seUterusPosition;
    /**
     * 专科检查-子宫大小：正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "专科检查-子宫大小：正常1、异常-1、未查0；正常值：正常", position = 19, required = false)
    @Range(min = -1, max = 1, message = "专科检查-子宫大小不匹配")
    private Integer seUterusSize;
    /**
     * 专科检查-子宫质地(字典)：中、软、硬、未查；正常值：中
     */
    @ApiModelProperty(value = "专科检查-子宫质地(字典)：中、软、硬、未查；正常值：中", position = 20, required = false)
    private String seUterineTexture;
    /**
     * 专科检查-子宫活动度(字典)：活动、受限、固定、未查；正常值：活动
     */
    @ApiModelProperty(value = "专科检查-子宫活动度(字典)：活动、受限、固定、未查；正常值：活动", position = 21, required = false)
    private String seUterineActivity;
    /**
     * 专科检查-子宫压痛：无、有、未查；正常值：无
     */
    @ApiModelProperty(value = "专科检查-子宫压痛：无1、有-1、未查0；正常值：无", position = 22, required = false)
    @Range(min = -1, max = 1, message = "专科检查-子宫压痛不匹配")
    private Integer seUterineTenderness;
    /**
     * 专科检查-左附件：正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "专科检查-左附件：正常1、异常-1、未查0；正常值：正常", position = 23, required = false)
    @Range(min = -1, max = 1, message = "专科检查-左附件不匹配")
    private Integer seLeftAnnex;
    /**
     * 专科检查-右附件：正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "专科检查-右附件：正常1、异常-1、未查0；正常值：正常", position = 24, required = false)
    @Range(min = -1, max = 1, message = "专科检查-右附件不匹配")
    private Integer seRightAnnex;
    /**
     * 专科检查-盆腔肿块：无、有、未查
     */
    @ApiModelProperty(value = "专科检查-盆腔肿块：无1、有-1、未查0；正常值：无", position = 25, required = false)
    @Range(min = -1, max = 1, message = "专科检查-盆腔肿块不匹配")
    private Integer sePelvicMass;

    /**
     * 常规检查-营养:正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "常规检查-营养:正常1、异常-1、未查0；正常值：正常", position = 26, required = false)
    @Range(min = -1, max = 1, message = "常规检查-营养不匹配")
    private Integer reNutrition;
    /**
     * 常规检查-神志:清晰、模糊、未查；正常值：请
     */
    @ApiModelProperty(value = "常规检查-神志:清晰1、模糊-1、未查0；正常值：清晰", position = 27, required = false)
    @Range(min = -1, max = 1, message = "常规检查-神志不匹配")
    private Integer reSenses;
    /**
     * 常规检查-发育:正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "常规检查-发育:正常1、异常-1、未查0；正常值：正常", position = 28, required = false)
    @Range(min = -1, max = 1, message = "常规检查-发育不匹配")
    private Integer reGrowth;
    /**
     * 常规检查-毛发:正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "常规检查-毛发:正常1、异常-1、未查0；正常值：正常", position = 29, required = false)
    @Range(min = -1, max = 1, message = "常规检查-毛发不匹配")
    private Integer reHair;
    /**
     * 常规检查-皮肤黏膜:正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "常规检查-皮肤黏膜:正常1、异常-1、未查0；正常值：正常", position = 30, required = false)
    @Range(min = -1, max = 1, message = "常规检查-皮肤黏膜不匹配")
    private Integer reSkinMucosa;
    /**
     * 常规检查-浅表淋巴结:正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "常规检查-浅表淋巴结:正常1、异常-1、未查0；正常值：正常", position = 31, required = false)
    @Range(min = -1, max = 1, message = "常规检查-浅表淋巴结不匹配")
    private Integer reSuperficialLymphNodes;
    /**
     * 常规检查-乳房:正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "常规检查-乳房:正常1、异常-1、未查0；正常值：正常", position = 32, required = false)
    @Range(min = -1, max = 1, message = "常规检查-乳房不匹配")
    private Integer reBreast;
    /**
     * 常规检查-溢乳:无、有、未查；正常值：无
     */
    @ApiModelProperty(value = "常规检查-溢乳:无0、有-1、未查0；正常值：无", position = 33, required = false)
    @Range(min = -1, max = 1, message = "常规检查-溢乳不匹配")
    private Integer reGalactorrhea;
    /**
     * 常规检查-心脏:正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "常规检查-心脏:正常1、异常-1、未查0；正常值：正常", position = 34, required = false)
    @Range(min = -1, max = 1, message = "常规检查-心脏不匹配")
    private Integer reHeart;
    /**
     * 常规检查-肺脏:正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "常规检查-肺脏:正常1、异常-1、未查0；正常值：正常", position = 35, required = false)
    @Range(min = -1, max = 1, message = "常规检查-肺脏不匹配")
    private Integer reLung;
    /**
     * 常规检查-肝脏:正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "常规检查-肝脏:正常1、异常-1、未查0；正常值：正常", position = 36, required = false)
    @Range(min = -1, max = 1, message = "常规检查-肝脏不匹配")
    private Integer reLiver;
    /**
     * 常规检查-肾脏:正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "常规检查-肾脏:正常1、异常-1、未查0；正常值：正常", position = 37, required = false)
    @Range(min = -1, max = 1, message = "常规肾脏-肝脏不匹配")
    private Integer reKidney;
    /**
     * 常规检查-嗅觉:正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "常规检查-嗅觉:正常1、异常-1、未查0；正常值：正常", position = 38, required = false)
    @Range(min = -1, max = 1, message = "常规肾脏-嗅觉不匹配")
    private Integer reSmell;
    /**
     * 常规检查-脊柱四肢:正常、异常、未查；正常值：正常
     */
    @ApiModelProperty(value = "常规检查-脊柱四肢:正常1、异常-1、未查0；正常值：正常", position = 39, required = false)
    @Range(min = -1, max = 1, message = "常规肾脏-脊柱四肢不匹配")
    private Integer reSpinalLimbs;
    /**
     * 常规检查-黑棘皮症:无、有、未查；正常值：无
     */
    @ApiModelProperty(value = "常规检查-黑棘皮症:无1、有-1、未查0；正常值：无", position = 40, required = false)
    @Range(min = -1, max = 1, message = "常规肾脏-黑棘皮症不匹配")
    private Integer reBlackAcanthosis;
    /**
     * 常规检查-痤疮:无、有、未查；正常值：无
     */
    @ApiModelProperty(value = "常规检查-痤疮:无1、有-1、未查0；正常值：无", position = 41, required = false)
    @Range(min = -1, max = 1, message = "常规肾脏-痤疮不匹配")
    private Integer reAcne;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注(200)", required = false)
    @Length(max = 200, message = "备注不能超过200个字符")
    private String remark;

}
