/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:IcePackBorrowAndReturnController.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */

package com.yuxin.art.modules.fz.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.yuxin.api.Result;
import com.yuxin.art.domain.fz.IcePackBorrowAndReturn;
import com.yuxin.art.modules.fz.service.IcePackBorrowAndReturnService;
import com.yuxin.art.modules.fz.vo.AddIcePackVo;
import com.yuxin.art.modules.fz.vo.EditIcePackVo;
import com.yuxin.framework.mvc.controller.BaseController;
import com.yuxin.framework.mvc.vo.IdVO;
import com.yuxin.framework.mvc.vo.PageParams;
import com.yuxin.security.util.AuthUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 冰袋借还 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-13
 */
@RestController
@RequestMapping(value = "/fz/IcePackBorrowAndReturn", produces = MediaType.APPLICATION_JSON_VALUE)
@Validated
@Api(tags = "冰袋借还")
@ApiSort(1)
public class IcePackBorrowAndReturnController extends BaseController {
    @Autowired
    private IcePackBorrowAndReturnService icePackBorrowAndReturnService;

    /**
     * 获取冰袋借还列表
     *
     * @return
     */
    @GetMapping("/getPage")
    @ApiOperation(value = "获取冰袋借还列表")
    @ApiOperationSupport(order = 1)
    public Result<IPage<IcePackBorrowAndReturn>> getPage(@Validated PageParams page) {
        IPage<IcePackBorrowAndReturn> list = icePackBorrowAndReturnService.getBusinessList(page);
        return Result.success(list);
    }

    /**
     * 根据病人id查询借还记录
     *
     * @param patientId
     * @return
     */
    @GetMapping("/getPatientId")
    @ApiOperation(value = "根据病人id查询借还记录")
    @ApiImplicitParam(name = "patientId", value = "病人ID", required = true, paramType = "query", dataType = "String")
    @ApiOperationSupport(order = 2)
    public Result<List<IcePackBorrowAndReturn>> getPatientId(@NotNull(message = "病人ID不能为空") Long patientId) {
        List<IcePackBorrowAndReturn> list = icePackBorrowAndReturnService.getPatientId(patientId);
        return Result.success(list);
    }

    /**
     * 新增
     *
     * @param addIcePackVo
     * @return
     */
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "新增", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 3)
    public Result<IdVO> create(@RequestBody @Validated AddIcePackVo addIcePackVo) {
        IcePackBorrowAndReturn icePackBorrowAndReturn = new IcePackBorrowAndReturn();
        addIcePackVo.setOperatorId(AuthUtil.getCurrentAuthInfo().getUserId());
        addIcePackVo.setOperatorName(AuthUtil.getCurrentAuthInfo().getName());
        addIcePackVo.setOperatorTime(new Date());
        BeanUtil.copyProperties(addIcePackVo, icePackBorrowAndReturn);
        Long id = icePackBorrowAndReturnService.insert(icePackBorrowAndReturn);
        return Result.success(new IdVO(id));
    }

    /**
     * 修改
     *
     * @param editIcePackVo
     */
    @PutMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "修改", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 4)
    public Result<Void> edit(@RequestBody @Validated EditIcePackVo editIcePackVo) {
        IcePackBorrowAndReturn icePackBorrowAndReturn = new IcePackBorrowAndReturn();
        editIcePackVo.setOperatorId(AuthUtil.getCurrentAuthInfo().getUserId());
        editIcePackVo.setOperatorName(AuthUtil.getCurrentAuthInfo().getName());
        editIcePackVo.setOperatorTime(new Date());
        BeanUtil.copyProperties(editIcePackVo, icePackBorrowAndReturn);
        icePackBorrowAndReturnService.update(icePackBorrowAndReturn);
        return Result.success();
    }

    /**
     * 删除
     *
     * @param id
     */
    @DeleteMapping
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "id", required = true, paramType = "query", dataType = "String")
    @ApiOperationSupport(order = 5)
    public Result<Void> delete(@NotNull(message = "id不能为空") Long id) {
        icePackBorrowAndReturnService.delete(id);
        return Result.success();
    }
}
