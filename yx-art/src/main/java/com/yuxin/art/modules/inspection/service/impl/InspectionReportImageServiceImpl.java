/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:InspectionReportImageServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.inspection.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuxin.art.domain.inspection.InspectionReport;
import com.yuxin.art.domain.inspection.InspectionReportImage;
import com.yuxin.art.modules.inspection.mapper.InspectionReportImageMapper;
import com.yuxin.art.modules.inspection.service.InspectionReportImageService;
import com.yuxin.art.modules.inspection.service.InspectionReportService;
import com.yuxin.art.modules.inspection.vo.AddInspectionReportImageVO;
import com.yuxin.art.modules.inspection.vo.EditInspectionReportImageVO;
import com.yuxin.constant.GlobalConstant;
import com.yuxin.framework.mvc.mapper.BaseMapper;
import com.yuxin.framework.mvc.service.impl.BaseServiceImpl;
import com.yuxin.security.util.AuthUtil;
import com.yuxin.util.SpringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 检查检验图像
 *
 * <AUTHOR>
 * @date 2020-08-12
 */
@Service
public class InspectionReportImageServiceImpl extends BaseServiceImpl<InspectionReportImage> implements InspectionReportImageService {

    @Autowired
    private InspectionReportImageMapper inspectionReportImageMapper;
    @Autowired
    private InspectionReportService inspectionReportService;

    @Override
    protected BaseMapper<InspectionReportImage> getMapper() {
        return inspectionReportImageMapper;
    }

    @Override
    public List<InspectionReportImage> getByReportId(Long reportId) {
        return inspectionReportImageMapper.selectList(
            Wrappers.lambdaQuery(InspectionReportImage.class).eq(InspectionReportImage::getReportId, reportId)
                .orderByAsc(InspectionReportImage::getSort));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createImages(List<AddInspectionReportImageVO> addInspectionReportImageVOS) {
        addInspectionReportImageVOS.forEach(addInspectionReportImageVO -> {
            InspectionReportImage inspectionReportImage = new InspectionReportImage();
            BeanUtils.copyProperties(addInspectionReportImageVO, inspectionReportImage);
            inspectionReportImage.setRecorderId(AuthUtil.getCurrentAuthInfo().getUserId());
            inspectionReportImage.setRecorderName(AuthUtil.getCurrentAuthInfo().getName());
            inspectionReportImage.setRecorderTime(new Date());
            super.insert(inspectionReportImage);
            InspectionReport inspectionReport = inspectionReportService.get(inspectionReportImage.getReportId());
            inspectionReport.setHaveImage(1);
            inspectionReportService.update(inspectionReport);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editImages(List<EditInspectionReportImageVO> editInspectionReportImageVOS) {
        editInspectionReportImageVOS.forEach(editInspectionReportImageVO -> {
            InspectionReportImage inspectionReportImage = inspectionReportImageMapper.selectById(editInspectionReportImageVO.getId());
            BeanUtils.copyProperties(editInspectionReportImageVO, inspectionReportImage);
            inspectionReportImage.setRecorderId(AuthUtil.getCurrentAuthInfo().getUserId());
            inspectionReportImage.setRecorderName(AuthUtil.getCurrentAuthInfo().getName());
            inspectionReportImage.setRecorderTime(new Date());
            super.update(inspectionReportImage);
            InspectionReport inspectionReport = inspectionReportService.get(inspectionReportImage.getReportId());
            inspectionReport.setHaveImage(1);
            inspectionReportService.update(inspectionReport);
        });
    }

    @Override
    public void delete(Long id) {
        InspectionReportImage inspectionReportImage = inspectionReportImageMapper.selectById(id);
        inspectionReportImageMapper.deleteById(id);
        Long count = inspectionReportImageMapper.selectCount(
            Wrappers.lambdaQuery(InspectionReportImage.class).eq(InspectionReportImage::getReportId, inspectionReportImage.getReportId()));
        // 更新报告是否有图像
        if (count <= 0) {
            InspectionReportService inspectionReportService = SpringUtil.getBean(InspectionReportService.class);
            InspectionReport inspectionReport = new InspectionReport();
            inspectionReport.setId(inspectionReportImage.getReportId());
            inspectionReport.setHaveImage(GlobalConstant.NO);
            inspectionReportService.update(inspectionReport);
        }
    }

}
