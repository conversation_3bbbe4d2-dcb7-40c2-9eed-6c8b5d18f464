/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称：yx-art
 * 文件名称:DataChangeHistoryServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.trace.history.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.yuxin.art.modules.trace.constant.TraceConstants;
import com.yuxin.art.modules.trace.enums.BizModuleEnum;
import com.yuxin.art.modules.trace.history.service.DataChangeHistoryService;
import com.yuxin.art.modules.trace.history.vo.DataChangeHistoryVO;
import com.yuxin.art.modules.trace.history.vo.FieldChangeHistoryVO;
import com.yuxin.framework.mvc.vo.PageResult;
import com.yuxin.search.service.ElasticSearchService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据变更历史查询服务实现类
 * <p>
 * 基于ElasticSearch实现数据变更历史的查询功能，支持复杂查询条件和数据分析。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service("dataChangeHistoryServiceImpl")
public class DataChangeHistoryServiceImpl implements DataChangeHistoryService {

    @Autowired
    private ElasticSearchService elasticSearchService;

    @Override
    public PageResult<DataChangeHistoryVO> queryHistoryByPage(Long bizId, BizModuleEnum bizModuleEnum, Integer pageNum, Integer pageSize,
        Date startTime, Date endTime) {
        try {
            // 构建查询条件
            BoolQueryBuilder queryBuilder = buildBaseQuery(bizId, bizModuleEnum);

            // 添加时间范围查询
            if (startTime != null || endTime != null) {
                addTimeRangeQuery(queryBuilder, startTime, endTime);
            }

            // 执行分页查询
            // 构建查询条件
            SearchSourceBuilder builder = new SearchSourceBuilder();
            builder.query(queryBuilder);
            builder.sort("operationTime", SortOrder.DESC);

            // 执行分页查询
            Map<String, Object> esResult = elasticSearchService.searchPage(TraceConstants.TRACE_INDEX_NAME, builder,
                (pageNum != null ? pageNum - 1 : 0) * (pageSize != null ? pageSize : TraceConstants.Query.DEFAULT_PAGE_SIZE),
                pageSize != null ? pageSize : TraceConstants.Query.DEFAULT_PAGE_SIZE);

            // 转换结果
            // 转换结果 - 从Map中提取数据
            @SuppressWarnings("unchecked") List<Map<String, Object>> records = (List<Map<String, Object>>)esResult.get("records");
            List<DataChangeHistoryVO> historyList =
                records != null ? records.stream().map(this::convertToHistoryVO).collect(Collectors.toList()) : new ArrayList<>();

            // 使用适配器构建返回结果
            PageResult<DataChangeHistoryVO> result = com.yuxin.art.modules.trace.util.PageResultAdapter.createPageResult(historyList,
                ((Number)esResult.getOrDefault("total", 0)).longValue(), pageNum != null ? pageNum : 1,
                pageSize != null ? pageSize : TraceConstants.Query.DEFAULT_PAGE_SIZE);

            return result;

        } catch (Exception e) {
            log.error("分页查询数据变更历史失败：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum, e);
            return new PageResult<>();
        }
    }

    @Override
    public List<DataChangeHistoryVO> queryAllHistory(Long bizId, BizModuleEnum bizModuleEnum) {
        try {
            // 构建查询条件
            BoolQueryBuilder queryBuilder = buildBaseQuery(bizId, bizModuleEnum);

            // 执行查询（限制最大返回1000条）
            // 构建查询条件
            SearchSourceBuilder builder = new SearchSourceBuilder();
            builder.query(queryBuilder);
            builder.sort("operationTime", SortOrder.DESC);
            builder.from(0);
            builder.size(TraceConstants.Query.MAX_PAGE_SIZE);

            // 执行ES查询
            Map<String, Object> searchResult =
                elasticSearchService.searchPage(TraceConstants.TRACE_INDEX_NAME, builder, 0, TraceConstants.Query.MAX_PAGE_SIZE);

            // 提取查询结果
            @SuppressWarnings("unchecked") List<Map<String, Object>> esResult =
                (List<Map<String, Object>>)searchResult.getOrDefault("records", new ArrayList<>());

            // 转换结果
            return esResult.stream().map(this::convertToHistoryVO).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("查询所有数据变更历史失败：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<FieldChangeHistoryVO> queryFieldHistory(Long bizId, BizModuleEnum bizModuleEnum, String fieldName) {
        if (StrUtil.isBlank(fieldName)) {
            return new ArrayList<>();
        }

        // 查询所有历史记录
        List<DataChangeHistoryVO> allHistory = queryAllHistory(bizId, bizModuleEnum);

        // 提取指定字段的变更历史
        List<FieldChangeHistoryVO> fieldHistory = new ArrayList<>();

        for (int i = 0; i < allHistory.size(); i++) {
            DataChangeHistoryVO current = allHistory.get(i);
            DataChangeHistoryVO previous = i < allHistory.size() - 1 ? allHistory.get(i + 1) : null;

            // 分析字段变更
            FieldChangeHistoryVO fieldChange = analyzeFieldChange(current, previous, fieldName);
            if (fieldChange != null) {
                fieldHistory.add(fieldChange);
            }
        }

        return fieldHistory;
    }

    @Override
    public Map<String, List<FieldChangeHistoryVO>> queryAllFieldHistory(Long bizId, BizModuleEnum bizModuleEnum) {
        // 查询所有历史记录
        List<DataChangeHistoryVO> allHistory = queryAllHistory(bizId, bizModuleEnum);

        if (CollUtil.isEmpty(allHistory)) {
            return new HashMap<>();
        }

        // 获取所有字段名
        Set<String> allFields = new HashSet<>();
        for (DataChangeHistoryVO history : allHistory) {
            if (history.getFieldChanges() != null) {
                allFields.addAll(history.getFieldChanges().stream().map(FieldChangeHistoryVO::getFieldName).collect(Collectors.toSet()));
            }
        }

        // 为每个字段构建变更历史
        Map<String, List<FieldChangeHistoryVO>> result = new HashMap<>();
        for (String fieldName : allFields) {
            List<FieldChangeHistoryVO> fieldHistory = queryFieldHistory(bizId, bizModuleEnum, fieldName);
            if (CollUtil.isNotEmpty(fieldHistory)) {
                result.put(fieldName, fieldHistory);
            }
        }

        return result;
    }

    @Override
    public Map<String, Object> getLatestDataSnapshot(Long bizId, BizModuleEnum bizModuleEnum) {
        List<DataChangeHistoryVO> history = queryAllHistory(bizId, bizModuleEnum);
        if (CollUtil.isEmpty(history)) {
            return new HashMap<>();
        }

        // 最新的记录在第一个位置（按时间倒序）
        DataChangeHistoryVO latest = history.get(0);
        return reconstructDataFromHistory(history, latest.getOperationTime());
    }

    @Override
    public Map<String, Object> getDataSnapshotAtTime(Long bizId, BizModuleEnum bizModuleEnum, Date pointInTime) {
        List<DataChangeHistoryVO> history = queryAllHistory(bizId, bizModuleEnum);
        return reconstructDataFromHistory(history, pointInTime);
    }

    @Override
    public List<FieldChangeHistoryVO> compareDataBetweenTime(Long bizId, BizModuleEnum bizModuleEnum, Date fromTime, Date toTime) {
        Map<String, Object> fromSnapshot = getDataSnapshotAtTime(bizId, bizModuleEnum, fromTime);
        Map<String, Object> toSnapshot = getDataSnapshotAtTime(bizId, bizModuleEnum, toTime);

        List<FieldChangeHistoryVO> changes = new ArrayList<>();

        // 比较所有字段
        Set<String> allFields = new HashSet<>();
        allFields.addAll(fromSnapshot.keySet());
        allFields.addAll(toSnapshot.keySet());

        for (String fieldName : allFields) {
            Object fromValue = fromSnapshot.get(fieldName);
            Object toValue = toSnapshot.get(fieldName);

            if (!Objects.equals(fromValue, toValue)) {
                FieldChangeHistoryVO change = new FieldChangeHistoryVO();
                change.setFieldName(fieldName);
                change.setOldValue(fromValue != null ? fromValue.toString() : null);
                change.setNewValue(toValue != null ? toValue.toString() : null);
                change.setOperationType("COMPARE");
                changes.add(change);
            }
        }

        return changes;
    }

    @Override
    public String generateFieldComment(Long bizId, BizModuleEnum bizModuleEnum, String fieldName) {
        List<FieldChangeHistoryVO> fieldHistory = queryFieldHistory(bizId, bizModuleEnum, fieldName);

        if (CollUtil.isEmpty(fieldHistory)) {
            return "";
        }

        // 构建批注内容
        StringBuilder comment = new StringBuilder();
        comment.append("变更历史：").append(TraceConstants.Comment.SEPARATOR);

        for (FieldChangeHistoryVO change : fieldHistory) {
            if (StrUtil.isNotBlank(change.getChangeDescription())) {
                comment.append(change.getChangeDescription()).append(TraceConstants.Comment.SEPARATOR);
            }
        }

        return comment.toString();
    }

    @Override
    public Map<String, String> generateAllFieldComments(Long bizId, BizModuleEnum bizModuleEnum) {
        Map<String, List<FieldChangeHistoryVO>> allFieldHistory = queryAllFieldHistory(bizId, bizModuleEnum);

        Map<String, String> comments = new HashMap<>();
        for (Map.Entry<String, List<FieldChangeHistoryVO>> entry : allFieldHistory.entrySet()) {
            String fieldName = entry.getKey();
            List<FieldChangeHistoryVO> fieldHistory = entry.getValue();

            if (CollUtil.isNotEmpty(fieldHistory)) {
                StringBuilder comment = new StringBuilder();
                comment.append("变更历史：").append(TraceConstants.Comment.SEPARATOR);

                for (FieldChangeHistoryVO change : fieldHistory) {
                    if (StrUtil.isNotBlank(change.getChangeDescription())) {
                        comment.append(change.getChangeDescription()).append(TraceConstants.Comment.SEPARATOR);
                    }
                }

                comments.put(fieldName, comment.toString());
            }
        }

        return comments;
    }

    @Override
    public long countChanges(Long bizId, BizModuleEnum bizModuleEnum) {
        try {
            BoolQueryBuilder queryBuilder = buildBaseQuery(bizId, bizModuleEnum);
            // 构建查询条件
            SearchSourceBuilder builder = new SearchSourceBuilder();
            builder.query(queryBuilder);
            builder.size(0); // 只需要计数，不需要返回数据
            builder.trackTotalHits(true);

            // 执行ES计数查询
            Map<String, Object> searchResult = elasticSearchService.searchPage(TraceConstants.TRACE_INDEX_NAME, builder, 0, 0);

            // 提取总数
            Object totalObj = searchResult.get("total");
            if (totalObj instanceof Number) {
                return ((Number)totalObj).longValue();
            }
            return 0L;
        } catch (Exception e) {
            log.error("统计变更次数失败：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum, e);
            return 0;
        }
    }

    @Override
    public Date getLastModifyTime(Long bizId, BizModuleEnum bizModuleEnum) {
        List<DataChangeHistoryVO> history = queryAllHistory(bizId, bizModuleEnum);
        if (CollUtil.isEmpty(history)) {
            return null;
        }

        // 查找最新的UPDATE操作
        return history.stream().filter(h -> TraceConstants.OperationType.UPDATE.equals(h.getOperationType()))
            .map(DataChangeHistoryVO::getOperationTime).findFirst().orElse(null);
    }

    @Override
    public Date getCreateTime(Long bizId, BizModuleEnum bizModuleEnum) {
        List<DataChangeHistoryVO> history = queryAllHistory(bizId, bizModuleEnum);
        if (CollUtil.isEmpty(history)) {
            return null;
        }

        // 查找CREATE操作
        return history.stream().filter(h -> TraceConstants.OperationType.CREATE.equals(h.getOperationType()))
            .map(DataChangeHistoryVO::getOperationTime).findFirst().orElse(null);
    }

    /**
     * 构建基础查询条件
     */
    private BoolQueryBuilder buildBaseQuery(Long bizId, BizModuleEnum bizModuleEnum) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        if (bizId != null) {
            queryBuilder.must(QueryBuilders.termQuery("biz_id", bizId));
        }

        if (bizModuleEnum != null) {
            queryBuilder.must(QueryBuilders.termQuery("biz_module", bizModuleEnum.name()));
        }

        return queryBuilder;
    }

    /**
     * 添加时间范围查询
     */
    private void addTimeRangeQuery(BoolQueryBuilder queryBuilder, Date startTime, Date endTime) {
        if (startTime != null || endTime != null) {
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("operationTime");
            if (startTime != null) {
                rangeQuery.gte(startTime);
            }
            if (endTime != null) {
                rangeQuery.lte(endTime);
            }
            queryBuilder.must(rangeQuery);
        }
    }

    /**
     * 转换ES结果为HistoryVO
     */
    private DataChangeHistoryVO convertToHistoryVO(Map<String, Object> esDoc) {
        DataChangeHistoryVO vo = new DataChangeHistoryVO();

        vo.setId((String)esDoc.get("id"));
        vo.setOperatorId(getLongValue(esDoc, "operator_id"));
        vo.setOperatorName((String)esDoc.get("operator_name"));
        vo.setOperationType((String)esDoc.get("operation_type"));
        vo.setOperationTime(getDateValue(esDoc, "operationTime"));
        vo.setOperationClientIp((String)esDoc.get("operation_client_ip"));
        vo.setOperationSource((String)esDoc.get("operation_source"));
        vo.setBizId(getLongValue(esDoc, "biz_id"));
        vo.setBizTableName((String)esDoc.get("biz_table_name"));
        vo.setBizModule((String)esDoc.get("biz_module"));
        vo.setCreateTime(getDateValue(esDoc, "create_time"));

        // 设置描述信息
        vo.setOperationTypeDesc(getOperationTypeDesc(vo.getOperationType()));
        vo.setOperationSourceDesc(getOperationSourceDesc(vo.getOperationSource()));
        vo.setBizModuleDesc(getBusinessModuleDesc(vo.getBizModule()));

        return vo;
    }

    /**
     * 分析字段变更
     */
    private FieldChangeHistoryVO analyzeFieldChange(DataChangeHistoryVO current, DataChangeHistoryVO previous, String fieldName) {
        // 这里需要解析biz_data字段来分析具体的字段变更
        // 由于篇幅限制，这里提供基础实现框架
        // 实际实现需要解析JSON数据并比较字段值

        FieldChangeHistoryVO change = new FieldChangeHistoryVO();
        change.setFieldName(fieldName);
        change.setOperationType(current.getOperationType());
        change.setOperatorName(current.getOperatorName());
        change.setOperationTime(current.getOperationTime());

        return change;
    }

    /**
     * 从历史记录重构指定时间点的数据
     */
    private Map<String, Object> reconstructDataFromHistory(List<DataChangeHistoryVO> history, Date pointInTime) {
        // 这里需要根据历史记录重构数据
        // 由于篇幅限制，这里提供基础实现框架
        return new HashMap<>();
    }

    /**
     * 获取Long值
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Number) {
            return ((Number)value).longValue();
        }
        return null;
    }

    /**
     * 获取Date值
     */
    private Date getDateValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value instanceof Date) {
            return (Date)value;
        }
        if (value instanceof Long) {
            return new Date((Long)value);
        }
        return null;
    }

    /**
     * 获取操作类型描述
     */
    private String getOperationTypeDesc(String operationType) {
        if (operationType == null)
            return "";
        switch (operationType) {
            case "CREATE":
                return "创建";
            case "UPDATE":
                return "更新";
            case "DELETE":
                return "删除";
            default:
                return operationType;
        }
    }

    /**
     * 获取操作来源描述
     */
    private String getOperationSourceDesc(String operationSource) {
        if (operationSource == null)
            return "";
        switch (operationSource) {
            case "WEB":
                return "网页端";
            case "MOBILE":
                return "移动端";
            case "API":
                return "接口调用";
            default:
                return operationSource;
        }
    }

    /**
     * 获取业务模块描述
     */
    private String getBusinessModuleDesc(String bizModule) {
        if (bizModule == null)
            return "";
        try {
            BizModuleEnum module = BizModuleEnum.valueOf(bizModule);
            return module.getDisplayName();
        } catch (Exception e) {
            return bizModule;
        }
    }
}
