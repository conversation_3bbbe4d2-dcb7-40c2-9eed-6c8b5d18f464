/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ChelationMonitorController.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.ultrasound.controller;

import cn.hutool.core.util.ObjectUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.yuxin.api.Result;
import com.yuxin.art.constant.KArtConstants;
import com.yuxin.art.domain.ultrasound.UltrasoundMonitor;
import com.yuxin.art.modules.ultrasound.service.ChelationMonitorService;
import com.yuxin.art.modules.ultrasound.service.UltrasoundMonitorService;
import com.yuxin.art.modules.ultrasound.vo.AddChelationMonitorVO;
import com.yuxin.art.modules.ultrasound.vo.EditChelationMonitorVO;
import com.yuxin.art.modules.ultrasound.vo.PharmacyModeVO;
import com.yuxin.art.modules.ultrasound.vo.UltrasoundMonitorVO;
import com.yuxin.framework.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 促排监测
 *
 * <AUTHOR>
 * @date 2020-07-21
 */
@RestController
@RequestMapping(value = "/ultrasound/chelationMonitor", produces = MediaType.APPLICATION_JSON_VALUE)
@Validated
@Api(tags = "促排监测")
@ApiSort(1)
public class ChelationMonitorController extends BaseController {
    @Autowired
    private ChelationMonitorService chelationMonitorService;
    @Autowired
    private UltrasoundMonitorService ultrasoundMonitorService;

    /**
     * 新增
     *
     * @param patientId
     * @param cycleId
     * @return
     */
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "新增", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 1)
    // @RequiresPermissions("ultrasound:chelationMonitor:create")
    public Result<UltrasoundMonitor> create(@RequestBody @Validated AddChelationMonitorVO addChelationMonitorVO) {
        UltrasoundMonitor ultrasoundMonitor = chelationMonitorService.create(addChelationMonitorVO.getPatientId(), addChelationMonitorVO.getCycleId(),
            addChelationMonitorVO.getMonitorTime());
        return Result.success(ultrasoundMonitor);
    }

    /**
     * 删除
     *
     * @param id
     */
    @DeleteMapping
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "id", required = true, paramType = "query", dataType = "String")
    @ApiOperationSupport(order = 2)
    // @RequiresPermissions("ultrasound:chelationMonitor:delete")
    public Result<Void> delete(@NotNull(message = "id不能为空") Long id) {
        chelationMonitorService.delete(id);
        return Result.success();
    }

    /**
     * 根据用药模式展示监测列表
     *
     * @param cycleId
     * @return
     */
    @GetMapping("/getListByPharmacyMode")
    @ApiOperation(value = "根据用药模式展示监测列表")
    @ApiImplicitParam(name = "cycleId", value = "周期ID", required = true, paramType = "query", dataType = "String")
    @ApiOperationSupport(order = 3)
    // @RequiresPermissions("ultrasound:chelationMonitor:getListByPharmacyMode")
    public Result<PharmacyModeVO> getListByPharmacyMode(@NotNull(message = "周期ID不能为空") Long cycleId) {
        return Result.success(chelationMonitorService.getListByPharmacyMode(cycleId));
    }

    /**
     * 根据开药模式展示监测列表
     *
     * @param cycleId
     * @return
     */
    @GetMapping("/getListByPrescribeMode")
    @ApiOperation(value = "根据开药模式展示监测列表")
    @ApiImplicitParam(name = "cycleId", value = "周期ID", required = true, paramType = "query", dataType = "String")
    @ApiOperationSupport(order = 4)
    // @RequiresPermissions("ultrasound:chelationMonitor:getListByPrescribeMode")
    public Result<List<UltrasoundMonitorVO>> getListByPrescribeMode(@NotNull(message = "周期ID不能为空") Long cycleId) {
        List<UltrasoundMonitorVO> list = chelationMonitorService.getListByPrescribeMode(cycleId);
        return Result.success(list);
    }

    /**
     * 批量编辑
     *
     * @param editChelationMonitorVOs
     */
    @PutMapping(value = "/mEdit", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "批量编辑", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 5)
    // @RequiresPermissions("ultrasound:chelationMonitor:mEdit")
    public Result<Void> edit(@RequestBody @Valid List<EditChelationMonitorVO> editChelationMonitorVOs) {
        chelationMonitorService.edit(editChelationMonitorVOs);
        return Result.success();
    }

    // /**
    // * 编辑下次就诊日期
    // * @param editUltrasoundAbnormalVO
    // */
    // @PutMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    // @ApiOperation(value = "编辑", consumes = MediaType.APPLICATION_JSON_VALUE)
    // @ApiOperationSupport(order = 6)
    // //@RequiresPermissions("ultrasound:chelationMonitor:editNextVisitDate")
    // public Result<Void> editNextVisitDate(@RequestBody @Validated EditNextVisitDateVO editNextVisitDateVO) {
    // chelationMonitorService.editNextVisitDate(editNextVisitDateVO);
    // return Result.success();
    // }

    /**
     * 根据监测日期查询促排记录
     *
     * @param cycleId
     * @return
     */
    @GetMapping("/getByMonitorDate")
    @ApiOperation(value = "根据监测日期查询促排记录")
    @ApiImplicitParams({@ApiImplicitParam(name = "monitorDate", value = "监测日期", required = true, paramType = "query", dataType = "Date"),
        @ApiImplicitParam(name = "cycleId", value = "周期ID", required = true, paramType = "query", dataType = "String")})
    @ApiOperationSupport(order = 4)
    // @RequiresPermissions("ultrasound:chelationMonitor:getByMonitorDate")
    public Result<Boolean> getByMonitorDate(@NotNull(message = "周期ID不能为空") Long cycleId,
        @NotNull(message = "监测日期") @DateTimeFormat(pattern = "yyyy-MM-dd") Date monitorDate) {
        UltrasoundMonitor ultrasoundMonitor =
            ultrasoundMonitorService.getByMonitorTime(null, cycleId, KArtConstants.MonitorType.CHELATION, monitorDate);
        Boolean isChelation = Boolean.TRUE;
        if (ObjectUtil.isNull(ultrasoundMonitor)) {
            isChelation = Boolean.FALSE;
        }
        return Result.success(isChelation);
    }
}
