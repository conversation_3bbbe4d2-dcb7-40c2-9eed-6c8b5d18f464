/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:PageNumberHolder.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.utils;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * @ClassName PageNumberHolder
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/3/4 10:13
 * @Version 1.0
 */
public class PageNumberHolder {
    public static ThreadLocal<AtomicInteger> pageNum = new ThreadLocal<>();
}
