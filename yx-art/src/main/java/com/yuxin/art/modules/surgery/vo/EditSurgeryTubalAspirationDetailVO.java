/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:EditSurgeryTubalAspirationDetailVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.surgery.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 输卵管抽吸手术详情
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-15
 */
@Data
@NoArgsConstructor
@ApiModel(value = "EditSurgeryTubalAspirationDetailVO", description = "编辑输卵管抽吸手术详情")
public class EditSurgeryTubalAspirationDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 位置 0：左侧 1：右侧
     */
    @ApiModelProperty(value = "位置 0：左侧 1：右侧", required = true)
    @NotNull(message = "位置不能为空")
    @Min(value = 0, message = "无法识别的位置")
    @Max(value = 1, message = "无法识别的位置")
    private Integer position;
    /**
     * 积水大小
     */
    @ApiModelProperty(value = "积水大小(20)")
    @Length(max = 20, message = "积水大小不能超过20个字符")
    private String waterSize;
    /**
     * 边界：0-清楚、1-不清楚
     */
    @ApiModelProperty(value = "边界：0-清楚、1-不清楚")
    @Min(value = 0, message = "无法识别的边界")
    @Max(value = 1, message = "无法识别的边界")
    private Integer boundary;
    /**
     * 抽吸：1-是，0-否
     */
    @ApiModelProperty(value = "抽吸：1-是，0-否")
    @Min(value = 0, message = "无法识别的抽吸")
    @Max(value = 1, message = "无法识别的抽吸")
    private Integer aspiration;
    /**
     * 进针数 0<=X<=4
     */
    @ApiModelProperty(value = "进针数 0<=X<=99(0-99)")
    @Min(value = 0, message = "无法识别的进针数")
    @Max(value = 99, message = "无法识别的进针数")
    private Integer needlesNum;
    /**
     * 抽吸量(ml)
     */
    @ApiModelProperty(value = "抽吸量(ml)(3,2)")
    @Digits(integer = 3, fraction = 2, message = "抽吸量(ml)只能包含3位整数,2位小数")
    @DecimalMin(value = "0.01", message = "抽吸量(ml)不能小于等于0")
    private BigDecimal suctionCapacity;
    /**
     * 积水形状：0:清 1:脓性 2:血性 3:其他
     */
    @ApiModelProperty(value = "积水形状：0:清 1:脓性 2:血性 3:其他")
    @Min(value = 0, message = "无法识别的积水形状")
    @Max(value = 3, message = "无法识别的积水形状")
    private Integer hydropsShape;

}
