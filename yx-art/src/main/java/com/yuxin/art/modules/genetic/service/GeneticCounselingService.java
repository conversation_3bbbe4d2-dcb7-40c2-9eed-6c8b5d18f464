/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:GeneticCounselingService.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.genetic.service;

import com.yuxin.art.domain.genetic.GeneticCounseling;
import com.yuxin.art.modules.genetic.vo.AddGeneticCounselingVo;
import com.yuxin.art.modules.genetic.vo.EditGeneticCounselingVo;
import com.yuxin.framework.mvc.service.BaseService;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * 遗传咨询service
 *
 * <AUTHOR>
 * @date 2023-07-24
 */
public interface GeneticCounselingService extends BaseService<GeneticCounseling> {

    /**
     * 新增
     *
     * @param addGeneticCounselingVo
     * @return
     */
    Long insert(AddGeneticCounselingVo addGeneticCounselingVo);

    /**
     * 新增
     *
     * @param addGeneticCounselingVo
     * @return
     */
    Long mCreate(AddGeneticCounselingVo addGeneticCounselingVo);

    /**
     * 编辑
     *
     * @param editGeneticCounselingVo
     */
    void update(EditGeneticCounselingVo editGeneticCounselingVo);

    /**
     * 删除
     *
     * @param id
     */
    void mDelete(Long id);

    /**
     * 遗传咨询打印
     *
     * @param id
     * @param path
     * @param printMode
     * @return
     */
    ByteArrayOutputStream exportPdf(Long id, String path, int printMode);

    List<GeneticCounseling> getListByCycleIdAndPatientId(Long cycleId, Long patientId);
}
