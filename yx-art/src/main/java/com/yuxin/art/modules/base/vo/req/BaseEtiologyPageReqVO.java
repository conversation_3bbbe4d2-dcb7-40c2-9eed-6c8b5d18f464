/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:BaseEtiologyPageReqVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.base.vo.req;

import com.yuxin.framework.mvc.vo.PageParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 病因类型资料分页查询
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BaseEtiologyPageReqVO", description = "病因类型资料分页查询")
public class BaseEtiologyPageReqVO extends PageParams {

    /**
     * 病因名称
     */
    @ApiModelProperty(value = "病因名称")
    private String name;

    /**
     * 病因编码
     */
    @ApiModelProperty(value = "病因编码")
    private String code;

    /**
     * 类型.0:原发;1:继发;2:不明原因
     */
    @ApiModelProperty(value = "类型.0:原发;1:继发;2:不明原因")
    private Integer type;
    /**
     * 适用性别.0:不限;1:男;2:女
     */
    private Integer genderScope;

    /**
     * 是否可用.0:否;1:是
     */
    @ApiModelProperty(value = "是否可用.0:否;1:是")
    private Integer status;
}