/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:FollowUpContinuedPregnancyServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.followup.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuxin.api.ResultEnum;
import com.yuxin.art.cache.base.BaseDataPrintCache;
import com.yuxin.art.constant.KArtConstants;
import com.yuxin.art.domain.art.cycle.ArtCycle;
import com.yuxin.art.domain.followup.FollowUp;
import com.yuxin.art.domain.followup.FollowUpContinuedPregnancy;
import com.yuxin.art.domain.sys.SysUser;
import com.yuxin.art.modules.auth.vo.AuthUser;
import com.yuxin.art.modules.followup.mapper.FollowUpContinuedPregnancyMapper;
import com.yuxin.art.modules.followup.service.FollowUpContinuedPregnancyService;
import com.yuxin.art.modules.followup.service.FollowUpProcessService;
import com.yuxin.art.modules.followup.service.FollowUpService;
import com.yuxin.art.modules.followup.service.FollowUpTraceService;
import com.yuxin.art.modules.print.PrintConstants;
import com.yuxin.art.modules.print.util.SignPrintUtil;
import com.yuxin.art.modules.sys.mapper.SysUserMapper;
import com.yuxin.art.modules.utils.PrintUtils;
import com.yuxin.constant.GlobalConstant;
import com.yuxin.framework.exception.BusinessException;
import com.yuxin.framework.mvc.mapper.BaseMapper;
import com.yuxin.framework.mvc.service.impl.BaseServiceImpl;
import com.yuxin.security.util.AuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.util.*;

/**
 * <p>
 * 继续妊娠随访详情 ServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-07
 */
@Service
@Slf4j
public class FollowUpContinuedPregnancyServiceImpl extends BaseServiceImpl<FollowUpContinuedPregnancy> implements FollowUpContinuedPregnancyService {
    @Autowired
    private FollowUpContinuedPregnancyMapper followUpContinuedPregnancyMapper;
    @Autowired
    private FollowUpService followUpService;
    @Autowired
    private BaseDataPrintCache baseDataPrintCache;
    @Value("${yuxin.oss.ftp.host}")
    private String imgUrl;
    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private FollowUpProcessService processService;
    @Autowired
    private FollowUpTraceService followUpTraceService;

    @Override
    protected BaseMapper<FollowUpContinuedPregnancy> getMapper() {
        return followUpContinuedPregnancyMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertFollowUpContinuedPregnancy(FollowUpContinuedPregnancy entity, Integer isAdd) {
        entity.setRecorderId(AuthUtil.getCurrentAuthInfo().getUserId());
        entity.setRecorderName(AuthUtil.getCurrentAuthInfo().getName());
        entity.setRecorderTime(new Date());
        FollowUp followUp = followUpService.get(entity.getFollowupId());
        entity.setCycleId(followUp.getCycleId());
        // hasItem(domain.getCycleId(), domain.getFollowerDate());
        if (StrUtil.isEmpty(followUp.getTransplantDate())) {
            throw new BusinessException(ResultEnum.FAILED.getCode(), "项目未进行移植，无法进行操作");
        }
        // domain.setGestationalWeek(followUpService.calcGestationalWeek(followUp, domain.getCheckDate()));
        if (StrUtil.isNotEmpty(entity.getProcess())) {
            followUp.setHasProcess(GlobalConstant.YES);
        } else {
            followUp.setHasProcess(GlobalConstant.NO);
        }
        followUp.setStatus(KArtConstants.FollowupStatus.FOLLOWEDUP);
        followUp.setIsFollowup(GlobalConstant.YES);
        if (GlobalConstant.NO == isAdd) {
            followUpService.update(followUp);
        } else {
            followUp.setAppointmentDate(null);
            followUp.setType(1);
            followUp.setItemIdentifier(KArtConstants.FollowupItem.PREGNANCY);
            followUp.setRecorderId(AuthUtil.getCurrentAuthInfo().getUserId());
            followUp.setRecorderName(((AuthUser)AuthUtil.getCurrentAuthInfo().getUserInfo()).getName());
            followUp.setRecorderTime(new Date());
            followUp.setBillingId(AuthUtil.getCurrentAuthInfo().getUserId());
            followUp.setBillingName(((AuthUser)AuthUtil.getCurrentAuthInfo().getUserInfo()).getName());
            followUp.setBillingTime(new Date());
            // followUp.setId(KeyGenerate.generateId());
            followUp.setId(null);
            Long followId = followUpService.insert(followUp);
            entity.setFollowupId(followId);
        }
        Long id = super.insert(entity);
        // 添加跟踪记录
        followUpTraceService.createFollowUpTrace(entity, KArtConstants.OperationType.ADD);
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(FollowUpContinuedPregnancy entity) {
        FollowUpContinuedPregnancy followUpContinuedPregnancy = followUpContinuedPregnancyMapper.selectById(entity.getId());
        if (null != followUpContinuedPregnancy) {
            /*if (!DateUtil.isSameDay(followUpContinuedPregnancy.getFollowerDate(), domain.getFollowerDate())) {
                hasItem(followUpContinuedPregnancy.getCycleId(), domain.getFollowerDate());
            }*/
            entity.setRecorderId(AuthUtil.getCurrentAuthInfo().getUserId());
            entity.setRecorderName(AuthUtil.getCurrentAuthInfo().getName());
            entity.setRecorderTime(new Date());
            FollowUp followUp = followUpService.get(followUpContinuedPregnancy.getFollowupId());
            /*if (!DateUtil.isSameDay(followUpContinuedPregnancy.getCheckDate(), domain.getCheckDate())) {
                domain.setGestationalWeek(followUpService.calcGestationalWeek(followUp, domain.getCheckDate()));
            }*/
            if (StrUtil.isNotEmpty(entity.getProcess())) {
                followUp.setHasProcess(GlobalConstant.YES);
            } else {
                followUp.setHasProcess(GlobalConstant.NO);
            }
            followUpService.update(followUp);
            super.update(entity);
            // 添加跟踪记录
            followUpTraceService.createFollowUpTrace(entity, KArtConstants.OperationType.UPDATE);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        FollowUpContinuedPregnancy followUpContinuedPregnancy = followUpContinuedPregnancyMapper.selectById(id);
        if (null != followUpContinuedPregnancy) {
            FollowUp followUp = followUpService.get(followUpContinuedPregnancy.getFollowupId());
            if (ObjectUtil.isNull(followUp.getAppointmentDate())) {
                followUpService.delete(followUp.getId());
            } else {
                followUp.setStatus(KArtConstants.FollowupStatus.NONE);
                followUp.setHasProcess(GlobalConstant.NO);
                followUp.setIsFollowup(GlobalConstant.NO);
                followUpService.update(followUp);
            }
            super.delete(id);
            // 添加跟踪记录
            followUpTraceService.createFollowUpTrace(followUpContinuedPregnancy, KArtConstants.OperationType.DELETE);
        }
    }

    @Override
    public List<FollowUpContinuedPregnancy> getListByCycleId(Long cycleId) {
        return followUpContinuedPregnancyMapper.selectList(
            Wrappers.lambdaQuery(FollowUpContinuedPregnancy.class).eq(FollowUpContinuedPregnancy::getCycleId, cycleId)
                .orderByAsc(FollowUpContinuedPregnancy::getFollowerDate));
    }

    private void hasItem(Long cycleId, Date followUpDate) {
        FollowUpContinuedPregnancy followUpContinuedPregnancy = followUpContinuedPregnancyMapper.selectOne(
            Wrappers.lambdaQuery(FollowUpContinuedPregnancy.class).eq(FollowUpContinuedPregnancy::getCycleId, cycleId)
                .eq(FollowUpContinuedPregnancy::getFollowerDate, DateUtil.beginOfDay(followUpDate)));
        if (null != followUpContinuedPregnancy) {
            throw new BusinessException(ResultEnum.FAILED.getCode(), "同一个项目当天只能随访一次，无法进行操作");
        }
    }

    @Override
    public ByteArrayOutputStream exportPdf(ArtCycle artCycle, String fileName, int printMode) {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("treatmentPlanInt", 0);

            List<FollowUpContinuedPregnancy> continuedPregnancys = this.getListByCycleId(artCycle.getId());
            if (CollectionUtil.isNotEmpty(continuedPregnancys)) {
                List<Map<String, Object>> continuedPregnancyList = new ArrayList<>();
                continuedPregnancys.forEach(continuedPregnancyVo -> {
                    Map<String, Object> continuedPregnancy = new HashMap<>();
                    if (NumberUtil.isNumber(continuedPregnancyVo.getGestationalWeek())) {
                        Integer gestationalWeek = NumberUtil.parseInt(continuedPregnancyVo.getGestationalWeek());
                        int week = gestationalWeek / 7;
                        int day = gestationalWeek % 7;
                        continuedPregnancy.put("gestationalWeek", week + "周" + day + "天");
                    } else {
                        continuedPregnancy.put("gestationalWeek", continuedPregnancyVo.getGestationalWeek());
                    }
                    continuedPregnancy.put("checkDate", DateUtil.format(continuedPregnancyVo.getCheckDate(), DatePattern.NORM_DATE_FORMAT));
                    continuedPregnancy.put("followerDate", DateUtil.format(continuedPregnancyVo.getFollowerDate(), DatePattern.NORM_DATE_FORMAT));
                    continuedPregnancy.put("followerName", continuedPregnancyVo.getFollowerName());
                    String isContinuedGestation =
                        baseDataPrintCache.getByCodeAndValue("isContinuedGestation", String.valueOf(continuedPregnancyVo.getIsContinuedGestation()));
                    continuedPregnancy.put("isContinuedGestation", isContinuedGestation);
                    continuedPregnancy.put("situation", continuedPregnancyVo.getSituation());
                    continuedPregnancy.put("process", continuedPregnancyVo.getProcess());
                    continuedPregnancy.put("recorderName", continuedPregnancyVo.getRecorderName());
                    continuedPregnancy.put("recorderTime",
                        DateUtil.format(continuedPregnancyVo.getRecorderTime(), DatePattern.NORM_DATETIME_MINUTE_FORMAT));

                    if (continuedPregnancyVo.getRecorderId() != null) {
                        SysUser sysUser = userMapper.selectById(continuedPregnancyVo.getRecorderId());
                        if (sysUser != null) {
                            SignPrintUtil.putUserSignToMap(continuedPregnancy, "doctorSign", sysUser, imgUrl);
                        }
                    }

                    continuedPregnancyList.add(continuedPregnancy);
                });

                // 处理
                List<Map<String, Object>> processes = processService.getPrintableProcess(artCycle.getId(), KArtConstants.FollowupItem.PREGNANCY);
                if (CollUtil.isNotEmpty(processes)) {
                    map.put("processes", processes);
                }

                map.put("continuedPregnancyList", continuedPregnancyList);
                map.putAll(PrintUtils.getCycleBasicData(artCycle));
                map.putAll(followUpService.getPrintBasicData(artCycle));
                return PrintUtils.printPdf(fileName, map);
            } else {
                if (printMode == PrintConstants.PrintMode.STRICT_MODE) {
                    // 严格输出模式
                    throw new BusinessException(ResultEnum.FAILED.getCode(), "没有随访记录");
                } else if (printMode == PrintConstants.PrintMode.DEFAULT_IF_NULL) {
                    // 打印空白手术
                    map.putAll(PrintUtils.getCycleBasicData(artCycle));
                    return PrintUtils.printPdf(fileName, map);
                } else {
                    // 跳过，返回NULL
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("打印PDF异常", e);
            throw new BusinessException(ResultEnum.FAILED.getCode(), e.getMessage());
        }
    }
}
