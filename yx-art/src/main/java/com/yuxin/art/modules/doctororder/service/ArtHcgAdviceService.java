/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ArtHcgAdviceService.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.doctororder.service;

import com.yuxin.art.domain.doctororder.ArtHcgAdvice;
import com.yuxin.art.modules.doctororder.vo.EditArtHcgAdviceVO;
import com.yuxin.art.modules.doctororder.vo.EditArtHcgVO;
import com.yuxin.art.modules.doctororder.vo.HcgAdviceTotalVO;
import com.yuxin.framework.mvc.service.BaseService;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * HCG医嘱 Service
 *
 * <AUTHOR>
 * @since 2020-12-11
 */
public interface ArtHcgAdviceService extends BaseService<ArtHcgAdvice> {

    /**
     * 通过周期id和Hcg时间查询Hcg医嘱
     *
     * @param cycleId
     * @param hcgTime
     * @return
     */
    ArtHcgAdvice getByCycleIdAndHcgTime(Long cycleId, Date hcgTime);

    /**
     * 通过周期和取卵时间
     *
     * @param cycleId
     * @param eggArtificialTime
     * @return
     */
    ArtHcgAdvice getByCycleIdAndEggArtificialTime(Long cycleId, Date eggArtificialTime);

    /**
     * 获取取卵手术之前的hcg代办
     * @param cycleId
     * @param hcgTime
     * @return
     */
    // List<ArtHcgAdvice> getListByCycleIdAndBeforeHcgTime(Long cycleId, Date hcgTime);

    /**
     * 根据周期获取hcg医嘱列表
     *
     * @param cycleId
     * @return
     */
    List<Date> getHcgDateList(Long cycleId);

    List<Date> getConfirmHcgDateList(Long cycleId);

    /**
     * 确认hcg医嘱
     *
     * @param editArtHcgAdviceVo
     */
    @Transactional(rollbackFor = Exception.class)
    void executeHcg(EditArtHcgAdviceVO editArtHcgAdviceVo);

    /**
     * 根据周期查询hcg医嘱
     *
     * @param cycleId
     * @return
     */
    List<ArtHcgAdvice> getListByCycleId(Long cycleId);

    /**
     * 根据取卵时间查询
     *
     * @param eggArtificialTime
     * @return
     */
    List<HcgAdviceTotalVO> getListByEggArtificialDate(Date eggArtificialTime);

    /**
     * 根据扳机时间查询人数
     *
     * @param hcgTime
     * @return
     */
    Integer getCountByHcgTime(Date hcgTime);

    void executeHcgByCycle(EditArtHcgVO editArtHcgVO);
}
