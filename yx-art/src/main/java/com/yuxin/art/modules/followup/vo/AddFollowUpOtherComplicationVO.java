/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:AddFollowUpOtherComplicationVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 其他并发症随访详情
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-07
 */
@Data
@NoArgsConstructor
@ApiModel(value = "AddFollowUpOtherComplicationVO", description = "新增其他并发症随访详情")
public class AddFollowUpOtherComplicationVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 随访主键ID
     */
    @ApiModelProperty(value = "随访主键ID", required = true)
    @NotNull(message = "随访主键ID不能为空")
    private Long followupId;
    /**
     * 发生日期
     */
    @ApiModelProperty(value = "发生日期", required = true)
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @NotNull(message = "发生日期不能为空")
    private Date occurrenceDate;
    /**
     * 并发症名称
     */
    @ApiModelProperty(value = "并发症名称(50)", required = true)
    @NotBlank(message = "并发症名称不能为空")
    @Length(max = 50, message = "并发症名称不能超过50个字符")
    private String name;
    /**
     * 并发症详情
     */
    @ApiModelProperty(value = "并发症详情(500)")
    @Length(max = 500, message = "并发症详情不能超过500个字符")
    private String details;
    /**
     * 随访人ID
     */
    @ApiModelProperty(value = "随访人ID", required = true)
    @NotNull(message = "随访人ID不能为空")
    private Long followerId;
    /**
     * 随访人姓名
     */
    @ApiModelProperty(value = "随访人姓名(50)", required = true)
    @NotBlank(message = "随访人姓名不能为空")
    @Length(max = 50, message = "随访人姓名不能超过50个字符")
    private String followerName;
    /**
     * 随访日期
     */
    @ApiModelProperty(value = "随访日期", required = true)
    @NotNull(message = "随访日期不能为空")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date followerDate;
    /**
     * 处理
     */
    @ApiModelProperty(value = "处理(500)")
    @Length(max = 500, message = "处理不能超过500个字符")
    private String process;
    /**
     * 是否临时增加随访
     */
    @ApiModelProperty(value = "是否临时增加随访 0.否 1.是", required = true)
    @NotNull(message = "是否临时增加随访不能为空")
    @Range(min = 0, max = 1, message = "无效的是否临时增加随访")
    private Integer isAdd;
}
