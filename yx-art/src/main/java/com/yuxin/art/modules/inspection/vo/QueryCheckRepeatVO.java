/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:QueryCheckRepeatVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.inspection.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 检查报告是否重复
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Data
@NoArgsConstructor
@ApiModel(value = "QueryCheckRepeatVO", description = "检查报告是否重复")
public class QueryCheckRepeatVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @ApiModelProperty(value = "ID,编辑时必传", required = false)
    public Long id;
    /**
     * 患者主键ID
     */
    @ApiModelProperty(value = "患者主键ID", required = true)
    @NotNull(message = "患者主键ID不能为空")
    private Long patientId;
    /**
     * 项目主键ID
     */
    @ApiModelProperty(value = "项目主键ID", required = true)
    @NotNull(message = "项目主键ID不能为空")
    private Long itemId;
    /**
     * 报告日期
     */
    @ApiModelProperty(value = "报告日期", required = true)
    @NotBlank(message = "报告日期不能为空")
    private String reportDate;
}
