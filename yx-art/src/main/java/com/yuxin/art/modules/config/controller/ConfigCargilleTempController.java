/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ConfigCargilleTempController.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.config.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.yuxin.api.Result;
import com.yuxin.art.domain.config.ConfigCargilleDetailTemp;
import com.yuxin.art.domain.config.ConfigCargilleTemp;
import com.yuxin.art.modules.config.service.ConfigCargilleDetailTempService;
import com.yuxin.art.modules.config.service.ConfigCargilleTempService;
import com.yuxin.art.modules.config.vo.SaveCargilleTempVo;
import com.yuxin.framework.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 配液模板 Controller
 *
 * <AUTHOR>
 * @date 2022-03-15
 */
@RestController
@RequestMapping(value = "/config/cargilleTemp", produces = MediaType.APPLICATION_JSON_VALUE)
@Validated
@Api(tags = "配液模板")
@ApiSort(1)
public class ConfigCargilleTempController extends BaseController {
    @Autowired
    private ConfigCargilleTempService configCargilleTempService;
    @Autowired
    private ConfigCargilleDetailTempService configCargilleDetailTempService;

    /**
     * 列表查询
     *
     * @return
     */
    @GetMapping("/getList")
    @ApiOperation(value = "列表查询")
    @ApiOperationSupport(order = 1)
    // @RequiresPermissions("config:cargilleTemp:getList")
    public Result<List<ConfigCargilleTemp>> getList() {
        List<ConfigCargilleTemp> list = configCargilleTempService.getList();
        return Result.success(list);
    }

    /**
     * 配液模板详情查询
     *
     * @return
     */
    @GetMapping("/getDetailList")
    @ApiOperation(value = "配液详情查询")
    @ApiOperationSupport(order = 2)
    @ApiImplicitParam(name = "cargilleListId", value = "cargilleListId", required = true, paramType = "query", dataType = "String")
    // @RequiresPermissions("config:cargilleTemp:getDetailList")
    public Result<List<ConfigCargilleDetailTemp>> getDetailList(@NotNull(message = "cargilleListId不能为空") Long cargilleListId) {
        List<ConfigCargilleDetailTemp> list = configCargilleDetailTempService.getListByCargilleListId(cargilleListId);
        return Result.success(list);
    }

    /**
     * 配液模板详情保存
     *
     * @param saveCargilleTempVo
     * @return
     */
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "配液模板详情保存", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 3)
    // @RequiresPermissions("config:cargilleTemp:save")
    public Result<Void> save(@RequestBody @Validated SaveCargilleTempVo saveCargilleTempVo) {
        configCargilleDetailTempService.mSave(saveCargilleTempVo);
        return Result.success();
    }

}
