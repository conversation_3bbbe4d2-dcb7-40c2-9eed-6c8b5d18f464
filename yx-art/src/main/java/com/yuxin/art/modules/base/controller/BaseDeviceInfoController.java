/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:BaseDeviceInfoController.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.base.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.yuxin.api.Result;
import com.yuxin.art.domain.base.BaseDeviceInfo;
import com.yuxin.art.modules.base.service.BaseDeviceInfoService;
import com.yuxin.art.modules.base.vo.req.BaseDeviceInfoSaveReqVO;
import com.yuxin.framework.mvc.controller.BaseController;
import com.yuxin.framework.mvc.vo.IdVO;
import com.yuxin.validation.ValidateGroup;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <p>
 * 设备信息 Controller
 * TODO 后期重构时,需要重构依赖关系,设备应该是底层信息,不应该处理上层业务逻辑,应该交由业务自己处理,设备只处理自己本身的绑定,解绑逻辑
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-22
 */
@RestController
@RequestMapping(value = "/base/deviceInfo", produces = MediaType.APPLICATION_JSON_VALUE)
@Validated
@Api(tags = "设备信息")
@ApiSort(1)
public class BaseDeviceInfoController extends BaseController {
    @Autowired
    private BaseDeviceInfoService baseDeviceInfoService;

    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "新增", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 1)
    // @RequiresPermissions("base:deviceInfo:create")
    public Result<IdVO> create(@Validated(ValidateGroup.Create.class) @RequestBody BaseDeviceInfoSaveReqVO deviceInfoSaveReqVO) {
        BaseDeviceInfo baseDeviceInfo = baseDeviceInfoService.createDeviceInfo(deviceInfoSaveReqVO);
        return Result.success(new IdVO(baseDeviceInfo.getId()));
    }

    @PutMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "修改状态", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 2)
    // @RequiresPermissions("base:deviceInfo:edit")
    public Result<Void> edit(@Validated(ValidateGroup.Update.class) @RequestBody BaseDeviceInfoSaveReqVO deviceInfoSaveReqVO) {
        baseDeviceInfoService.updateDeviceInfo(deviceInfoSaveReqVO);
        return Result.success();
    }

    @GetMapping("/getDeviceInfo")
    @ApiOperation(value = "查询设备绑定信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "bizId", value = "取卵ID", paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "relatedId", value = "捡卵ID", paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "deviceNo", value = "设备号", required = true, paramType = "query", dataType = "String")})
    @ApiOperationSupport(order = 3)
    public Result<BaseDeviceInfo> getDeviceInfo(Long bizId, Long relatedId, @NotBlank(message = "设备号不能为空") String deviceNo) {
        BaseDeviceInfo baseDeviceInfo = baseDeviceInfoService.getDeviceInfo(bizId, relatedId, deviceNo);
        return Result.success(baseDeviceInfo);
    }

    @GetMapping("/getEggInfo")
    @ApiOperation(value = "查询取卵或捡卵关联信息")
    @ApiImplicitParams({@ApiImplicitParam(name = "todoId", value = "待办ID", paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "type", value = "类型 0-捡卵 1-取卵 2-病程记录", paramType = "query", dataType = "Integer")})
    @ApiOperationSupport(order = 3)
    public Result<Map<String, Object>> getEggInfo(@NotNull(message = "待办Id不能为空") Long todoId, @NotNull(message = "类型不能为空") Integer type) {
        Map<String, Object> map = baseDeviceInfoService.getEggInfo(todoId, type);
        return Result.success(map);
    }
}
