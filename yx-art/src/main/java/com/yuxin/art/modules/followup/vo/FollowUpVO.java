/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:FollowUpVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.followup.vo;

import com.yuxin.art.domain.followup.FollowUp;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022-10-28
 */
@Data
@NoArgsConstructor
@ApiModel(value = "FollowUpVO", description = "随访实体类")
public class FollowUpVO extends FollowUp {
    private String teamName;
}
