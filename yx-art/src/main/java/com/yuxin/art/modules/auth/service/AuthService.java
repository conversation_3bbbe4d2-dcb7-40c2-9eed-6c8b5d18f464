/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:AuthService.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.auth.service;

import com.yuxin.art.domain.sys.SysUser;
import com.yuxin.art.modules.auth.vo.CheckSingleUserVO;
import com.yuxin.art.modules.auth.vo.CheckThreeUserVO;
import com.yuxin.art.modules.auth.vo.CheckUserVO;
import com.yuxin.art.modules.auth.vo.UsernamePasswordVO;
import com.yuxin.art.modules.patient.vo.CheckUserRespVO;
import com.yuxin.art.modules.sys.vo.rsp.SysUserPermissionRspVO;
import com.yuxin.domain.AuthInfo;
import com.yuxin.framework.mvc.service.BaseService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-05-15
 */
public interface AuthService extends BaseService<SysUser> {
    /**
     * 登录
     *
     * @param authUsernamePasswordVO
     * @return
     */
    AuthInfo login(UsernamePasswordVO authUsernamePasswordVO);

    /**
     * 获取菜单的下级资源
     *
     * @param menuId
     * @return
     */
    List<SysUserPermissionRspVO> getMenuChildren(Long menuId);

    /**
     * 校验用户（双人）
     *
     * @param checkUserVO
     * @return
     */
    List<CheckUserRespVO> checkUser(CheckUserVO checkUserVO);

    /**
     * 校验用户（三人）
     *
     * @param checkUserVO
     * @return
     */
    List<CheckUserRespVO> checkThreeUser(CheckThreeUserVO checkUserVO);

    /**
     * 校验用户（单人）
     *
     * @param reviewerVO
     * @return
     */
    CheckUserRespVO checkSingleUser(CheckSingleUserVO reviewerVO);

    /**
     * 获取菜单的下级路由
     *
     * @param menuId
     * @return
     */
    List<String> getMenuRouting(Long menuId, Long parentId);
}
