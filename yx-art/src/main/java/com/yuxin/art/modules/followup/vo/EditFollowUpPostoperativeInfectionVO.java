/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:EditFollowUpPostoperativeInfectionVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 术后感染随访详情
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-05
 */
@Data
@NoArgsConstructor
@ApiModel(value = "EditFollowUpPostoperativeInfectionVO", description = "编辑术后感染随访详情")
public class EditFollowUpPostoperativeInfectionVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @ApiModelProperty(value = "ID", required = true)
    @NotNull(message = "id不能为空")
    public Long id;
    /**
     * 感染日期
     */
    @ApiModelProperty(value = "感染日期", required = true)
    @NotNull(message = "感染日期不能为空")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date infectionDate;
    /**
     * 并发症详情
     */
    @ApiModelProperty(value = "并发症详情(500)")
    @Length(max = 500, message = "并发症详情不能超过500个字符")
    private String details;
    /**
     * 随访人ID
     */
    @ApiModelProperty(value = "随访人ID", required = true)
    @NotNull(message = "随访人ID不能为空")
    private Long followerId;
    /**
     * 随访人姓名
     */
    @ApiModelProperty(value = "随访人姓名(50)", required = true)
    @NotBlank(message = "随访人姓名不能为空")
    @Length(max = 50, message = "随访人姓名不能超过50个字符")
    private String followerName;
    /**
     * 随访日期
     */
    @ApiModelProperty(value = "随访日期", required = true)
    @NotNull(message = "随访日期不能为空")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date followerDate;
    /**
     * 处理
     */
    @ApiModelProperty(value = "处理(500)")
    @Length(max = 500, message = "处理不能超过500个字符")
    private String process;
}
