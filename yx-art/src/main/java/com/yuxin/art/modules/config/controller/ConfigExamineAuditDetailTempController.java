/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ConfigExamineAuditDetailTempController.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.config.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.yuxin.api.Result;
import com.yuxin.art.modules.config.service.ConfigExamineAuditDetailsTemplateService;
import com.yuxin.art.modules.config.vo.ConfigExamineAuditDetailTempVO;
import com.yuxin.art.modules.config.vo.ExamineShuttleBoxVO;
import com.yuxin.framework.mvc.controller.BaseController;
import com.yuxin.framework.mvc.vo.IdsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 检验单审核配置详情 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-03
 */
@RestController
@RequestMapping(value = "/config/examineAuditDetailTemp", produces = MediaType.APPLICATION_JSON_VALUE)
@Validated
@Api(tags = "检验单审核配置详情")
@ApiSort(1)
public class ConfigExamineAuditDetailTempController extends BaseController {
    @Autowired
    private ConfigExamineAuditDetailsTemplateService configExamineAuditDetailsTemplateService;

    /**
     * 批量删除
     *
     * @param ids id集合
     */
    @DeleteMapping(value = "/mDelete", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "批量删除", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 2)
    //@RequiresPermissions("config:examineAuditDetailTemp:mDelete")
    public Result<Void> mDelete(@RequestBody @Validated IdsVO ids) {
        configExamineAuditDetailsTemplateService.delete(ids.getIds());
        return Result.success();
    }

    /**
     * 获取穿梭框信息
     *
     * @return
     */
    @GetMapping("/getShuttleList")
    @ApiOperation(value = "获取穿梭框信息")
    @ApiOperationSupport(order = 3)
    //@RequiresPermissions("config:examineAuditDetailTemp:getShuttleList")
    public Result<List<ConfigExamineAuditDetailTempVO>> getShuttleList(@Validated ExamineShuttleBoxVO param) {
        List<ConfigExamineAuditDetailTempVO> list =
            configExamineAuditDetailsTemplateService.getExamineShuttleInfo(param.getExamineAuditConfigId(), param.getReportTypeId());
        return Result.success(list);
    }

}
