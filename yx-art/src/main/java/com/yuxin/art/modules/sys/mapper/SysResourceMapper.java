/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:SysResourceMapper.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.sys.mapper;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuxin.art.domain.sys.SysResource;
import com.yuxin.art.domain.sys.SysRoleResource;
import com.yuxin.art.domain.sys.SysUserRole;
import com.yuxin.art.modules.sys.vo.rsp.SysUserPermissionRspVO;
import com.yuxin.framework.mvc.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 资源管理 Mapper
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-22
 */
@Mapper
public interface SysResourceMapper extends BaseMapper<SysResource> {

    default MPJLambdaWrapper<SysResource> buildQueryWrapper() {
        MPJLambdaWrapper<SysResource> wrapper = new MPJLambdaWrapper<>();
        wrapper.select(SysResource::getId);
        wrapper.select(SysResource::getName);
        wrapper.select(SysResource::getCode);
        wrapper.select(SysResource::getClientRoute);
        wrapper.select(SysResource::getClientRouteUrl);
        wrapper.select(SysResource::getClientOpenMode);
        wrapper.select(SysResource::getResourceUrl);
        wrapper.select(SysResource::getMethod);
        wrapper.select(SysResource::getType);
        wrapper.select(SysResource::getIcon);
        wrapper.select(SysResource::getSort);
        wrapper.select(SysResource::getRemark);
        wrapper.select(SysResource::getParentId);
        return wrapper;
    }

    /**
     * 根据用户ID获取资源集合
     */
    default List<SysUserPermissionRspVO> selectByUserId(Long userId) {
        MPJLambdaWrapper<SysResource> wrapper = buildQueryWrapper();
        wrapper.distinct();
        wrapper.leftJoin(SysRoleResource.class, SysRoleResource::getResourceId, SysResource::getId);
        wrapper.leftJoin(SysUserRole.class, SysUserRole::getRoleId, SysRoleResource::getRoleId);
        wrapper.eq(SysUserRole::getUserId, userId);
        wrapper.orderByAsc(SysResource::getSort);
        return selectJoinList(SysUserPermissionRspVO.class, wrapper);
    }

    /**
     * 根据管理员账号获取资源集合
     */
    default List<SysUserPermissionRspVO> selectByAdmin() {
        MPJLambdaWrapper<SysResource> wrapper = new MPJLambdaWrapper<>();
        wrapper.distinct();
        wrapper.selectAsClass(SysResource.class, SysUserPermissionRspVO.class);
        wrapper.orderByAsc(SysResource::getSort);
        return selectJoinList(SysUserPermissionRspVO.class, wrapper);
    }

    /**
     * 根据资源code获取资源
     */
    default SysResource selectByCode(String code) {
        return selectOne(SysResource::getCode, code);
    }

    /**
     * 根据父资源ID获取子资源数量
     */
    default Long selectCountByParentId(Long parentId) {
        return selectCount(SysResource::getParentId, parentId);
    }

}
