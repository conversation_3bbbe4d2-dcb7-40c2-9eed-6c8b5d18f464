/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:SysRoleSaveReqVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.sys.vo.req;

import com.yuxin.validation.ValidateGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 角色管理
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-22
 */
@Data
@NoArgsConstructor
@ApiModel(value = "SysRoleSaveReqVO", description = "新增角色")
public class SysRoleSaveReqVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    @NotNull(message = "ID不能为空", groups = {ValidateGroup.Update.class})
    protected Long id;
    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称", required = true)
    @NotBlank(message = "角色名称不能为空")
    @Length(max = 50, message = "角色名称不能超过50个字符")
    private String name;
    /**
     * 角色编码
     */
    @ApiModelProperty(value = "角色编码", required = true)
    @NotBlank(message = "角色编码不能为空")
    @Length(max = 20, message = "角色编码不能超过20个字符")
    private String code;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Length(max = 200, message = "备注不能超过200个字符")
    private String remark;
    /**
     * 资源ID集合
     */
    @ApiModelProperty(value = "资源ID集合")
    private List<Long> resourceIds;

}
