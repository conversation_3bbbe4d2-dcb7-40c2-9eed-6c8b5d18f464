/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:EditArtIdentityCheckResultsVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */

package com.yuxin.art.modules.audit.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 身份核对
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-3
 */
@Data
@NoArgsConstructor
@ApiModel(value = "EditArtIdentityCheckResultsVO", description = "编辑身份核对结果")
public class EditArtIdentityCheckResultsVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @ApiModelProperty(value = "ID集合(以英文,号隔开)", required = true)
    @NotBlank(message = "id集合不能为空")
    public String ids;
    /**
     * 核对类型 0：特殊手术核对 1：取精核对 2：取卵手术核对 3：人工授精核对 4：移植核对
     */
    @ApiModelProperty(value = "核对类型 0：特殊手术核对 1：取精核对 2：取卵手术核对 3：人工授精核对 4：移植核对", required = true)
    @NotNull(message = "核对类型不能为空")
    @Min(value = 0, message = "无法识别的核对类型")
    @Max(value = 99, message = "无法识别的核对类型")
    private Integer checkType;
    /**
     * 核对结果 0:通过 1:不通过
     */
    @ApiModelProperty(value = "核对结果 0:通过 1:不通过", required = true)
    @NotNull(message = "核对结果不能为空")
    @Min(value = 0, message = "无法识别的核对结果")
    @Max(value = 1, message = "无法识别的核对结果")
    private Integer isCheck;

    /**
     * 核对类型 0:其他 1：取精核对 2:取卵核对 3:移植核对 4：人工授精核对
     */
    @ApiModelProperty(value = "核对类型 0:其他 1：取精核对 2:取卵核对 3:移植核对 4：人工授精核对", required = true)
    @NotNull(message = "核对类型不能为空")
    @Min(value = 0, message = "无法识别的核对类型")
    @Max(value = 4, message = "无法识别的核对类型")
    private Integer type;
}
