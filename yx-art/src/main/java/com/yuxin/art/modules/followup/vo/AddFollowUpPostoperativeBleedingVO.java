/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:AddFollowUpPostoperativeBleedingVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 术后出血随访详情
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-07
 */
@Data
@NoArgsConstructor
@ApiModel(value = "AddFollowUpPostoperativeBleedingVO", description = "新增术后出血随访详情")
public class AddFollowUpPostoperativeBleedingVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 随访主键ID
     */
    @ApiModelProperty(value = "随访主键ID", required = true)
    @NotNull(message = "随访主键ID不能为空")
    private Long followupId;
    /**
     * 发生日期
     */
    @ApiModelProperty(value = "发生日期", required = true)
    @NotNull(message = "发生日期不能为空")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date occurrenceDate;
    /**
     * 出血量
     */
    @ApiModelProperty(value = "出血量(0-99)", required = true)
    @NotNull(message = "出血量不能为空")
    @Min(value = 0, message = "无法识别的出血量")
    @Max(value = 99, message = "无法识别的出血量")
    private Integer bleedingVolume;
    /**
     * 出血天数
     */
    @ApiModelProperty(value = "出血天数（0-99）")
    @Min(value = 0, message = "无法识别的出血天数")
    @Max(value = 99, message = "无法识别的出血天数")
    private Integer bleedingDay;
    /**
     * 并发症详情
     */
    @ApiModelProperty(value = "并发症详情(500)")
    @Length(max = 500, message = "并发症详情不能超过500个字符")
    private String details;
    /**
     * 随访人ID
     */
    @ApiModelProperty(value = "随访人ID", required = true)
    @NotNull(message = "随访人ID不能为空")
    private Long followerId;
    /**
     * 随访人姓名
     */
    @ApiModelProperty(value = "随访人姓名(50)", required = true)
    @NotBlank(message = "随访人姓名不能为空")
    @Length(max = 50, message = "随访人姓名不能超过50个字符")
    private String followerName;
    /**
     * 随访日期
     */
    @ApiModelProperty(value = "随访日期", required = true)
    @NotNull(message = "随访日期不能为空")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date followerDate;
    /**
     * 出血位置 0-盆腔 1-阴道 2-其他 3-膀胱
     */
    @ApiModelProperty(value = "出血位置 0-盆腔 1-阴道 2-其他 3-膀胱", required = true)
    @NotNull(message = "出血位置不能为空")
    @Min(value = 0, message = "无法识别的出血位置")
    @Max(value = 3, message = "无法识别的出血位置")
    private Integer bleedingLocation;
    /**
     * 处理
     */
    @ApiModelProperty(value = "处理(500)")
    @Length(max = 500, message = "处理不能超过500个字符")
    private String process;
    /**
     * 是否临时增加随访
     */
    @ApiModelProperty(value = "是否临时增加随访 0.否 1.是", required = true)
    @NotNull(message = "是否临时增加随访不能为空")
    @Range(min = 0, max = 1, message = "无效的是否临时增加随访")
    private Integer isAdd;
}
