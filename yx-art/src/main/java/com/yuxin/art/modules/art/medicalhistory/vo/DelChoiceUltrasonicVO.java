/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:DelChoiceUltrasonicVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.art.medicalhistory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 删除已选卵泡
 *
 * <AUTHOR>
 * @date 2020/9/17
 */
@Data
@NoArgsConstructor
@ApiModel(value = "DelChoiceUltrasonicVO", description = "删除已选卵泡")
public class DelChoiceUltrasonicVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 周期主键ID
     */
    @ApiModelProperty(value = "周期主键 ID  进周后的artID标识，如果是进周后的在写，否则为空", required = true)
    @NotNull(message = "周期主键ID不能为空")
    private Long cycleId;

    /**
     * ID集合
     */
    @ApiModelProperty(value = "ID集合", required = true)
    @NotNull(message = "ID集合不能为空")
    @Size(min = 1, message = "ID集合不能为空")
    private List<Long> ids;
}
