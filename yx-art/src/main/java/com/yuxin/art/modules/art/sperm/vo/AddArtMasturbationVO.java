/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:AddArtMasturbationVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.art.sperm.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 手淫取精
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-25
 */
@Data
@NoArgsConstructor
@ApiModel(value = "AddArtMasturbationVO", description = "新增手淫取精")
public class AddArtMasturbationVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 周期主键ID
     */
    @ApiModelProperty(value = "周期主键ID")
    private Long cycleId;
    /**
     * 女性患者主键 ID
     */
    @ApiModelProperty(value = "女性患者主键 ID")
    private Long patientFemaleId;
    /**
     * 女性患者ID
     */
    @ApiModelProperty(value = "女性患者ID")
    private String patientFemaleIdentifyId;
    /**
     * 女患者姓名
     */
    @ApiModelProperty(value = "女患者姓名(50)")
    @NotBlank(message = "女患者姓名不能为空")
    @Length(max = 50, message = "女患者姓名不能超过50个字符")
    private String patientFemaleName;
    /**
     * 女性患者卡号
     */
    @ApiModelProperty(value = "女性患者卡号(50)")
    @NotBlank(message = "女性患者卡号不能为空")
    @Length(max = 50, message = "女性患者卡号不能超过50个字符")
    private String patientFemaleCardNo;
    /**
     * 男性患者主键 ID
     */
    @ApiModelProperty(value = "男性患者主键 ID", required = true)
    @NotNull(message = "男性患者主键 ID不能为空")
    private Long patientMaleId;
    /**
     * 男性患者ID
     */
    @ApiModelProperty(value = "男性患者ID(20)", required = true)
    @NotNull(message = "男性患者ID不能为空")
    private String patientMaleIdentifyId;
    /**
     * 男性患者姓名
     */
    @ApiModelProperty(value = "男性患者姓名(50)", required = true)
    @NotBlank(message = "男性患者姓名不能为空")
    @Length(max = 50, message = "男性患者姓名不能超过50个字符")
    private String patientMaleName;
    /**
     * 男性患者卡号
     */
    @ApiModelProperty(value = "男性患者卡号(50)", required = true)
    @NotNull(message = "男性患者卡号不能为空")
    @Length(max = 50, message = "男性患者卡号不能超过50个字符")
    private String patientMaleCardNo;
    /**
     * 取精时间
     */
    @ApiModelProperty(value = "取精时间", required = true)
    @NotNull(message = "取精时间不能为空")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date takeDate;
}
