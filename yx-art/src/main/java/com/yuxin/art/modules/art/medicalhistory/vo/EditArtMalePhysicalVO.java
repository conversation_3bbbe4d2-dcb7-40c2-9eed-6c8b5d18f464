/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:EditArtMalePhysicalVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.art.medicalhistory.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 男患者ART体格检查
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-11
 */
@Data
@NoArgsConstructor
@ApiModel(value = "EditArtMalePhysicalVO", description = "编辑男患者ART体格检查")
public class EditArtMalePhysicalVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @ApiModelProperty(value = "ID", required = true)
    @NotNull(message = "id不能为空")
    public Long id;
    /**
     * 周期主键ID
     */
    @ApiModelProperty(value = "周期主键ID", required = true)
    @NotNull(message = "周期主键ID不能为空")
    private Long cycleId;
    /**
     * 患者主键ID
     */
    @ApiModelProperty(value = "患者主键ID", required = true)
    @NotNull(message = "患者主键ID不能为空")
    private Long patientId;
    /**
     * 一般检查-身高(cm)
     */
    @ApiModelProperty(value = "一般检查-身高(cm)(3,2)", required = true)
    @Digits(integer = 3, fraction = 2, message = "一般检查-身高(cm)只能包含3位整数,2位小数")
    @NotNull(message = "一般检查-身高不能为空")
    @DecimalMin(value = "0.01", message = "一般检查-身高(cm)不能小于等于0")
    private BigDecimal giHeight;
    /**
     * 一般检查-体重（kg）
     */
    @ApiModelProperty(value = "一般检查-体重（kg）(3,2)", required = true)
    @Digits(integer = 3, fraction = 2, message = "一般检查-体重（kg）只能包含3位整数,2位小数")
    @NotNull(message = "一般检查-体重不能为空")
    @DecimalMin(value = "0.01", message = "一般检查-体重（kg）不能小于等于0")
    private BigDecimal giWeight;
    /**
     * 一般检查-指距（cm）
     */
    @ApiModelProperty(value = "一般检查-指距（cm）(3,2)", required = false)
    @Digits(integer = 3, fraction = 2, message = "一般检查-指距（cm）只能包含3位整数,2位小数")
    @DecimalMin(value = "0.01", message = "一般检查-指距（cm）不能小于等于0")
    private BigDecimal giFingerDistance;
    /**
     * 一般检查-收缩压（mmHg）
     */
    @ApiModelProperty(value = "一般检查-收缩压（mmHg）(3,2)", required = true)
    @Digits(integer = 3, fraction = 2, message = "一般检查-收缩压（mmHg）只能包含3位整数,2位小数")
    @NotNull(message = "一般检查-收缩压不能为空")
    @DecimalMin(value = "0.01", message = "一般检查-收缩压（mmHg）不能小于等于0")
    private BigDecimal giSystolicPressure;
    /**
     * 一般检查-扩张压（mmHg）
     */
    @ApiModelProperty(value = "一般检查-扩张压（mmHg）(3,2)", required = true)
    @Digits(integer = 3, fraction = 2, message = "一般检查-扩张压（mmHg）只能包含3位整数,2位小数")
    @NotNull(message = "一般检查-扩张压不能为空")
    @DecimalMin(value = "0.01", message = "一般检查-扩张压（mmHg）不能小于等于0")
    private BigDecimal giExpansionPressure;
    /**
     * 一般检查-上下身比
     */
    @ApiModelProperty(value = "一般检查-上下身比(3,2)")
    @Digits(integer = 3, fraction = 2, message = "一般检查-上下身比只能包含3位整数,2位小数")
    @DecimalMin(value = "0.01", message = "一般检查-上下身比不能小于等于0")
    private BigDecimal giBodyRatio;
    /**
     * 第二性征-喉结：正常1、异常-1、未查0；正常值：正常
     */
    @ApiModelProperty(value = "第二性征-喉结：正常1、异常-1、未查0；正常值：正常", required = true)
    @NotNull(message = "第二性征-喉结不能为空")
    @Range(min = -1, max = 1, message = "第二性征-喉结不匹配")
    private Integer secondaryProminentiaLaryngea;
    /**
     * 第二性征-阴毛：正常1、异常-1、未查0；正常值：正常
     */
    @ApiModelProperty(value = "第二性征-阴毛：正常1、异常-1、未查0；正常值：正常", required = true)
    @NotNull(message = "第二性征-阴毛不能为空")
    @Range(min = -1, max = 1, message = "第二性征-阴毛不匹配")
    private Integer secondaryPubicHair;
    /**
     * 第二性征-乳房：正常1、异常-1、未查0；正常值：正常
     */
    @ApiModelProperty(value = "第二性征-乳房：正常1、异常-1、未查0；正常值：正常", required = true)
    @NotNull(message = "第二性征-乳房不能为空")
    @Range(min = -1, max = 1, message = "第二性征-乳房不匹配")
    private Integer secondaryBreast;
    /**
     * 第二性征-胡须：有1、无-1、未查0；正常值：有
     */
    @ApiModelProperty(value = "第二性征-胡须：有1、无-1、未查0；正常值：有", required = true)
    @NotNull(message = "第二性征-胡须不能为空")
    @Range(min = -1, max = 1, message = "第二性征-胡须不匹配")
    private Integer secondaryBeard;
    /**
     * 专科检查-阴茎长度（0.成人型、1.儿童型、2.尿道下裂、3.未查）
     */
    @ApiModelProperty(value = "专科检查-阴茎长度（0.成人型、1.儿童型、2.尿道下裂、3.未查）", required = true)
    @NotNull(message = "专科检查-阴茎长度不能为空")
    @Range(min = 0, max = 3, message = "专科检查-阴茎长度不匹配")
    private Integer sePenisSize;
    /**
     * 专科检查-前列腺：正常1、异常-1、未查0；正常值：正常
     */
    @ApiModelProperty(value = "专科检查-前列腺：正常1、异常-1、未查0；正常值：正常", required = true)
    @NotNull(message = "专科检查-前列腺不能为空")
    @Range(min = -1, max = 1, message = "专科检查-前列腺不匹配")
    private Integer seProstate;
    /**
     * 专科检查-左睾丸大小
     */
    @ApiModelProperty(value = "专科检查-左睾丸大小", required = true)
    @NotNull(message = "专科检查-左睾丸大小不能为空")
    @Range(min = 1, message = "专科检查-左睾丸大小不匹配")
    private Integer seLeftTesticleSize;
    /**
     * 专科检查-左睾丸质地：正常1、异常-1、未查0
     */
    @ApiModelProperty(value = "专科检查-左睾丸质地：正常1、异常-1、未查0", required = true)
    @NotNull(message = "专科检查-左睾丸质地不能为空")
    @Range(min = -1, max = 1, message = "专科检查-左睾丸质地不匹配")
    private Integer seLeftTesticleTexture;
    /**
     * 专科检查-左附睾：正常1、异常-1、未查0
     */
    @ApiModelProperty(value = "专科检查-左附睾：正常1、异常-1、未查0", required = true)
    @NotNull(message = "专科检查-左附睾不能为空")
    @Range(min = -1, max = 1, message = "专科检查-左附睾不匹配")
    private Integer seLeftEpididymis;
    /**
     * 专科检查-左输精管：正常1、异常-1、未查0
     */
    @ApiModelProperty(value = "专科检查-左输精管：正常1、异常-1、未查0", required = true)
    @NotNull(message = "专科检查-左输精管不能为空")
    @Range(min = -1, max = 1, message = "专科检查-左输精管不匹配")
    private Integer seLeftVasDeferens;
    /**
     * 专科检查-左精索静脉曲张：未查:0、正常：1、I：2、II：3、III：4
     */
    @ApiModelProperty(value = "专科检查-左精索静脉曲张：未查:0、正常：1、I：2、II：3、III：4", required = true)
    @NotNull(message = "专科检查-左精索静脉曲张不能为空")
    @Range(min = 0, max = 4, message = "专科检查-左精索静脉曲张不匹配")
    private Integer seLeftVaricocele;
    /**
     * 专科检查-右睾丸大小
     */
    @ApiModelProperty(value = "专科检查-右睾丸大小", required = true)
    @NotNull(message = "专科检查-右睾丸大小不能为空")
    @Range(min = 1, message = "专科检查-右睾丸大小不匹配")
    private Integer seRightTesticleSize;
    /**
     * 专科检查-右睾丸质地：正常1、异常-1、未查0
     */
    @ApiModelProperty(value = "专科检查-右睾丸质地：正常1、异常-1、未查0", required = true)
    @NotNull(message = "专科检查-右睾丸质地不能为空")
    @Range(min = -1, max = 1, message = "专科检查-右睾丸质地不匹配")
    private Integer seRightTesticleTexture;
    /**
     * 专科检查-左附睾：正常1、异常-1、未查0
     */
    @ApiModelProperty(value = "专科检查-左附睾：正常1、异常-1、未查0", required = true)
    @NotNull(message = "专科检查-左附睾不能为空")
    @Range(min = -1, max = 1, message = "专科检查-左附睾不匹配")
    private Integer seRightEpididymis;
    /**
     * 专科检查-右输精管：正常1、异常-1、未查0
     */
    @ApiModelProperty(value = "专科检查-右输精管：正常1、异常-1、未查0", required = true)
    @NotNull(message = "专科检查-右输精管不能为空")
    @Range(min = -1, max = 1, message = "专科检查-右输精管不匹配")
    private Integer seRightVasDeferens;
    /**
     * 专科检查-右精索静脉曲张：未查:0、正常：1、I：2、II：3、III：4
     */
    @ApiModelProperty(value = "专科检查-右精索静脉曲张：未查:0、正常：1、I：2、II：3、III：4", required = true)
    @NotNull(message = "专科检查-右精索静脉曲张不能为空")
    @Range(min = 0, max = 4, message = "专科检查-右精索静脉曲张不匹配")
    private Integer seRightVaricocele;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注(200)", required = false)
    @Length(max = 200, message = "备注不能超过200个字符")
    private String remark;

}
