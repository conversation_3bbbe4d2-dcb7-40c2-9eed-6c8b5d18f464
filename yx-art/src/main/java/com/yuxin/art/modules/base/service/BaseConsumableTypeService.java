/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:BaseConsumableTypeService.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.base.service;

import com.yuxin.art.domain.base.BaseConsumableType;
import com.yuxin.art.modules.base.vo.req.BaseConsumableTypeSaveReqVO;
import com.yuxin.art.modules.base.vo.req.BaseConsumableTypeSortReqVO;

import java.util.List;

/**
 * <p>
 * 耗材类型 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-30
 */
public interface BaseConsumableTypeService {

    /**
     * 新增耗材类型
     */
    BaseConsumableType createConsumableType(BaseConsumableTypeSaveReqVO consumableTypeSaveReqVO);

    /**
     * 更新耗材类型
     */
    void updateConsumableType(BaseConsumableTypeSaveReqVO consumableTypeSaveReqVO);

    /**
     * 根据id查询耗材类型
     */
    BaseConsumableType getConsumableType(Long id);

    /**
     * 删除耗材类型
     */
    void deleteConsumableType(Long id);

    /**
     * 获取列表
     */
    List<BaseConsumableType> getConsumableTypeList();

    /**
     * 耗材类型排序
     */
    void consumableTypeSort(List<BaseConsumableTypeSortReqVO> list);
}
