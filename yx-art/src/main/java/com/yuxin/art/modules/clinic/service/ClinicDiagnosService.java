/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ClinicDiagnosService.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.clinic.service;

import com.yuxin.art.domain.clinic.ClinicDiagnos;
import com.yuxin.art.modules.clinic.vo.PastClinicDiagnosVO;
import com.yuxin.framework.mvc.service.BaseService;

import java.util.List;

/**
 * 诊断service
 *
 * <AUTHOR>
 * @date 2024-01-04
 */
public interface ClinicDiagnosService extends BaseService<ClinicDiagnos> {
    /**
     * 保存诊断
     *
     * @param diagnos
     * @return
     */
    Long saveDiagnos(ClinicDiagnos diagnos);

    /**
     * 删除诊断
     *
     * @param id
     */
    void deleteDiagnos(Long id);

    List<PastClinicDiagnosVO> getPastDiagnosByRecordIdAndPatientId(Long recordId, Long patientId, Integer isShow);

    List<ClinicDiagnos> getListByRecordIdAndPatientId(Long recordId, Long patientId, Integer isShow);

    List<PastClinicDiagnosVO> getListDiagnosByIdCard(String idCard);
}
