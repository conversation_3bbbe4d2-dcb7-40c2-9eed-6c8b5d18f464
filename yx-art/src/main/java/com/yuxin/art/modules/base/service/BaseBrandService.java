/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:BaseBrandService.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.base.service;

import com.yuxin.art.domain.base.BaseBrand;
import com.yuxin.art.modules.base.vo.req.BaseBrandSaveReqVO;

import java.util.List;

/**
 * <p>
 * 品牌 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
public interface BaseBrandService {
    /**
     * 新增品牌
     */
    BaseBrand createBrand(BaseBrandSaveReqVO brandSaveReqVO);

    /**
     * 修改品牌
     */
    void updateBrand(BaseBrandSaveReqVO brandSaveReqVO);

    /**
     * 删除品牌
     */
    void deleteBrand(Long id);

    /**
     * 根据id查询品牌
     */
    BaseBrand getBrand(Long id);

    /**
     * 查询品牌列表
     */
    List<BaseBrand> getBrandList();

    /**
     * 查询可用的品牌列表
     */
    List<BaseBrand> getBrandListByEnable();
}
