/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ArtCheckEtController.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.audit.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.yuxin.api.Result;
import com.yuxin.art.domain.audit.ArtCheckEt;
import com.yuxin.art.modules.audit.service.ArtCheckEtService;
import com.yuxin.art.modules.audit.vo.LabelPrintInfoVO;
import com.yuxin.framework.mvc.controller.BaseController;
import com.yuxin.framework.mvc.vo.PageParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * 移植核对 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-27
 */
@RestController
@RequestMapping(value = "/audit/artCheckEt", produces = MediaType.APPLICATION_JSON_VALUE)
@Validated
@Api(tags = "移植核对")
@ApiSort(1)
public class ArtCheckEtController extends BaseController {
    @Autowired
    private ArtCheckEtService artCheckEtService;

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "查询")
    @ApiImplicitParam(name = "id", value = "id", required = true, paramType = "path", dataType = "String")
    @ApiOperationSupport(order = 4)
    // @RequiresPermissions("audit:artCheckEt:get")
    public Result<ArtCheckEt> get(@NotNull(message = "id不能为空") @PathVariable("id") Long id) {
        ArtCheckEt artCheckEt = artCheckEtService.get(id);
        return Result.success(artCheckEt);
    }

    /**
     * 分页查询
     *
     * @param page 分页查询参数
     * @return
     */
    @GetMapping("/getPage")
    @ApiOperation(value = "分页查询")
    @ApiOperationSupport(order = 5)
    // @RequiresPermissions("audit:artCheckEt:getPage")
    public Result<IPage<ArtCheckEt>> getPage(@Validated PageParams page) {
        IPage<ArtCheckEt> pageInfo = artCheckEtService.getList(page);
        return Result.success(pageInfo);
    }

    /**
     * 获取标签打印内容
     *
     * @param cycleId 周期ID
     * @return
     */
    @GetMapping("/labelPrint")
    @ApiOperation(value = "获取标签打印内容")
    @ApiOperationSupport(order = 5)
    @ApiImplicitParam(name = "cycleId", value = "cycleId", required = true, paramType = "query", dataType = "String")
    // @RequiresPermissions("audit:artCheckEt:labelPrint")
    public Result<LabelPrintInfoVO> labelPrint(@NotNull(message = "周期ID不能为空") Long cycleId) {
        return Result.success(artCheckEtService.getLabelPrintInfo(cycleId));
    }
}
