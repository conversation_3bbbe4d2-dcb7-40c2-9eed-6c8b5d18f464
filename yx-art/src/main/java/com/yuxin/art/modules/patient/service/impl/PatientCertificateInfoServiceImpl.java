/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:PatientCertificateInfoServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.patient.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yuxin.api.ResultEnum;
import com.yuxin.art.domain.patient.PatientCertificateInfo;
import com.yuxin.art.domain.patient.PatientInfo;
import com.yuxin.art.domain.patient.PatientPairBond;
import com.yuxin.art.modules.patient.mapper.PatientCertificateInfoMapper;
import com.yuxin.art.modules.patient.service.PatientCertificateInfoService;
import com.yuxin.art.modules.patient.service.PatientInfoService;
import com.yuxin.art.modules.patient.service.PatientPairBondService;
import com.yuxin.art.modules.patient.vo.PatientCertificateVO;
import com.yuxin.constant.GlobalConstant;
import com.yuxin.framework.exception.BusinessException;
import com.yuxin.framework.mvc.mapper.BaseMapper;
import com.yuxin.framework.mvc.service.impl.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 患者证件资料 ServiceImpl
 *
 * <AUTHOR>
 * @since 2020-12-24
 */
@Service
public class PatientCertificateInfoServiceImpl extends BaseServiceImpl<PatientCertificateInfo> implements PatientCertificateInfoService {
    @Autowired
    private PatientCertificateInfoMapper patientCertificateInfoMapper;
    @Autowired
    private PatientPairBondService patientPairBondService;
    @Autowired
    private PatientInfoService patientInfoService;

    @Override
    protected BaseMapper<PatientCertificateInfo> getMapper() {
        return patientCertificateInfoMapper;
    }

    @Override
    public List<PatientCertificateVO> getCertificateById(Long patientId) {
        Map<Long, Integer> patientIdMap = Maps.newHashMap();
        PatientInfo patientInfo = patientInfoService.get(patientId);
        if (patientInfo == null) {
            throw new BusinessException(ResultEnum.FAILED.getCode(), "找不到对应病人数据");
        }
        List<PatientPairBond> patientPairBond = patientPairBondService.findByStatusAndGender(GlobalConstant.YES, patientId, patientInfo.getGender());
        if (CollectionUtil.isNotEmpty(patientPairBond)) {
            patientIdMap.put(patientPairBond.get(0).getPatientFemaleId(), GlobalConstant.NO);
            patientIdMap.put(patientPairBond.get(0).getPatientMaleId(), GlobalConstant.YES);
        } else {
            patientIdMap.put(patientId, patientInfo.getGender());
        }

        // key:证件类型+正反面+性别;
        Set<String> set = Sets.newHashSet();
        List<PatientCertificateVO> resList = Lists.newArrayList();
        QueryWrapper<PatientCertificateInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(PatientCertificateInfo::getPatientId, patientIdMap.keySet()).orderByDesc(PatientCertificateInfo::getRecorderDate);
        List<PatientCertificateInfo> list = super.getList(queryWrapper);
        Set<String> transferCache = new HashSet<>();
        list.forEach(vo -> {
            // 迁移的部分数据有多条，且正反面一致
            if (ObjectUtil.equals(vo.getPCertificateAddress(), vo.getOCertificateAddress())) {
                String sKey = vo.getType().toString() + (vo.getGender() == null ? "" : vo.getGender().toString());
                if (!transferCache.contains(sKey)) {
                    // 正面处理
                    StringBuilder keyBuffer = new StringBuilder(vo.getType().toString()).append(GlobalConstant.YES);
                    if (GlobalConstant.NO == vo.getType()) {
                        keyBuffer.append(vo.getGender());
                    }
                    String key = keyBuffer.toString();
                    if (!set.contains(key)) {
                        PatientCertificateVO info = new PatientCertificateVO();
                        info.setValidPeriod(vo.getValidPeriod());
                        info.setCertificateAddress(vo.getPCertificateAddress());
                        info.setType(key);
                        resList.add(info);
                        set.add(key);
                    }
                    transferCache.add(sKey);
                } else {
                    // 反面处理
                    StringBuilder keyBuffer = new StringBuilder(vo.getType().toString()).append(GlobalConstant.NO);
                    if (GlobalConstant.NO == vo.getType()) {
                        keyBuffer.append(vo.getGender());
                    }
                    String key = keyBuffer.toString();
                    if (!set.contains(key)) {
                        PatientCertificateVO info = new PatientCertificateVO();
                        info.setValidPeriod(vo.getValidPeriod());
                        info.setCertificateAddress(vo.getOCertificateAddress());
                        info.setType(key);
                        resList.add(info);
                        set.add(key);
                    }
                }
            } else {
                if (StrUtil.isNotBlank(vo.getPCertificateAddress())) {
                    StringBuilder keyBuffer = new StringBuilder(vo.getType().toString()).append(GlobalConstant.YES);
                    if (GlobalConstant.NO == vo.getType()) {
                        keyBuffer.append(vo.getGender());
                    }
                    String key = keyBuffer.toString();
                    if (!set.contains(key)) {
                        PatientCertificateVO info = new PatientCertificateVO();
                        info.setValidPeriod(vo.getValidPeriod());
                        info.setCertificateAddress(vo.getPCertificateAddress());
                        info.setType(key);
                        resList.add(info);
                        set.add(key);
                    }
                }

                if (StrUtil.isNotBlank(vo.getOCertificateAddress())) {
                    StringBuilder keyBuffer = new StringBuilder(vo.getType().toString()).append(GlobalConstant.NO);
                    if (GlobalConstant.NO == vo.getType()) {
                        keyBuffer.append(vo.getGender());
                    }

                    String key = keyBuffer.toString();
                    if (!set.contains(key)) {
                        PatientCertificateVO info = new PatientCertificateVO();
                        info.setValidPeriod(vo.getValidPeriod());
                        info.setCertificateAddress(vo.getOCertificateAddress());
                        info.setType(key);
                        resList.add(info);
                        set.add(key);
                    }
                }
            }
        });

        return resList;
    }
}
