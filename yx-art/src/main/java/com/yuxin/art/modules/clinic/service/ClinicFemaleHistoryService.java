/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ClinicFemaleHistoryService.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.clinic.service;

import com.yuxin.art.domain.clinic.ClinicFemaleHistory;
import com.yuxin.art.modules.clinic.vo.AddClinicFemaleHistoryVO;
import com.yuxin.art.modules.clinic.vo.EditClinicFemaleHistoryVO;
import com.yuxin.framework.mvc.service.BaseService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 女方病史service
 *
 * <AUTHOR>
 * @date 2024-01-04
 */
public interface ClinicFemaleHistoryService extends BaseService<ClinicFemaleHistory> {
    /**
     * 新增
     *
     * @param addClinicFemaleHistoryVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    Long businessInsert(AddClinicFemaleHistoryVO addClinicFemaleHistoryVO);

    /**
     * 修改
     *
     * @param editClinicFemaleHistoryVO
     */
    @Transactional(rollbackFor = Exception.class)
    void businessUpdate(EditClinicFemaleHistoryVO editClinicFemaleHistoryVO);
}
