/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称：yx-art
 * 文件名称:WordDocumentServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.trace.service.impl;

import cn.hutool.core.util.StrUtil;
import com.yuxin.art.modules.art.medicalhistory.service.ArtFemaleHistoryService;
import com.yuxin.art.modules.art.medicalhistory.service.ArtMaleHistoryService;
import com.yuxin.art.modules.trace.enums.BizModuleEnum;
import com.yuxin.art.modules.trace.service.WordDocumentService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.charset.StandardCharsets;

/**
 * Word文档生成服务实现类
 * <p>
 * 实现带批注的Word文档生成功能，支持多种业务模块。
 * 根据业务模块类型选择对应的数据服务和模板文件。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class WordDocumentServiceImpl implements WordDocumentService {

    // @Autowired
    // private WordTemplateWithCommentGenerator wordTemplateGenerator; // 暂时注释，避免编译错误

    @Autowired
    private ArtFemaleHistoryService artFemaleHistoryService;

    @Autowired
    private ArtMaleHistoryService artMaleHistoryService;

    @Getter
    @Setter
    @Value("${yuxin.word.template.path:classpath:templates/word}")
    private String templateBasePath = "classpath:templates/word"; // 设置默认值

    @Getter
    @Setter
    @Value("${yuxin.word.preview.url:http://localhost:8080/preview}")
    private String previewBaseUrl = "http://localhost:8080/preview"; // 设置默认值

    @Override
    public byte[] generateWordWithComments(Long bizId, BizModuleEnum bizModuleEnum) {
        log.info("生成带批注的Word文档：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum);

        try {
            // 获取业务数据
            Object businessData = getBusinessData(bizId, bizModuleEnum);
            if (businessData == null) {
                log.error("未找到业务数据：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum);
                throw new RuntimeException("未找到业务数据");
            }

            // 获取模板文件路径
            String templatePath = getTemplatePath(bizModuleEnum);
            if (!isTemplateExists(bizModuleEnum)) {
                log.error("模板文件不存在：{}", templatePath);
                throw new RuntimeException("模板文件不存在：" + templatePath);
            }

            // 生成Word文档（暂时使用简化实现）
            // byte[] wordBytes = wordTemplateGenerator.generateWordWithComments(templatePath, businessData, bizId, bizModuleEnum);
            String content = String.format("带批注的%s文档 - 业务ID: %s", bizModuleEnum.getDisplayName(), bizId);
            byte[] wordBytes = content.getBytes(StandardCharsets.UTF_8);

            log.info("Word文档生成成功：bizId={}, bizModuleEnum={}, size={} bytes", bizId, bizModuleEnum, wordBytes.length);
            return wordBytes;

        } catch (Exception e) {
            log.error("生成带批注的Word文档失败：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum, e);
            throw new RuntimeException("生成Word文档失败：" + e.getMessage(), e);
        }
    }

    @Override
    public byte[] generateWordWithoutComments(Long bizId, BizModuleEnum bizModuleEnum) {
        log.info("生成不带批注的Word文档：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum);

        try {
            // 获取业务数据
            Object businessData = getBusinessData(bizId, bizModuleEnum);
            if (businessData == null) {
                log.error("未找到业务数据：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum);
                throw new RuntimeException("未找到业务数据");
            }

            // 获取模板文件路径
            String templatePath = getTemplatePath(bizModuleEnum);
            if (!isTemplateExists(bizModuleEnum)) {
                log.error("模板文件不存在：{}", templatePath);
                throw new RuntimeException("模板文件不存在：" + templatePath);
            }

            // 生成Word文档（暂时使用简化实现）
            // byte[] wordBytes = wordTemplateGenerator.generateWordWithoutComments(templatePath, businessData);
            String content = String.format("不带批注的%s文档 - 业务ID: %s", bizModuleEnum.getDisplayName(), bizId);
            byte[] wordBytes = content.getBytes(StandardCharsets.UTF_8);

            log.info("Word文档生成成功：bizId={}, bizModuleEnum={}, size={} bytes", bizId, bizModuleEnum, wordBytes.length);
            return wordBytes;

        } catch (Exception e) {
            log.error("生成不带批注的Word文档失败：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum, e);
            throw new RuntimeException("生成Word文档失败：" + e.getMessage(), e);
        }
    }

    @Override
    public String generatePreviewUrl(Long bizId, BizModuleEnum bizModuleEnum, boolean withComments) {
        log.info("生成Word文档预览URL：bizId={}, bizModuleEnum={}, withComments={}", bizId, bizModuleEnum, withComments);

        try {
            // 构建预览URL
            StringBuilder urlBuilder = new StringBuilder(previewBaseUrl);
            urlBuilder.append("/word-preview");
            urlBuilder.append("?bizId=").append(bizId);
            urlBuilder.append("&bizModuleEnum=").append(bizModuleEnum.name());
            urlBuilder.append("&withComments=").append(withComments);
            urlBuilder.append("&timestamp=").append(System.currentTimeMillis());

            String previewUrl = urlBuilder.toString();
            log.info("预览URL生成成功：{}", previewUrl);
            return previewUrl;

        } catch (Exception e) {
            log.error("生成预览URL失败：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum, e);
            throw new RuntimeException("生成预览URL失败：" + e.getMessage(), e);
        }
    }

    @Override
    public boolean isModuleSupported(BizModuleEnum bizModuleEnum) {
        if (bizModuleEnum == null) {
            return false;
        }

        // 检查是否有对应的数据服务
        switch (bizModuleEnum) {
            case FEMALE_HISTORY:
                return artFemaleHistoryService != null;
            case MALE_HISTORY:
                return artMaleHistoryService != null;
            // 可以继续添加其他业务模块的支持
            default:
                log.warn("业务模块暂未支持：{}", bizModuleEnum);
                return false;
        }
    }

    @Override
    public String getTemplatePath(BizModuleEnum bizModuleEnum) {
        if (bizModuleEnum == null) {
            throw new IllegalArgumentException("业务模块不能为空");
        }

        // 构建模板文件路径
        String templateFileName = bizModuleEnum.getTemplateName();
        if (StrUtil.isBlank(templateFileName)) {
            throw new RuntimeException("业务模块未配置模板文件名：" + bizModuleEnum);
        }

        // 将.ftl扩展名替换为.docx
        String docxFileName = templateFileName.replace(".ftl", ".docx");

        // 构建完整路径
        String fullPath;
        if (templateBasePath.startsWith("classpath:")) {
            // 类路径资源
            fullPath = templateBasePath + "/" + docxFileName;
        } else {
            // 文件系统路径
            fullPath = templateBasePath + File.separator + docxFileName;
        }

        log.debug("获取模板文件路径：bizModuleEnum={}, templatePath={}", bizModuleEnum, fullPath);
        return fullPath;
    }

    @Override
    public boolean isTemplateExists(BizModuleEnum bizModuleEnum) {
        try {
            String templatePath = getTemplatePath(bizModuleEnum);

            if (templatePath.startsWith("classpath:")) {
                // 检查类路径资源
                String resourcePath = templatePath.substring("classpath:".length());
                return this.getClass().getClassLoader().getResource(resourcePath) != null;
            } else {
                // 检查文件系统文件
                return new File(templatePath).exists();
            }

        } catch (Exception e) {
            log.error("检查模板文件存在性失败：bizModuleEnum={}", bizModuleEnum, e);
            return false;
        }
    }

    /**
     * 根据业务模块获取业务数据
     *
     * @param bizId         业务数据ID
     * @param bizModuleEnum 业务模块
     * @return 业务数据对象
     */
    private Object getBusinessData(Long bizId, BizModuleEnum bizModuleEnum) {
        if (bizId == null || bizModuleEnum == null) {
            return null;
        }

        try {
            switch (bizModuleEnum) {
                case FEMALE_HISTORY:
                    return artFemaleHistoryService.getArtFemaleHistoryByCycleId(bizId);
                case MALE_HISTORY:
                    return artMaleHistoryService.getArtMaleHistoryByCycleId(bizId);
                // 可以继续添加其他业务模块的数据获取逻辑
                default:
                    log.warn("不支持的业务模块：{}", bizModuleEnum);
                    return null;
            }
        } catch (Exception e) {
            log.error("获取业务数据失败：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum, e);
            return null;
        }
    }

    /**
     * 获取支持的业务模块列表
     *
     * @return 支持的业务模块数组
     */
    public BizModuleEnum[] getSupportedModules() {
        return new BizModuleEnum[] {BizModuleEnum.FEMALE_HISTORY, BizModuleEnum.MALE_HISTORY
            // 可以继续添加其他支持的业务模块
        };
    }

}
