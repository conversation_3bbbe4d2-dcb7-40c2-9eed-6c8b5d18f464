/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称：yx-art
 * 文件名称:WordDocumentServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.trace.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.yuxin.art.modules.art.medicalhistory.service.ArtFemaleHistoryService;
import com.yuxin.art.modules.art.medicalhistory.service.ArtMaleHistoryService;
import com.yuxin.art.modules.trace.enums.BizModuleEnum;
import com.yuxin.art.modules.trace.service.DataChangeTraceService;
import com.yuxin.art.modules.trace.service.WordDocumentService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * Word文档生成服务实现类
 * <p>
 * 实现带批注的Word文档生成功能，支持多种业务模块。
 * 根据业务模块类型选择对应的数据服务和模板文件。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class WordDocumentServiceImpl implements WordDocumentService {

    @Autowired
    private ArtFemaleHistoryService artFemaleHistoryService;

    @Autowired
    private ArtMaleHistoryService artMaleHistoryService;

    @Autowired
    private DataChangeTraceService dataChangeTraceService;

    @Getter
    @Setter
    @Value("${yuxin.word.template.base-path:classpath:templates}")
    private String templateBasePath = "classpath:templates"; // 设置默认值

    @Getter
    @Setter
    @Value("${yuxin.word.preview.base-url:http://localhost:8080/preview}")
    private String previewBaseUrl = "http://localhost:8080/preview"; // 设置默认值

    @Value("${yuxin.word.template.female-history:FemaleHistory.docx}")
    private String femaleHistoryTemplate;

    @Value("${yuxin.word.template.male-history:MaleHistory.docx}")
    private String maleHistoryTemplate;

    @Override
    public byte[] generateWordWithComments(Long bizId, BizModuleEnum bizModuleEnum) {
        log.info("生成带批注的Word文档：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum);

        try {
            // 获取业务数据
            Object businessData = getBusinessData(bizId, bizModuleEnum);
            if (businessData == null) {
                log.error("未找到业务数据：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum);
                throw new RuntimeException("未找到业务数据");
            }

            // 获取模板文件路径
            String templatePath = getTemplatePath(bizModuleEnum);
            if (!isTemplateExists(bizModuleEnum)) {
                log.error("模板文件不存在：{}", templatePath);
                throw new RuntimeException("模板文件不存在：" + templatePath);
            }

            // 获取变更记录作为批注
            List<String> comments = dataChangeTraceService.generateComments(bizId, bizModuleEnum);

            // 生成Word文档
            byte[] wordBytes = generateWordDocument(templatePath, businessData, comments, true);

            log.info("Word文档生成成功：bizId={}, bizModuleEnum={}, size={} bytes", bizId, bizModuleEnum, wordBytes.length);
            return wordBytes;

        } catch (Exception e) {
            log.error("生成带批注的Word文档失败：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum, e);
            throw new RuntimeException("生成Word文档失败：" + e.getMessage(), e);
        }
    }

    @Override
    public byte[] generateWordWithoutComments(Long bizId, BizModuleEnum bizModuleEnum) {
        log.info("生成不带批注的Word文档：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum);

        try {
            // 获取业务数据
            Object businessData = getBusinessData(bizId, bizModuleEnum);
            if (businessData == null) {
                log.error("未找到业务数据：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum);
                throw new RuntimeException("未找到业务数据");
            }

            // 获取模板文件路径
            String templatePath = getTemplatePath(bizModuleEnum);
            if (!isTemplateExists(bizModuleEnum)) {
                log.error("模板文件不存在：{}", templatePath);
                throw new RuntimeException("模板文件不存在：" + templatePath);
            }

            // 生成Word文档（不带批注）
            byte[] wordBytes = generateWordDocument(templatePath, businessData, null, false);

            log.info("Word文档生成成功：bizId={}, bizModuleEnum={}, size={} bytes", bizId, bizModuleEnum, wordBytes.length);
            return wordBytes;

        } catch (Exception e) {
            log.error("生成不带批注的Word文档失败：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum, e);
            throw new RuntimeException("生成Word文档失败：" + e.getMessage(), e);
        }
    }

    @Override
    public String generatePreviewUrl(Long bizId, BizModuleEnum bizModuleEnum, boolean withComments) {
        log.info("生成Word文档预览URL：bizId={}, bizModuleEnum={}, withComments={}", bizId, bizModuleEnum, withComments);

        try {
            // 构建预览URL
            StringBuilder urlBuilder = new StringBuilder(previewBaseUrl);
            urlBuilder.append("/word-preview");
            urlBuilder.append("?bizId=").append(bizId);
            urlBuilder.append("&bizModuleEnum=").append(bizModuleEnum.name());
            urlBuilder.append("&withComments=").append(withComments);
            urlBuilder.append("&timestamp=").append(System.currentTimeMillis());

            String previewUrl = urlBuilder.toString();
            log.info("预览URL生成成功：{}", previewUrl);
            return previewUrl;

        } catch (Exception e) {
            log.error("生成预览URL失败：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum, e);
            throw new RuntimeException("生成预览URL失败：" + e.getMessage(), e);
        }
    }

    @Override
    public boolean isModuleSupported(BizModuleEnum bizModuleEnum) {
        if (bizModuleEnum == null) {
            return false;
        }

        // 检查是否有对应的数据服务
        switch (bizModuleEnum) {
            case FEMALE_HISTORY:
                return artFemaleHistoryService != null;
            case MALE_HISTORY:
                return artMaleHistoryService != null;
            // 可以继续添加其他业务模块的支持
            default:
                log.warn("业务模块暂未支持：{}", bizModuleEnum);
                return false;
        }
    }

    @Override
    public String getTemplatePath(BizModuleEnum bizModuleEnum) {
        if (bizModuleEnum == null) {
            throw new IllegalArgumentException("业务模块不能为空");
        }

        // 根据业务模块获取对应的模板文件名
        String templateFileName;
        switch (bizModuleEnum) {
            case FEMALE_HISTORY:
                templateFileName = femaleHistoryTemplate;
                break;
            case MALE_HISTORY:
                templateFileName = maleHistoryTemplate;
                break;
            default:
                // 对于其他模块，使用默认规则
                String defaultTemplate = bizModuleEnum.getTemplateName();
                if (StrUtil.isBlank(defaultTemplate)) {
                    throw new RuntimeException("业务模块未配置模板文件名：" + bizModuleEnum);
                }
                templateFileName = defaultTemplate.replace(".ftl", ".docx");
                break;
        }

        // 构建完整路径
        String fullPath;
        if (templateBasePath.startsWith("classpath:")) {
            // 类路径资源
            fullPath = templateBasePath + "/" + templateFileName;
        } else {
            // 文件系统路径
            fullPath = templateBasePath + File.separator + templateFileName;
        }

        log.debug("获取模板文件路径：bizModuleEnum={}, templatePath={}", bizModuleEnum, fullPath);
        return fullPath;
    }

    @Override
    public boolean isTemplateExists(BizModuleEnum bizModuleEnum) {
        try {
            String templatePath = getTemplatePath(bizModuleEnum);

            if (templatePath.startsWith("classpath:")) {
                // 检查类路径资源
                String resourcePath = templatePath.substring("classpath:".length());
                return this.getClass().getClassLoader().getResource(resourcePath) != null;
            } else {
                // 检查文件系统文件
                return new File(templatePath).exists();
            }

        } catch (Exception e) {
            log.error("检查模板文件存在性失败：bizModuleEnum={}", bizModuleEnum, e);
            return false;
        }
    }

    /**
     * 根据业务模块获取业务数据
     *
     * @param bizId         业务数据ID
     * @param bizModuleEnum 业务模块
     * @return 业务数据对象
     */
    private Object getBusinessData(Long bizId, BizModuleEnum bizModuleEnum) {
        if (bizId == null || bizModuleEnum == null) {
            return null;
        }

        try {
            switch (bizModuleEnum) {
                case FEMALE_HISTORY:
                    return artFemaleHistoryService.getArtFemaleHistoryByCycleId(bizId);
                case MALE_HISTORY:
                    return artMaleHistoryService.getArtMaleHistoryByCycleId(bizId);
                // 可以继续添加其他业务模块的数据获取逻辑
                default:
                    log.warn("不支持的业务模块：{}", bizModuleEnum);
                    return null;
            }
        } catch (Exception e) {
            log.error("获取业务数据失败：bizId={}, bizModuleEnum={}", bizId, bizModuleEnum, e);
            return null;
        }
    }

    /**
     * 生成Word文档
     *
     * @param templatePath   模板文件路径
     * @param businessData   业务数据
     * @param comments       批注列表
     * @param withComments   是否包含批注
     * @return Word文档字节数组
     */
    private byte[] generateWordDocument(String templatePath, Object businessData, List<String> comments, boolean withComments) {
        try {
            // 加载Word模板
            InputStream templateStream = loadTemplate(templatePath);
            if (templateStream == null) {
                throw new RuntimeException("无法加载模板文件：" + templatePath);
            }

            // 使用POI处理Word文档
            XWPFDocument document = new XWPFDocument(templateStream);

            // 填充业务数据
            fillBusinessData(document, businessData);

            // 如果需要，添加批注
            if (withComments && comments != null && !comments.isEmpty()) {
                addComments(document, comments);
            }

            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);
            document.close();
            templateStream.close();

            return outputStream.toByteArray();

        } catch (Exception e) {
            log.error("生成Word文档失败：templatePath={}", templatePath, e);
            throw new RuntimeException("生成Word文档失败：" + e.getMessage(), e);
        }
    }

    /**
     * 加载模板文件
     */
    private InputStream loadTemplate(String templatePath) {
        try {
            if (templatePath.startsWith("classpath:")) {
                // 从类路径加载
                String resourcePath = templatePath.substring("classpath:".length());
                return this.getClass().getClassLoader().getResourceAsStream(resourcePath);
            } else {
                // 从文件系统加载
                return new FileInputStream(templatePath);
            }
        } catch (Exception e) {
            log.error("加载模板文件失败：{}", templatePath, e);
            return null;
        }
    }

    /**
     * 填充业务数据到Word文档
     */
    private void fillBusinessData(XWPFDocument document, Object businessData) {
        if (businessData == null) {
            return;
        }

        try {
            // 将业务数据转换为Map
            Map<String, Object> dataMap = BeanUtil.beanToMap(businessData);

            // 处理所有段落
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                fillParagraph(paragraph, dataMap);
            }

            // 处理所有表格
            for (XWPFTable table : document.getTables()) {
                fillTable(table, dataMap);
            }

        } catch (Exception e) {
            log.error("填充业务数据失败", e);
        }
    }

    /**
     * 填充段落数据
     */
    private void fillParagraph(XWPFParagraph paragraph, Map<String, Object> dataMap) {
        for (XWPFRun run : paragraph.getRuns()) {
            String text = run.getText(0);
            if (text != null) {
                String filledText = fillPlaceholders(text, dataMap);
                run.setText(filledText, 0);
            }
        }
    }

    /**
     * 填充表格数据
     */
    private void fillTable(XWPFTable table, Map<String, Object> dataMap) {
        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    fillParagraph(paragraph, dataMap);
                }
            }
        }
    }

    /**
     * 替换占位符
     */
    private String fillPlaceholders(String text, Map<String, Object> dataMap) {
        if (text == null || dataMap == null) {
            return text;
        }

        String result = text;
        for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
            String placeholder = "{{" + entry.getKey() + "}}";
            if (result.contains(placeholder)) {
                String value = entry.getValue() != null ? entry.getValue().toString() : "";
                result = result.replace(placeholder, value);
            }
        }
        return result;
    }

    /**
     * 添加批注到文档
     */
    private void addComments(XWPFDocument document, List<String> comments) {
        try {
            // 在文档末尾添加批注信息
            XWPFParagraph commentParagraph = document.createParagraph();
            XWPFRun commentRun = commentParagraph.createRun();
            commentRun.setText("变更记录：");
            commentRun.setBold(true);

            for (int i = 0; i < comments.size(); i++) {
                XWPFParagraph p = document.createParagraph();
                XWPFRun r = p.createRun();
                r.setText((i + 1) + ". " + comments.get(i));
            }

        } catch (Exception e) {
            log.error("添加批注失败", e);
        }
    }



    /**
     * 获取支持的业务模块列表
     *
     * @return 支持的业务模块数组
     */
    public BizModuleEnum[] getSupportedModules() {
        return new BizModuleEnum[] {BizModuleEnum.FEMALE_HISTORY, BizModuleEnum.MALE_HISTORY
            // 可以继续添加其他支持的业务模块
        };
    }

}
