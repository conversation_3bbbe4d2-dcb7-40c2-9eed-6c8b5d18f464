/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:AddArchivalNurseConfirmVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.medicalrecord.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 护士确定
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-31
 */
@Data
@NoArgsConstructor
@ApiModel(value = "AddArchivalNurseConfirmVO", description = "新增护士确定")
public class AddArchivalNurseConfirmVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    public Long id;
    /**
     * 档案周期表主键ID
     */
    @ApiModelProperty(value = "档案周期表主键ID", required = true)
    @NotNull(message = "档案周期表主键ID不能为空")
    private Long cycleInfoId;
    /**
     * 知情同意书：0-不通过 1-通过
     */
    @ApiModelProperty(value = "知情同意书：0-不通过 1-通过", required = true)
    @NotNull(message = "知情同意书不能为空")
    @Min(value = 0, message = "无法识别的知情同意书")
    @Max(value = 1, message = "无法识别的知情同意书")
    private Integer informedConsent;
    /**
     * 检验审查：0-不通过 1-通过
     */
    @ApiModelProperty(value = "检验审查：0-不通过 1-通过", required = true)
    @NotNull(message = "检验审查不能为空")
    @Min(value = 0, message = "无法识别的检验审查")
    @Max(value = 1, message = "无法识别的检验审查")
    private Integer audit;
    /**
     * 女病史：0-不通过 1-通过
     */
    @ApiModelProperty(value = "女病史：0-不通过 1-通过", required = true)
    @NotNull(message = "女病史不能为空")
    @Min(value = 0, message = "无法识别的女病史")
    @Max(value = 1, message = "无法识别的女病史")
    private Integer femaleMedicalHistory;
    /**
     * 男病史：0-不通过 1-通过
     */
    @ApiModelProperty(value = "男病史：0-不通过 1-通过", required = true)
    @NotNull(message = "男病史不能为空")
    @Min(value = 0, message = "无法识别的男病史")
    @Max(value = 1, message = "无法识别的男病史")
    private Integer maleMedicalHistory;
    /**
     * 病程记录：0-不通过 1-通过
     */
    @ApiModelProperty(value = "病程记录：0-不通过 1-通过", required = true)
    @NotNull(message = "病程记录不能为空")
    @Min(value = 0, message = "无法识别的病程记录")
    @Max(value = 1, message = "无法识别的病程记录")
    private Integer course;
    /**
     * 首次病程记录：0-不通过 1-通过
     */
    @ApiModelProperty(value = "首次病程记录：0-不通过 1-通过")
    @Min(value = 0, message = "无法识别的首次病程记录")
    @Max(value = 1, message = "无法识别的首次病程记录")
    private Integer firstCourse;
    /**
     * 促排监测记录：0-不通过 1-通过
     */
    @ApiModelProperty(value = "促排监测记录：0-不通过 1-通过", required = true)
    @NotNull(message = "促排监测记录不能为空")
    @Min(value = 0, message = "无法识别的排监测记录")
    @Max(value = 1, message = "无法识别的排监测记录")
    private Integer promote;
    /**
     * 手术记录：0-不通过 1-通过
     */
    @ApiModelProperty(value = "手术记录：0-不通过 1-通过", required = true)
    @NotNull(message = "手术记录：0-不通过 1-通过不能为空")
    @Min(value = 0, message = "无法识别的手术记录")
    @Max(value = 1, message = "无法识别的手术记录")
    private Integer surgery;
    /**
     * 捡卵记录：0-不通过 1-通过
     */
    @ApiModelProperty(value = "捡卵记录：0-不通过 1-通过", required = true)
    @NotNull(message = "捡卵记录不能为空")
    @Min(value = 0, message = "无法识别的捡卵记录")
    @Max(value = 1, message = "无法识别的捡卵记录")
    private Integer pickEggs;
    /**
     * 精液处理记录：0-不通过 1-通过
     */
    @ApiModelProperty(value = "精液处理记录：0-不通过 1-通过", required = true)
    @NotNull(message = "精液处理记录不能为空")
    @Min(value = 0, message = "无法识别的精液处理记录")
    @Max(value = 1, message = "无法识别的精液处理记录")
    private Integer semenTreatment;
    /**
     * 体外授精记录：0-不通过 1-通过
     */
    @ApiModelProperty(value = "体外授精记录：0-不通过 1-通过", required = true)
    @NotNull(message = "体外授精记录不能为空")
    @Min(value = 0, message = "无法识别的体外授精记录")
    @Max(value = 1, message = "无法识别的体外授精记录")
    private Integer ivf;
    /**
     * 胚胎观察：0-不通过 1-通过
     */
    @ApiModelProperty(value = "胚胎观察：0-不通过 1-通过", required = true)
    @NotNull(message = "胚胎观察不能为空")
    @Min(value = 0, message = "无法识别的胚胎观察")
    @Max(value = 1, message = "无法识别的胚胎观察")
    private Integer embryoObservation;
    /**
     * 解冻记录：0-不通过 1-通过
     */
    @ApiModelProperty(value = "解冻记录：0-不通过 1-通过", required = true)
    @NotNull(message = "解冻记录不能为空")
    @Min(value = 0, message = "无法识别的解冻记录")
    @Max(value = 1, message = "无法识别的解冻记录")
    private Integer thaw;
    /**
     * 冷冻记录：0-不通过 1-通过
     */
    @ApiModelProperty(value = "冷冻记录：0-不通过 1-通过", required = true)
    @NotNull(message = "冷冻记录不能为空")
    @Min(value = 0, message = "无法识别的冷冻记录")
    @Max(value = 1, message = "无法识别的冷冻记录")
    private Integer freezing;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注(200)")
    @Length(max = 200, message = "备注不能超过200个字符")
    private String remark;

}
