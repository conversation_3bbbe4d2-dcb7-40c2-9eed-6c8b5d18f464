/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ArtPrescriptionDetailServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.medication.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yuxin.api.ResultEnum;
import com.yuxin.art.constant.KArtConstants;
import com.yuxin.art.domain.art.cycle.ArtCycle;
import com.yuxin.art.domain.doctororder.ArtMedicalAdvice;
import com.yuxin.art.domain.medication.ArtPrescription;
import com.yuxin.art.domain.medication.ArtPrescriptionDetail;
import com.yuxin.art.modules.art.course.service.ArtProgressNoteService;
import com.yuxin.art.modules.art.cycle.service.ArtCycleService;
import com.yuxin.art.modules.art.cycle.service.ArtCycleStatusChangeService;
import com.yuxin.art.modules.art.utils.ArtErrorOperatingUtil;
import com.yuxin.art.modules.doctororder.service.ArtMedicalAdviceService;
import com.yuxin.art.modules.medication.mapper.ArtPrescriptionDetailMapper;
import com.yuxin.art.modules.medication.service.ArtMedicationDetailService;
import com.yuxin.art.modules.medication.service.ArtPrescriptionDetailService;
import com.yuxin.art.modules.medication.service.ArtPrescriptionService;
import com.yuxin.art.modules.medication.vo.*;
import com.yuxin.art.modules.ultrasound.service.ChelationMonitorService;
import com.yuxin.art.modules.upcoming.service.TodoCohService;
import com.yuxin.art.modules.upcoming.vo.UpdateCohStatusVO;
import com.yuxin.constant.GlobalConstant;
import com.yuxin.framework.exception.BusinessException;
import com.yuxin.framework.mvc.mapper.BaseMapper;
import com.yuxin.framework.mvc.service.impl.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 * 处方明细（开药明细） ServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-15
 */
@Slf4j
@Service
public class ArtPrescriptionDetailServiceImpl extends BaseServiceImpl<ArtPrescriptionDetail> implements ArtPrescriptionDetailService {
    @Autowired
    private ArtPrescriptionDetailMapper artPrescriptionDetailMapper;
    @Autowired
    private TodoCohService todoCohService;
    @Autowired
    private ArtPrescriptionService artPrescriptionService;
    @Autowired
    private ArtMedicationDetailService artMedicationDetailService;
    @Autowired
    private ArtMedicalAdviceService artMedicalAdviceService;

    @Override
    protected BaseMapper<ArtPrescriptionDetail> getMapper() {
        return artPrescriptionDetailMapper;
    }

    @Autowired
    private ArtErrorOperatingUtil artErrorOperatingUtil;
    @Autowired
    private ArtCycleService artCycleService;
    @Autowired
    private ArtProgressNoteService artProgressNoteService;
    @Autowired
    private ChelationMonitorService chelationMonitorService;
    @Autowired
    private ArtCycleStatusChangeService artCycleStatusChangeService;

    @Override
    public synchronized void businessDelete(List<DelArtPrescribeDetailVO> delArtPrescribeDetailVos, Long cycleId) {
        // 需要删除的促排记录
        Set<Date> delPharmacyDates = Sets.newHashSet();
        ArtCycle artCycle = artCycleService.get(cycleId);
        artErrorOperatingUtil.hasError(artCycle);
        Set<Long> prescriptionIdSet = Sets.newHashSet();
        Set<Long> prescriptionDetailIdSet = Sets.newHashSet();
        List<Long> medicationIds = Lists.newArrayList();
        List<Long> detailIds = Lists.newArrayList();
        Set<Date> hcgDates = Sets.newHashSet();
        delArtPrescribeDetailVos.forEach(delVo -> {
            if (ObjectUtil.isNotEmpty(delVo.getPrescriptionId())) {
                prescriptionIdSet.add(delVo.getPrescriptionId());
            }
            if (ObjectUtil.isNotEmpty(delVo.getId())) {
                if (KArtConstants.PrescriptionType.PROMOTE.equals(delVo.getPrescriptionType())) {
                    // 促排
                    medicationIds.add(delVo.getId());
                } else {
                    detailIds.add(delVo.getId());
                }
            }
        });
        // 正常业务是不可能存在全为空的情况，只有要提交不正常数据时才会出现
        if (CollUtil.isEmpty(medicationIds) && CollUtil.isEmpty(detailIds)) {
            throw new BusinessException(ResultEnum.PARAM_VERIFICATION_FAILED.getCode(), "没有可删除的数据！");
        }

        if (CollectionUtil.isNotEmpty(medicationIds)) {
            List<BusinessMedicationVO> medicationDetailList =
                artMedicationDetailService.getListByBusinessIds(medicationIds, KArtConstants.General.NORMAL);
            medicationDetailList.forEach(oldMedicationDetail -> {
                if (DateUtil.beginOfDay(oldMedicationDetail.getUseDrugTime())
                    .before(DateUtil.beginOfDay(new Date())) && !KArtConstants.PrescriptionType.BEFORE_TREATMENT.equals(
                    oldMedicationDetail.getPrescriptionType())) {
                    throw new BusinessException(ResultEnum.PARAM_VERIFICATION_FAILED.getCode(), "所选日期包括过去用药，不能删除或修改！");
                }
                prescriptionDetailIdSet.add(oldMedicationDetail.getPrescriptionDetailId());
                // 需要删除促排监测日期
                delPharmacyDates.add(oldMedicationDetail.getUseDrugTime());
            });
            artMedicationDetailService.delete(medicationIds);
        }
        // 删除其他用药
        if (CollectionUtil.isNotEmpty(detailIds)) {
            List<BusinessMedicationVO> medicationDetailList =
                artMedicationDetailService.getListByBusinessIds(detailIds, KArtConstants.General.ABNORMAL);
            medicationDetailList.forEach(oldMedicationDetail -> {
                Integer prescriptionType = oldMedicationDetail.getPrescriptionType();
                // 判断促排监测是否删除
                if (KArtConstants.PrescriptionType.BEFORE_TREATMENT.equals(prescriptionType)) {
                    if (ObjectUtil.equals(GlobalConstant.YES, oldMedicationDetail.getMonitor())) {
                        delPharmacyDates.add(oldMedicationDetail.getBeginTime());
                    }
                } else {
                    delPharmacyDates.add(oldMedicationDetail.getCreatePrescriptionTime());
                }
            });
            artMedicationDetailService.delByPrescriptionDetailIds(detailIds);
            List<ArtPrescriptionDetail> details = super.getList(detailIds);
            if (CollUtil.isNotEmpty(details)) {
                details.forEach(detail -> {
                    if (ObjectUtil.isNotNull(detail)) {
                        if (detail.getMedicationDays() != null) {
                            // 获取hcg时间
                            for (int i = 0; i < detail.getMedicationDays(); i++) {
                                hcgDates.add(DateUtil.beginOfDay(DateUtil.offset(detail.getBeginTime(), DateField.DAY_OF_MONTH, i)));
                            }
                        } else if (ObjectUtil.equals(detail.getPersistent(), GlobalConstant.YES)) {
                            delPharmacyDates.add(detail.getBeginTime());
                        }

                    }
                });
            }
            super.delete(detailIds);
        }

        // 删除促排开药详情
        List<Long> prescriptionDetailIds = new ArrayList<>();
        prescriptionDetailIdSet.forEach(prescriptionDetailId -> {
            long count = artMedicationDetailService.countByDetailId(prescriptionDetailId);
            if (count == 0) {
                prescriptionDetailIds.add(prescriptionDetailId);

            }
        });
        if (CollUtil.isNotEmpty(prescriptionDetailIds)) {
            super.delete(prescriptionDetailIds);
        }
        // 更新下次就诊日期
        Date nextVisitDate = artMedicationDetailService.getNextVisitDateByCycleId(cycleId);
        artCycle.setNextVisitDate(nextVisitDate);
        artCycleService.update(artCycle);
        List<Long> prescriptionIds = new ArrayList<>();
        List<Long> artMedicalAdviceIds = new ArrayList<>();
        List<Long> artPrescriptionIds = new ArrayList<>();
        prescriptionIdSet.forEach(prescriptionId -> {
            ArtPrescription artPrescription = artPrescriptionService.get(prescriptionId);
            ArtMedicalAdvice artMedicalAdvice = null;
            if (artPrescription != null) {
                artMedicalAdvice = artMedicalAdviceService.get(artPrescription.getMedicalAdviceId());
            }
            // 只会有一个周期
            if (ObjectUtil.isNotNull(artMedicalAdvice)) {
                String content = artPrescriptionService.getContentByPrescriptionId(prescriptionId);
                artMedicalAdvice.setContent(content);
                if (StrUtil.isBlank(content)) {
                    // 如果对应处方下面的开药全部被删除，那么删除处方
                    prescriptionIds.add(prescriptionId);
                    artMedicalAdviceIds.add(artMedicalAdvice.getId());

                    // 置空医嘱
                    UpdateCohStatusVO adviceStatusVO = new UpdateCohStatusVO();
                    adviceStatusVO.setCycleId(cycleId);
                    adviceStatusVO.setItem(KArtConstants.TodoCohItem.COH_MONITOR);
                    adviceStatusVO.setDatetime(new Date());
                    adviceStatusVO.setAdviceStatus(null);
                    todoCohService.updateStatus(adviceStatusVO);
                } else {
                    artMedicalAdviceService.update(artMedicalAdvice);
                    // 保存病程
                    artPrescriptionService.saveSystemProgressNote(prescriptionId, content, cycleId);
                }
            } else {
                // 治疗前用药无医嘱
                List<ArtPrescriptionDetail> list = this.getListByPrescriptionIdAndStatus(prescriptionId, null);
                if (CollectionUtil.isEmpty(list)) {
                    artPrescriptionIds.add(prescriptionId);

                }
            }
        });
        if (CollUtil.isNotEmpty(artPrescriptionIds)) {
            artPrescriptionService.delete(artPrescriptionIds);
        }
        if (CollUtil.isNotEmpty(prescriptionIds)) {
            artPrescriptionService.delete(prescriptionIds);
            artProgressNoteService.deleteByRelevanceIds(prescriptionIds);
        }
        if (CollUtil.isNotEmpty(artMedicalAdviceIds)) {
            artMedicalAdviceService.delete(artMedicalAdviceIds);
        }
        long key = cycleId + DateUtil.beginOfDay(new Date()).getTime();
        String progressContent = artPrescriptionService.getContentByCycleIdAndCreateDate(cycleId, new Date());
        artProgressNoteService.saveSystemProgressNote(cycleId, KArtConstants.ProgressNotes.PRESCRIBE, progressContent, key);
        // 删除促排记录
        delPharmacyDates.forEach(delPharmacyDate -> {
            // 促排药物是否该有日期的促排监测
            List<MedicationDetailVO> promoteList =
                artMedicationDetailService.getListByCycleIdAndUseDrugDate(cycleId, KArtConstants.PrescriptionType.PROMOTE, delPharmacyDate);
            // 治疗前是否该有日期的促排监测
            List<MedicationDetailVO> bfTmtList = this.getBeforeTreatmentByCycleIdAndUseDate(cycleId, delPharmacyDate);
            // 其他用药是否有该日期的促排监测
            List<MedicationDetailVO> otherList = this.getListByCycleIdAndCreateDate(cycleId, delPharmacyDate);
            if (CollectionUtil.isEmpty(promoteList) && CollectionUtil.isEmpty(bfTmtList) && CollectionUtil.isEmpty(otherList)) {
                log.info("businessDelete==删除促排：{}", delPharmacyDate);
                chelationMonitorService.deleteByDate(cycleId, delPharmacyDate, GlobalConstant.YES);
            }
        });
        // 没有促排用药，那么周期状态退回到已定方案
        List<MedicationDetailVO> promoteList =
            artMedicationDetailService.getListByCycleIdAndUseDrugDate(cycleId, KArtConstants.PrescriptionType.PROMOTE, null);
        if (CollectionUtil.isEmpty(promoteList)) {
            if (KArtConstants.CycleStatus.PROMOTE.equals(artCycle.getCycleStatus())) {
                artCycleStatusChangeService.createArtCycleStatusChange(cycleId, KArtConstants.CycleStatus.PLAN);
            }
        }
        // 处理hcg医嘱
        artPrescriptionService.saveHcgAdvice(cycleId, hcgDates, "删除扳机药-更新hcg时间");
        // 今日降调节短信
        artPrescriptionService.sendTodayDownRegulation(artCycle);
        // 明日启动
        artPrescriptionService.sendStartTomorrow(artCycle);
        // 今日注射夜针
        artPrescriptionService.sendHcg(artCycle);
        // 明日促排卵复诊短信
        artCycleService.sendTomorrowCoh(artCycle);

    }

    @Override
    public List<PrescriptionDetailVO> getOtherPrescribeListByCycleId(Long cycleId, Integer prescriptionType) {
        // 非持续用药
        List<PrescriptionDetailVO> resultList = artPrescriptionDetailMapper.getOtherPrescribeListByCycleId(cycleId, prescriptionType);

        // 持续用药
        resultList.addAll(artPrescriptionDetailMapper.getOtherPersistPrescribeListByCycleId(cycleId, prescriptionType));

        resultList.sort(Comparator.comparing(PrescriptionDetailVO::getBeginTime));
        return resultList;
    }

    // @Override
    // public ArtPrescriptionDetail getMaxBeginDateByPrescriptionId(Long prescriptionId) {
    // QueryWrapper<ArtPrescriptionDetail> queryWrapper = new QueryWrapper<>();
    // queryWrapper.lambda().eq(ObjectUtil.isNotNull(prescriptionId), ArtPrescriptionDetail::getPrescriptionId,
    // prescriptionId)
    // .orderByDesc(ArtPrescriptionDetail::getBeginTime);
    // List<ArtPrescriptionDetail> list = super.getList(queryWrapper);
    // ArtPrescriptionDetail artPrescriptionDetail = null;
    // if (CollectionUtil.isNotEmpty(list)) {
    // artPrescriptionDetail = list.get(0);
    // }
    // return artPrescriptionDetail;
    // }

    @Override
    public List<ArtPrescriptionDetail> getListByPrescriptionIdAndStatus(Long prescriptionId, Integer status) {
        QueryWrapper<ArtPrescriptionDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ObjectUtil.isNotNull(prescriptionId), ArtPrescriptionDetail::getPrescriptionId, prescriptionId)
            .eq(ObjectUtil.isNotNull(status), ArtPrescriptionDetail::getStatus, status);
        return super.getList(queryWrapper);
    }

    @Override
    public List<PrescriptionDetailUsePhaseVO> getPriorTreatmentList(Long cycleId) {
        // 查询非持续用药
        List<PrescriptionDetailUsePhaseVO> resultList = artPrescriptionDetailMapper.selectPriorTreatmentList(cycleId);
        // 查询持续用药（无medicationDetail）
        resultList.addAll(artPrescriptionDetailMapper.selectPriorTreatmentPersistList(cycleId));
        resultList.sort(Comparator.comparing(PrescriptionDetailUsePhaseVO::getBeginTime));
        return resultList;
    }

    // @Override
    // public List<MedicationDetailVO> getListByCycleIdAndUseDate(Long cycleId, Date useDate) {
    // return artPrescriptionDetailMapper.selectListByCycleIdAndUseDate(cycleId, useDate);
    // }

    @Override
    public List<MedicationDetailVO> getListByCycleIdAndCreateDate(Long cycleId, Date createDate) {
        List<MedicationDetailVO> resList = Lists.newArrayList();

        // 非术前 - 非持续用药
        List<MedicationDetailVO> list = artPrescriptionDetailMapper.selectListByCycleIdAndCreateDate(cycleId, createDate);
        if (CollectionUtil.isNotEmpty(list)) {
            resList.addAll(list);
        }

        // 非术前 - 持续用药
        List<MedicationDetailVO> persistList = artPrescriptionDetailMapper.selectPersistListByCycleIdAndCreateDate(cycleId, createDate);
        if (CollectionUtil.isNotEmpty(persistList)) {
            resList.addAll(persistList);
        }

        // 术前 - 非持续用药 - 加入监控
        List<MedicationDetailVO> beforeTreatmentList = artPrescriptionDetailMapper.selectBeforeTreatmentByCycleIdAndUseDate(cycleId, createDate);
        if (CollectionUtil.isNotEmpty(beforeTreatmentList)) {
            resList.addAll(beforeTreatmentList);
        }

        // 术前 - 持续用药 - 加入监控
        List<MedicationDetailVO> persistBeforeTreatmentList =
            artPrescriptionDetailMapper.selectPersistBeforeTreatmentByCycleIdAndUseDate(cycleId, createDate);
        if (CollectionUtil.isNotEmpty(persistBeforeTreatmentList)) {
            resList.addAll(persistBeforeTreatmentList);
        }
        return resList;
    }

    @Override
    public List<MedicationDetailVO> getBeforeTreatmentByCycleIdAndUseDate(Long cycleId, Date useDate) {
        return artPrescriptionDetailMapper.selectBeforeTreatmentByCycleIdAndUseDate(cycleId, useDate);
    }

    @Override
    public List<ArtPrescriptionDetail> getListByCycleIdAndMonitor(Long cycleId, Integer monitor) {
        return artPrescriptionDetailMapper.selectListByCycleIdAndMonitor(cycleId, monitor);
    }

    // @Override
    // public long getAvailableHcgCount(Long cycleId) {
    // return artPrescriptionDetailMapper.getAvailableHcgCount(cycleId);
    // }
}
