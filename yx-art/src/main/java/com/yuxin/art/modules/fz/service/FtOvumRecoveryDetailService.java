/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:FtOvumRecoveryDetailService.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.fz.service;

import com.yuxin.art.domain.art.cycle.ArtCycle;
import com.yuxin.art.domain.fz.FtOvumRecoveryDetail;
import com.yuxin.art.domain.fz.FtThawOvum;
import com.yuxin.art.modules.fz.vo.DelRecoveryDetailVO;
import com.yuxin.art.modules.fz.vo.EditFtOvumRecoveryVO;
import com.yuxin.framework.mvc.service.BaseService;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * <p>
 * 卵子复苏评分详情表 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-09
 */
public interface FtOvumRecoveryDetailService extends BaseService<FtOvumRecoveryDetail> {
    /**
     * 批量保存
     *
     * @param editList
     */
    @Transactional(rollbackFor = Exception.class)
    void businessBatchSave(List<EditFtOvumRecoveryVO> editList);

    /**
     * 通过卵子解冻ID和卵子id查询列表
     * @param ovumId
     * @param thawOvumId
     * @return
     */
    // List<FtOvumRecoveryDetail> getListByOvumIdAndThawOvumId(Long ovumId, Long thawOvumId);

    /**
     * 批量删除
     *
     * @param delRecoveryDetailVo
     */
    @Transactional(rollbackFor = Exception.class)
    void businessBatchDel(DelRecoveryDetailVO delRecoveryDetailVo);

    /**
     * 通过麦管解冻id删除
     *
     * @param thawStrawId
     */
    @Transactional(rollbackFor = Exception.class)
    void delByThawStrawId(Long thawStrawId);

    /**
     * 通过麦管解冻ID查询列表
     * @param thawStrawId
     * @return
     */
    // List<FtOvumRecoveryDetail> getListByThawStrawId(Long thawStrawId);

    /**
     * 通过卵子解冻ID查询列表
     */
    List<FtOvumRecoveryDetail> getListByThawOvumId(Long thawOvumId);

    /**
     * 根据复苏ID和添加状态查询列表
     *
     * @param recoveryId
     * @param status
     * @return
     */
    List<FtOvumRecoveryDetail> getListByRecoveryIdAndAddStatus(Long recoveryId, Integer status);

    /**
     * 通过解冻ID和添加状态查询列表
     *
     * @param thawOvumId
     * @param status
     * @return
     */
    List<FtOvumRecoveryDetail> getListByThawOvumIdAndAddStatus(Long thawOvumId, Integer status);

    /**
     * 通过解冻ID获取最新复苏记录
     *
     * @param thawOvumId
     * @return
     */
    List<FtOvumRecoveryDetail> getNewestListByThawOvumId(Long thawOvumId);

    /**
     * 通过卵子id查询最新评分
     *
     * @param thawOvumId
     * @param ovumId
     * @return
     */
    FtOvumRecoveryDetail getNewestByOvumId(Long thawOvumId, Long ovumId);

    /**
     * @param thawOvumId
     * @param ovumId
     * @param scoreNumber
     * @return
     */
    FtOvumRecoveryDetail getByUniqueness(Long thawOvumId, Long ovumId, Integer scoreNumber);

    /**
     * 获取待观察列表
     *
     * @param thawStrawId
     * @return
     */
    List<FtOvumRecoveryDetail> getWaitObserveList(Long thawStrawId);

    /**
     * 修改复苏待办
     *
     * @param ftThawOvum
     */
    @Transactional(rollbackFor = Exception.class)
    void updateRecoverytodoLab(FtThawOvum ftThawOvum);

    /**
     * 导出PDF
     *
     * @param artCycle
     * @param fileName
     */
    ByteArrayOutputStream exportPdf(ArtCycle artCycle, String fileName);
}
