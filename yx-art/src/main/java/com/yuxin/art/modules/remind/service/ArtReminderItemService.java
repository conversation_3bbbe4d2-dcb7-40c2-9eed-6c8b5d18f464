/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ArtReminderItemService.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.remind.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yuxin.art.domain.remind.ArtReminderItem;
import com.yuxin.art.modules.remind.vo.AddArtReminderItemVO;
import com.yuxin.art.modules.remind.vo.EditArtReminderItemVO;
import com.yuxin.framework.mvc.service.BaseService;
import com.yuxin.framework.mvc.vo.PageParams;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 提醒事项 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-14
 */
public interface ArtReminderItemService extends BaseService<ArtReminderItem> {

    /**
     * 批量新增
     *
     * @param addList
     */
    @Transactional(rollbackFor = Exception.class)
    void insertBatch(List<AddArtReminderItemVO> addList);

    /**
     * 批量修改
     *
     * @param editList
     */
    @Transactional(rollbackFor = Exception.class)
    void updateBatch(List<EditArtReminderItemVO> editList);

    /**
     * 分页查询
     *
     * @return
     */
    IPage<ArtReminderItem> getBusinessList(PageParams page);
}
