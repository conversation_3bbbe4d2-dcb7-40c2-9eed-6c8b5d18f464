/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:AddArtHcgAdviceVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.doctororder.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * HCG医嘱
 *
 * <AUTHOR>
 * @since 2020-12-11
 */
@Data
@NoArgsConstructor
@ApiModel(value = "AddArtHcgAdviceVO", description = "新增HCG医嘱")
public class AddArtHcgAdviceVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 扳机时间
     */
    @ApiModelProperty(value = "扳机时间", required = true)
    @NotNull(message = "扳机时间不能为空")
    private Date hcgTime;
    /**
     * 取卵时间/人工授精时间
     */
    @ApiModelProperty(value = "取卵时间/人工授精时间", required = true)
    @NotNull(message = "取卵时间/人工授精时间不能为空")
    private Date eggArtificialTime;
    /**
     * 取卵待办ID/人工授精待办ID
     */
    @ApiModelProperty(value = "取卵待办ID/人工授精待办ID(32)", required = true)
    @NotBlank(message = "取卵待办ID/人工授精待办ID不能为空")
    @Length(max = 32, message = "取卵待办ID/人工授精待办ID不能超过32个字符")
    private String todoId;
    /**
     * HCG次数
     */
    @ApiModelProperty(value = "HCG次数", required = true)
    @NotNull(message = "HCG次数不能为空")
    @Min(value = 0, message = "HCG次数不能小于0")
    private Integer hcgNumber;
    /**
     * 状态.0:未确认、1：已确认
     */
    @ApiModelProperty(value = "状态.0:未确认、1：已确认", required = true)
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态不能小于0")
    @Max(value = 1, message = "状态不能大于1")
    private Integer status;

}
