/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ConsumableStockInSaveReqVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */

package com.yuxin.art.modules.consumable.vo.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 耗材入库请求参数
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ConsumableStockInSaveReqVO", description = "耗材入库请求参数")
public class ConsumableStockInSaveReqVO extends ConsumableBaseReqVO {
    private static final long serialVersionUID = 1L;
    /**
     * 耗材入库id
     */
    @ApiModelProperty(value = "耗材入库id", required = true)
    @NotNull(message = "耗材入库id不能为空")
    private Long id;
    /**
     * 检测结果
     */
    @ApiModelProperty(value = "检测结果", required = true)
    @NotNull(message = "检测结果不能为空")
    @Range(min = 0, max = 3, message = "无法识别的检测结果")
    private Integer testStatus;
    /**
     * 入库日期
     */
    @ApiModelProperty(value = "入库日期", required = true)
    @NotNull(message = "入库日期不能为空")
    private Date stockInDate;
    /**
     * 入库状态.0:待入库;1:已入库;2:不入库
     */
    @ApiModelProperty(value = "入库状态.0:待入库;1:已入库;2:不入库", required = true)
    @NotNull(message = "入库状态不能为空")
    @Range(min = 0, max = 2, message = "无法识别的入库状态")
    private Integer stockInStatus;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Length(max = 255, message = "备注长度不能超过255个字符")
    private String remark;

}
