/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:HcgAdviceTotalVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.doctororder.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 扳机信息
 *
 * <AUTHOR>
 * @date 2020/9/11
 **/
@Data
@NoArgsConstructor
@ApiModel(value = "HcgAdviceTotalVO", description = "扳机统计信息")
public class HcgAdviceTotalVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "取卵时间/人工授精时间")
    private Date eggArtificialTime;

    @ApiModelProperty(value = "人数")
    private Integer number;
}
