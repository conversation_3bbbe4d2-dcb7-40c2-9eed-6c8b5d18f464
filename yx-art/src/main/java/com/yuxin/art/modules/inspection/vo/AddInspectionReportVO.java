/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:AddInspectionReportVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.inspection.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 新增患者报告
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Data
@NoArgsConstructor
@ApiModel(value = "AddInspectionReportVO", description = "新增患者报告")
public class AddInspectionReportVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 患者主键ID
     */
    @ApiModelProperty(value = "患者主键ID", required = true)
    @NotNull(message = "患者主键ID不能为空")
    private Long patientId;
    /**
     * 是否本院报告 0:否1:是
     */
    @ApiModelProperty(value = "是否本院报告 0:否1:是", required = true)
    @NotNull(message = "是否本院报告不能为空")
    @Range(min = 0, max = 1, message = "无法识别是否本院报告")
    private Integer ourHospital;
    /**
     * 报告类型 0:检验 1：检查 2:其它
     */
    @ApiModelProperty(value = "报告类型 0:检验 1：检查", required = true)
    @NotNull(message = "报告类型不能为空")
    @Range(min = 0, max = 2, message = "无法识别的报告类型")
    private Integer reportType;
    /**
     * 项目主键ID
     */
    @ApiModelProperty(value = "项目主键ID", required = false)
    // @NotNull(message = "项目主键ID不能为空")
    private Long itemId;
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称(50)", required = false)
    // @NotBlank(message = "项目名称不能为空")
    private String itemName;
    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号(50)")
    private String itemSerialNo;
    /**
     * 项目内部类型编码
     */
    @ApiModelProperty(value = "项目内部类型编码(50)")
    private String itemInternalTypeCode;
    /**
     * 报告医院
     */
    @ApiModelProperty(value = "报告医院(50)", required = true)
    @NotBlank(message = "报告医院不能为空")
    private String reportHospital;
    /**
     * 报告日期
     */
    @ApiModelProperty(value = "报告日期", required = false)
    // @NotNull(message = "报告日期不能为空")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date reportDate;
    /**
     * 报告有效期,数据字典,json格式
     */
    @ApiModelProperty(value = "报告有效期,数据字典,json格式")
    @Length(max = 200, message = "报告有效期不能超过200个字符")
    private String reportValidity;
    /**
     * 报告医生ID,为本院报告时，需要记录报告医生ID
     */
    @ApiModelProperty(value = "报告医生ID,为本院报告时,需要记录报告医生ID")
    private Long reportDoctorId;
    /**
     * 报告医生名称
     */
    @ApiModelProperty(value = "报告医生名称(20)", required = false)
    @Length(max = 20, message = "报告医生名称不能超过20个字符")
    private String reportDoctorName;
    /**
     * 检查报告详情
     */
    @ApiModelProperty(value = "检查报告详情,为检查单时必填")
    @Valid
    private InspectionReportInspectVO inspectionReportInspect;
    /**
     * 检验报告详情
     */
    @ApiModelProperty(value = "检验报告详情,为检验单时必填")
    @Valid
    private List<InspectionReportExamineVO> inspectionReportExamines;
    /**
     * 报告图像集合
     */
    @ApiModelProperty(value = "报告图像集合")
    @Valid
    private List<InspectionReportImageVO> inspectionReportImages;

}
