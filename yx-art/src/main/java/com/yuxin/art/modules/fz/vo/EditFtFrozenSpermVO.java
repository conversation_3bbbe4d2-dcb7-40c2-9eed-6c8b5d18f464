/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:EditFtFrozenSpermVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.fz.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/9/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@ApiModel(value = "EditFtFrozenSpermVO", description = "编辑冷冻预约")
public class EditFtFrozenSpermVO extends AddFtFrozenSpermVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID", required = true)
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 单号
     */
    @ApiModelProperty(value = "单号(50)", required = true)
    @NotBlank(message = "单号不能为空")
    @Length(max = 50, message = "单号不能超过50个字符")
    private String orderNo;

    /**
     * 待办ID
     */
    @ApiModelProperty(value = "待办ID", required = true)
    @NotNull(message = "待办ID不能为空")
    private Long todoId;

    /**
     * 状态.0:待分配、1:待冷冻、2:待存放、3:已存放
     */
    @ApiModelProperty(value = "状态.0:待分配、1:待冷冻、2:待存放、3:已存放", required = true)
    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "无法识别的状态")
    @Max(value = 3, message = "无法识别的状态")
    private Integer status;
}
