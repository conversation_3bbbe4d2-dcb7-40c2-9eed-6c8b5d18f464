/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:EditInspectionReportImageVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.inspection.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 修改患者报告图像
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Data
@NoArgsConstructor
@ApiModel(value = "EditInspectionReportImageVO", description = "修改患者报告图像")
public class EditInspectionReportImageVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @ApiModelProperty(value = "ID", required = true)
    @NotNull(message = "id不能为空")
    public Long id;
    /**
     * 报告ID
     */
    @ApiModelProperty(value = "报告ID", required = true)
    @NotNull(message = "报告ID不能为空")
    private Long reportId;
    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private Long itemId;
    /**
     * 图像路径
     */
    @ApiModelProperty(value = "图像路径", required = true)
    @NotBlank(message = "图像路径不能为空")
    private String image;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序", required = true)
    @NotNull(message = "排序不能为空")
    @Range(min = 1, max = 1000, message = "排序不能小于1,大于1000")
    private Integer sort;

}
