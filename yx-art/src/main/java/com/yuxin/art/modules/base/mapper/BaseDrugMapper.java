/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:BaseDrugMapper.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.base.mapper;

import cn.hutool.core.util.StrUtil;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuxin.art.domain.base.BaseDrug;
import com.yuxin.framework.mvc.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 药品 Mapper
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
@Mapper
public interface BaseDrugMapper extends BaseMapper<BaseDrug> {

    /**
     * 根据名称查询药品
     */
    default BaseDrug selectByName(String name) {
        return selectOne(BaseDrug::getName, name);
    }

    default List<BaseDrug> selectListByConditions(String name, Integer type, Integer status, Integer isCoh) {
        MPJLambdaWrapper<BaseDrug> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BaseDrug.class);
        if (StrUtil.isNotEmpty(name)) {
            wrapper.and(w -> w.like(BaseDrug::getName, name).or().like(BaseDrug::getNameCode, name));
        }
        wrapper.eqIfExists(BaseDrug::getDrugType, type);
        wrapper.eqIfExists(BaseDrug::getStatus, status);
        wrapper.eqIfExists(BaseDrug::getIsCoh, isCoh);
        wrapper.orderByAsc(BaseDrug::getSort);
        return selectList(wrapper);
    }

    /**
     * 列表查询
     */
    default List<BaseDrug> selectListByStatus(Integer status) {
        MPJLambdaWrapper<BaseDrug> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BaseDrug.class);
        wrapper.eqIfExists(BaseDrug::getStatus, status);
        wrapper.orderByAsc(BaseDrug::getSort);
        return selectList(wrapper);
    }

    default List<BaseDrug> selectListByNullNameCode() {
        MPJLambdaWrapper<BaseDrug> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(BaseDrug.class);
        wrapper.isNull(BaseDrug::getNameCode);
        wrapper.orderByAsc(BaseDrug::getSort);
        return selectList(wrapper);
    }

    /**
     * 根据ID列表查询药品列表
     */
    default List<BaseDrug> selectListByIds(List<Long> ids) {
        return selectList(BaseDrug::getId, ids);
    }
}
