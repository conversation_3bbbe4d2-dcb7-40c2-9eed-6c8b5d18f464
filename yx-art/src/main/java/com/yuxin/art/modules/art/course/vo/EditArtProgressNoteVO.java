/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:EditArtProgressNoteVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.art.course.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * Art病程记录
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-18
 */
@Data
@NoArgsConstructor
@ApiModel(value = "EditArtProgressNoteVO", description = "编辑Art病程记录")
public class EditArtProgressNoteVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @ApiModelProperty(value = "ID", required = true)
    @NotNull(message = "id不能为空")
    public Long id;
    /**
     * 0：首次病程 1：系统病程  2:医患沟通 3:术前讨论 4.病例讨论 5.其他
     */
    @ApiModelProperty(value = "病程类型：0：首次病程 1：系统病程  2:医患沟通 3:术前讨论 4.病例讨论 5.其他", required = true)
    @NotNull(message = "病程类型不能为空")
    @Min(value = 0, message = "无法识别的病程类型")
    @Max(value = 5, message = "无法识别的病程类型")
    private Integer recordSource;
    /**
     * 记录项目
     */
    @ApiModelProperty(value = "记录项目(50)", required = true)
    // @NotBlank(message = "记录项目不能为空")
    @Length(max = 50, message = "记录项目不能超过50个字符")
    private String recordItem;
    /**
     * 记录内容
     */
    @ApiModelProperty(value = "记录内容(1000)", required = true)
    @NotBlank(message = "记录内容不能为空")
    private String content;
    /**
     * 记录人ID
     */
    @ApiModelProperty(value = "记录人ID")
    private Long recorderId;
    /**
     * 记录人姓名
     */
    @ApiModelProperty(value = "记录人姓名")
    private String recorderName;
}
