/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:AddBaseSupplierVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.base.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>
 * 供应商
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-27
 */
@Data
@NoArgsConstructor
@ApiModel(value = "AddBaseSupplierVO", description = "新增供应商")
public class AddBaseSupplierVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称(50)", required = true)
    @NotBlank(message = "供应商名称不能为空")
    @Length(max = 50, message = "供应商名称不能超过50个字符")
    private String name;
    /**
     * 供应商代码
     */
    @ApiModelProperty(value = "供应商代码(20)", required = true)
    @NotBlank(message = "供应商代码不能为空")
    @Length(max = 20, message = "供应商代码不能超过20个字符")
    private String code;
    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人(20)", required = false)
    @Length(max = 20, message = "联系人不能超过20个字符")
    private String contact;
    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话(20)", required = false)
    @Length(max = 20, message = "联系人电话不能超过20个字符")
    private String contactTel;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人(20)", required = false)
    @Length(max = 20, message = "负责人不能超过20个字符")
    private String leader;
    /**
     * 负责人电话
     */
    @ApiModelProperty(value = "负责人电话(20)", required = false)
    @Length(max = 20, message = "负责人电话不能超过20个字符")
    private String leaderTel;
    /**
     * 地区,json格式
     */
    @ApiModelProperty(value = "地区(200),json格式", required = true)
    @NotBlank(message = "地区,json格式不能为空")
    @Length(max = 200, message = "地区,json格式不能超过200个字符")
    private String area;
    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址(50)", required = false)
    @Length(max = 50, message = "详细地址不能超过50个字符")
    private String address;
    /**
     * 是否可用.0:否;1:是
     */
    @ApiModelProperty(value = "是否可用.0:否;1:是", required = true)
    @NotNull(message = "是否可用不能为空")
    @Range(min = 0, max = 1, message = "无法识别的可用状态")
    private Integer status;

}
