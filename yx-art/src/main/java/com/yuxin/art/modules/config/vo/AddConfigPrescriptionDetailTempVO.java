/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:AddConfigPrescriptionDetailTempVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.config.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 处方模板详情
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
@Data
@NoArgsConstructor
@ApiModel(value = "AddConfigPrescriptionDetailTempVO", description = "新增处方模板详情")
public class AddConfigPrescriptionDetailTempVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 处方模板ID
     */
    @ApiModelProperty(value = "处方模板ID", required = true)
    @NotNull(message = "处方模板ID不能为空")
    private Long templatePrescriptionId;

    @ApiModelProperty(value = "药品信息集合", required = true)
    @NotNull(message = "药品信息集合不能为空")
    @Size(min = 1, message = "药品信息集合不能为空")
    @Valid
    List<AddConfigPrescriptionDrugVO> addConfigPrescriptionDrugs;

}
