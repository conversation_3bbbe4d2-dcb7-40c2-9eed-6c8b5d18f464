/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:KnxDateUtils.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yuxin.constant.GlobalConstant;

import java.util.Date;

/**
 * 日期相关的操作工具类
 *
 * <AUTHOR>
 * @date 2022-04-20
 */

public class KnxDateUtils {
    public static int calcAgeNow(Integer idCardType, String idCardNo, Date birthday) {
        return calcAge(idCardType, idCardNo, birthday, new Date());
    }

    public static int calcAge(Integer idCardType, String idCardNo, Date birthday, Date offsetDate) {
        int age = 0;

        if (ObjectUtil.equals(idCardType, GlobalConstant.NO) && StrUtil.isNotBlank(idCardNo) && IdcardUtil.isValidCard(idCardNo)) {
            age = calcAgeByBirthday(IdcardUtil.getBirthDate(idCardNo), offsetDate);
        } else if (birthday != null && birthday.before(offsetDate)) {
            age = calcAgeByBirthday(birthday, offsetDate);
        }

        // 对于其他情况，年龄都是0了

        return age;
    }

    public static int calcAgeByBirthday(Date birthday, Date offsetDate) {
        if (birthday == null || offsetDate == null || birthday.after(offsetDate)) {
            return 0;
        }

        return DateUtil.age(birthday, offsetDate);
    }

    public static int calcAgeByBirthday(String birthday, Date offsetDate) {
        Date date = null;
        try {
            date = DateUtil.parseDate(birthday);
        } catch (Exception e) {
            // 不输出
        }
        if (date == null) {
            return 0;
        }
        return calcAgeByBirthday(DateUtil.parseDate(birthday), offsetDate);
    }
}
