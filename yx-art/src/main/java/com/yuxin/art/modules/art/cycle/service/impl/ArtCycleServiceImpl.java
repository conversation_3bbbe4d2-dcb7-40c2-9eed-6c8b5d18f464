/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ArtCycleServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.art.cycle.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.yuxin.api.ResultEnum;
import com.yuxin.art.cache.base.BaseDataPrintCache;
import com.yuxin.art.cache.config.ConfigRuleCache;
import com.yuxin.art.cache.cycle.CycleInfoCache;
import com.yuxin.art.constant.KArtActualTreatmentPlanEnum;
import com.yuxin.art.constant.KArtConstants;
import com.yuxin.art.constant.KArtObConstants;
import com.yuxin.art.constant.MessageTypeConstants;
import com.yuxin.art.domain.art.ai.ArtArtificialInsemination;
import com.yuxin.art.domain.art.cycle.ArtAppointment;
import com.yuxin.art.domain.art.cycle.ArtCycle;
import com.yuxin.art.domain.art.egg.EggSurgery;
import com.yuxin.art.domain.art.embryo.ArtEmbryo;
import com.yuxin.art.domain.art.ivf.ArtIcsi;
import com.yuxin.art.domain.art.ivf.ArtIvf;
import com.yuxin.art.domain.art.ivf.ArtOvum;
import com.yuxin.art.domain.art.medicalhistory.*;
import com.yuxin.art.domain.art.plan.*;
import com.yuxin.art.domain.art.sperm.ArtSpermApplication;
import com.yuxin.art.domain.art.sperm.ArtSpermProcessing;
import com.yuxin.art.domain.art.sperm.ArtSpermProcessingPostoperation;
import com.yuxin.art.domain.art.transplant.ArtEmbryoTransfer;
import com.yuxin.art.domain.audit.ArtWeeklyReviewDetail;
import com.yuxin.art.domain.audit.ArtWeeklyReviewProcess;
import com.yuxin.art.domain.base.BaseTreatmentTeam;
import com.yuxin.art.domain.cargille.ArtCargilleList;
import com.yuxin.art.domain.config.ConfigMessage;
import com.yuxin.art.domain.config.ConfigMessageTemp;
import com.yuxin.art.domain.followup.FollowUpOhss;
import com.yuxin.art.domain.followup.FollowUpOtherComplication;
import com.yuxin.art.domain.followup.FollowUpPostoperativeBleeding;
import com.yuxin.art.domain.followup.FollowUpPostoperativeInfection;
import com.yuxin.art.domain.fz.*;
import com.yuxin.art.domain.patient.PatientAllergicHistory;
import com.yuxin.art.domain.patient.PatientGeneralInspection;
import com.yuxin.art.domain.patient.PatientInfo;
import com.yuxin.art.domain.surgery.SurgerySpecialOrder;
import com.yuxin.art.domain.sys.SysUser;
import com.yuxin.art.domain.ultrasound.UltrasoundBmi;
import com.yuxin.art.domain.ultrasound.UltrasoundEndocrine;
import com.yuxin.art.domain.ultrasound.UltrasoundMonitor;
import com.yuxin.art.domain.upcoming.*;
import com.yuxin.art.external.vo.CycleInfoVo;
import com.yuxin.art.his.service.HisPatientService;
import com.yuxin.art.his.vo.SyncPatientInfo;
import com.yuxin.art.modules.art.ai.service.ArtArtificialInseminationService;
import com.yuxin.art.modules.art.ai.vo.ArtArtificialInseminationVO;
import com.yuxin.art.modules.art.cycle.mapper.ArtCycleMapper;
import com.yuxin.art.modules.art.cycle.service.ArtAppointmentService;
import com.yuxin.art.modules.art.cycle.service.ArtCycleService;
import com.yuxin.art.modules.art.cycle.service.ArtCycleStatusChangeService;
import com.yuxin.art.modules.art.cycle.vo.*;
import com.yuxin.art.modules.art.egg.service.EggSurgeryService;
import com.yuxin.art.modules.art.egg.vo.EggOpuSurgeryDetailVO;
import com.yuxin.art.modules.art.embryo.service.ArtEmbryoService;
import com.yuxin.art.modules.art.embryoobservation.service.ArtEmbryoObService;
import com.yuxin.art.modules.art.embryoobservation.vo.ArtObSummaryVO;
import com.yuxin.art.modules.art.ivf.service.ArtIcsiService;
import com.yuxin.art.modules.art.ivf.service.ArtIvfService;
import com.yuxin.art.modules.art.ivf.service.ArtOvumService;
import com.yuxin.art.modules.art.medicalhistory.service.*;
import com.yuxin.art.modules.art.plan.service.*;
import com.yuxin.art.modules.art.sperm.service.ArtSpermApplicationService;
import com.yuxin.art.modules.art.sperm.service.ArtSpermProcessingPostoperationService;
import com.yuxin.art.modules.art.sperm.service.ArtSpermProcessingService;
import com.yuxin.art.modules.art.transplant.service.ArtEmbryoTransferService;
import com.yuxin.art.modules.audit.service.ArtWeeklyReviewDetailService;
import com.yuxin.art.modules.audit.service.ArtWeeklyReviewProcessService;
import com.yuxin.art.modules.base.service.BaseTreatmentTeamService;
import com.yuxin.art.modules.cargille.service.ArtCargilleService;
import com.yuxin.art.modules.common.util.DataDictionaryUtil;
import com.yuxin.art.modules.config.service.ConfigMessageService;
import com.yuxin.art.modules.config.service.ConfigMessageTempService;
import com.yuxin.art.modules.doctororder.service.ArtHcgAdviceService;
import com.yuxin.art.modules.followup.service.FollowUpOhssService;
import com.yuxin.art.modules.followup.service.FollowUpOtherComplicationService;
import com.yuxin.art.modules.followup.service.FollowUpPostoperativeBleedingService;
import com.yuxin.art.modules.followup.service.FollowUpPostoperativeInfectionService;
import com.yuxin.art.modules.fz.service.*;
import com.yuxin.art.modules.fz.vo.DonorSpermCycleVO;
import com.yuxin.art.modules.fz.vo.FrozenStockVO;
import com.yuxin.art.modules.fz.vo.ParamDonorSpermStaticVO;
import com.yuxin.art.modules.medicalrecord.service.ArchivalCycleInfoService;
import com.yuxin.art.modules.medication.service.ArtMedicationDetailService;
import com.yuxin.art.modules.medication.service.ArtPrescriptionService;
import com.yuxin.art.modules.medication.vo.MedicationDetailVO;
import com.yuxin.art.modules.patient.service.PatientAllergicHistoryService;
import com.yuxin.art.modules.patient.service.PatientGeneralInspectionService;
import com.yuxin.art.modules.patient.service.PatientInfoService;
import com.yuxin.art.modules.print.util.SignPrintUtil;
import com.yuxin.art.modules.surgery.service.SurgerySpecialOrderService;
import com.yuxin.art.modules.sys.mapper.SysUserMapper;
import com.yuxin.art.modules.ultrasound.service.UltrasoundBmiService;
import com.yuxin.art.modules.ultrasound.service.UltrasoundEndocrineService;
import com.yuxin.art.modules.ultrasound.service.UltrasoundMonitorService;
import com.yuxin.art.modules.upcoming.service.*;
import com.yuxin.art.modules.utils.PrintUtils;
import com.yuxin.art.sms.service.SendService;
import com.yuxin.art.sms.vo.SmsTemplate;
import com.yuxin.cache.util.RedisMapUtil;
import com.yuxin.cache.util.SerialNoGenerator;
import com.yuxin.constant.GlobalConstant;
import com.yuxin.framework.exception.BusinessException;
import com.yuxin.framework.mvc.mapper.BaseMapper;
import com.yuxin.framework.mvc.service.impl.BaseServiceImpl;
import com.yuxin.framework.mvc.vo.PageParams;
import com.yuxin.security.util.AuthUtil;
import com.yuxin.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>
 * ART周期 ServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-10
 */
@Service
@Slf4j
public class ArtCycleServiceImpl extends BaseServiceImpl<ArtCycle> implements ArtCycleService {
    @Autowired
    private ArtCycleMapper artCycleMapper;
    @Autowired
    private ArtAppointmentService artAppointmentService;
    @Autowired
    private ArtCycleReviewService artCycleReviewService;
    @Autowired
    private ArtOvumService artOvumService;
    @Autowired
    private ArtPlanService artPlanService;
    @Autowired
    private ArtWeeklyReviewDetailService artWeeklyReviewDetailService;
    @Autowired
    private ArtWeeklyReviewProcessService artWeeklyReviewProcessService;
    @Autowired
    private ArtSpermApplicationService artSpermApplicationService;
    @Autowired
    private ArchivalCycleInfoService archivalCycleInfoService;
    @Autowired
    private TodoCohService todoCohService;
    @Autowired
    private ArtArtificialInseminationService artArtificialInseminationService;
    @Autowired
    private ArtEmbryoObService artEmbryoObService;
    @Autowired
    private ArtEmbryoTransferService artEmbryoTransferService;
    @Autowired
    private ArtEmbryoService artEmbryoService;
    @Autowired
    private ArtPrescriptionService artPrescriptionService;
    @Autowired
    private FtEmbryoRecoveryDetailService ftEmbryoRecoveryDetailService;
    @Autowired
    private EggSurgeryService eggSurgeryService;
    @Autowired
    private FtThawStrawEmbryoService ftThawStrawEmbryoService;
    @Autowired
    private FtFrozenStockService ftFrozenStockService;
    @Autowired
    private SurgerySpecialOrderService surgerySpecialOrderService;
    @Autowired
    private ArtDiagnosService artDiagnosService;
    @Autowired
    private ArtHcgAdviceService artHcgAdviceService;
    @Autowired
    private ArtFemaleHistoryService artFemaleHistoryService;
    @Autowired
    private ArtMaleHistoryService artMaleHistoryService;
    @Autowired
    private ArtFemalePhysicalService artFemalePhysicalService;
    @Autowired
    private ArtMalePhysicalService artMalePhysicalService;
    @Autowired
    private ArtSummaryService artSummaryService;
    @Autowired
    private ArtIndicationService artIndicationService;
    @Autowired
    private SerialNoGenerator serialNoGenerator;
    @Autowired
    private ArtTreatmentRecommendationService artTreatmentRecommendationService;
    @Autowired
    private ArtChoiceInspectService artChoiceInspectService;
    @Autowired
    private ArtChoiceTargetService choiceTargetService;
    @Autowired
    private ArtChoiceUltrasonicService artChoiceUltrasonicService;
    @Autowired
    private ConfigRuleCache configRuleCache;
    @Autowired
    private BaseDataPrintCache baseDataPrintCache;
    @Autowired
    private ArtIvfService artIvfService;
    @Autowired
    private ArtIcsiService artIcsiService;
    @Autowired
    private ArtSpermProcessingPostoperationService artSpermProcessingPostoperationService;
    @Autowired
    private TodoLabService todoLabService;
    @Autowired
    private TodoClinicalService todoClinicalService;
    @Autowired
    private TodoEtService todoEtService;
    @Autowired
    private TodoSpecialSurgeryService todoSpecialSurgeryService;
    @Autowired
    private ArtCargilleService artCargilleService;
    @Autowired
    private ArtMedicationDetailService artMedicationDetailService;
    @Autowired
    private BaseTreatmentTeamService baseTreatmentTeamService;
    @Autowired
    private PatientInfoService patientInfoService;
    @Autowired
    private FollowUpOhssService followUpOhssService;
    @Autowired
    private PatientAllergicHistoryService patientAllergicHistoryService;
    @Autowired
    private FollowUpPostoperativeInfectionService followUpPostoperativeInfectionService;
    @Autowired
    private FollowUpPostoperativeBleedingService followUpPostoperativeBleedingService;
    @Autowired
    private FollowUpOtherComplicationService followUpOtherComplicationService;
    @Autowired
    private UltrasoundMonitorService ultrasoundMonitorService;
    @Autowired
    private UltrasoundEndocrineService ultrasoundEndocrineService;
    @Autowired
    private SendService sendService;
    @Autowired
    private ConfigMessageService configMessageService;
    @Autowired
    private RedisMapUtil redisMapUtil;
    @Autowired
    private HisPatientService hisPatientService;
    @Autowired
    private FtFrozenStockEmbryoDetailService ftFrozenStockEmbryoDetailService;
    @Autowired
    private FtFrozenStockOvumDetailService ftFrozenStockOvumDetailService;
    @Autowired
    private FtFrozenStockSpermDetailService ftFrozenStockSpermDetailService;
    @Autowired
    private UltrasoundBmiService ultrasoundBmiService;
    @Value("${yuxin.sms.type}")
    private String smsType;
    @Value("${yuxin.oss.ftp.host}")
    private String imgUrl;
    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private ArtPlanEtService artPlanEtService;
    @Autowired
    private ArtPlanAiService artPlanAiService;
    @Autowired
    private ArtPlanOvumService artPlanOvumService;
    @Autowired
    private FtDoEmbryoService ftDoEmbryoService;
    @Autowired
    private ArtPlanThawEmbryoService artPlanThawEmbryoService;
    @Autowired
    private CycleInfoCache cycleInfoCache;
    @Autowired
    private ArtSpermProcessingService artSpermProcessingService;

    private String recycleKey = "art:artcycle:recycleCaseNo";
    // private String cycleKey = "art:artcycle:updateCycle";
    @Autowired
    private ConfigMessageTempService configMessageTempService;

    @Override
    protected BaseMapper<ArtCycle> getMapper() {
        return artCycleMapper;
    }

    /**
     * 获取总周期数
     */
    @Override
    public Long getCount() {
        return artCycleMapper.selectCount(Wrappers.emptyWrapper());
    }

    @Override
    public Long insert(ArtCycle entity) {
        return insertCycle(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(Long id) {
        auditCycle(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revokeAudit(Long id) {
        revokeAuditCycle(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertAudit(ArtCycle artCycle) {
        Long id = insertCycle(artCycle);
        auditCycle(id);
        return id;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revokeAuditUpdate(ArtCycle artCycle) {
        revokeAuditCycle(artCycle.getId());
        updateCycle(artCycle);
        auditCycle(artCycle.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void revokeAuditDelete(Long id) {
        revokeAuditCycle(id);
        deleteCycle(id);
    }

    @Override
    public ArtCycle get(Long id) {
        ArtCycle artCycle = id == null ? null : artCycleMapper.selectById(id);//cycleInfoCache.getCyeleInfo(id);
        //        if (artCycle == null) {
        //            artCycle = artCycleMapper.selectById(id);
        //        }
        return artCycle;
    }

    @Override
    public synchronized void update(ArtCycle artCycle) {
        // ArtCycle CacheCyeyleInfo = (ArtCycle)redisMapUtil.hget(cycleKey, artCycle.getId().toString());
        cycleInfoCache.setCycletInfo(artCycle);
        // if (CacheCyeyleInfo==null){
        // redisMapUtil.hset(cycleKey,artCycle.getId().toString(),artCycle,1);
        super.update(artCycle);
        // }

    }

    @Override
    public synchronized void updateBatch(Collection<ArtCycle> list) {
        super.updateBatch(list);
        cycleInfoCache.setCycletInfoBatch(list);
    }

    @Override
    public void delete(Long id) {
        deleteCycle(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ArtCycle> getListByParams(QueryArtCycleVo queryArtCycleVo) {
        List<ArtCycle> artCycles = artCycleMapper.selectList(Wrappers.lambdaQuery(ArtCycle.class)
            .eq(StrUtil.isNotEmpty(queryArtCycleVo.getPatientFemaleCardNo()), ArtCycle::getPatientFemaleCardNo,
                queryArtCycleVo.getPatientFemaleCardNo())
            .eq(ObjectUtil.isNotNull(queryArtCycleVo.getPatientFemaleIdentifyId()), ArtCycle::getPatientFemaleIdentifyId,
                queryArtCycleVo.getPatientFemaleIdentifyId())
            .eq(StrUtil.isNotEmpty(queryArtCycleVo.getPatientMaleCardNo()), ArtCycle::getPatientMaleCardNo, queryArtCycleVo.getPatientMaleCardNo())
            .eq(ObjectUtil.isNotNull(queryArtCycleVo.getTeamId()), ArtCycle::getTeamId, queryArtCycleVo.getTeamId())
            .eq(ObjectUtil.isNotNull(queryArtCycleVo.getPatientMaleIdentifyId()), ArtCycle::getPatientMaleIdentifyId,
                queryArtCycleVo.getPatientMaleIdentifyId())
            .eq(ObjectUtil.isNotNull(queryArtCycleVo.getPairBondId()), ArtCycle::getPairBondId, queryArtCycleVo.getPairBondId())
            .eq(ObjectUtil.isNotNull(queryArtCycleVo.getCaseNo()), ArtCycle::getCaseNo, queryArtCycleVo.getCaseNo())
            .eq(null != queryArtCycleVo.getOurHospitalTreatment(), ArtCycle::getOurHospitalTreatment, queryArtCycleVo.getOurHospitalTreatment())
            .orderByDesc(ArtCycle::getCycleBeginTime));
        artCycles.forEach(artCycle -> {
            // 20230823 整改调整，去除更改周期年龄操作
            /*  if (artCycle.getCycleStatus().intValue() != KArtConstants.CycleStatus.OBJECTIVETERMINATION
                && artCycle.getCycleStatus().intValue() != KArtConstants.CycleStatus.FINISH
                && null != artCycle.getTermination() && artCycle.getTermination().intValue() != GlobalConstant.YES) {
                updateAge(artCycle);
            }*/
            // 20230804 直接从移植计划里拉最新的移植时间
            List<ArtPlan> list = artPlanService.getList(Wrappers.lambdaQuery(ArtPlan.class).eq(ArtPlan::getCycleId, artCycle.getId())
                .eq(ArtPlan::getPlanStatus, KArtConstants.ArtPlanStatus.DEFAULT).orderByDesc(ArtPlan::getBillingTime));
            if (CollUtil.isNotEmpty(list)) {
                ArtPlan artPlan = list.get(0);
                List<ArtPlanEt> artPlanEts = artPlanEtService.getList(
                    Wrappers.lambdaQuery(ArtPlanEt.class).eq(ArtPlanEt::getPlanId, artPlan.getId()).orderByDesc(ArtPlanEt::getEtTime));
                if (CollectionUtil.isNotEmpty(artPlanEts)) {
                    ArtPlanEt artPlanEt = artPlanEts.get(0);
                    artCycle.setEtTime(artPlanEt.getEtTime());
                }
            }
        });
        return artCycles;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ArtCycle> getList(String patientIdentify, Long pairBondId) {
        List<ArtCycle> artCycles = artCycleMapper.selectList(
            Wrappers.lambdaQuery(ArtCycle.class).eq(ArtCycle::getPatientFemaleCardNo, patientIdentify).or()
                .eq(ArtCycle::getPatientFemaleIdentifyId, patientIdentify).eq(ObjectUtil.isNotNull(pairBondId), ArtCycle::getPairBondId, pairBondId)
                .orderByDesc(ArtCycle::getCycleBeginTime));
        artCycles.forEach(artCycle -> {
            // 20230823 整改调整，去除更改周期年龄操作
            /* if (artCycle.getCycleStatus().intValue() != KArtConstants.CycleStatus.OBJECTIVETERMINATION
                && artCycle.getCycleStatus().intValue() != KArtConstants.CycleStatus.FINISH
                && null != artCycle.getTermination() && artCycle.getTermination().intValue() != GlobalConstant.YES) {
                updateAge(artCycle);
            }*/
            // 20230804 直接从移植计划里拉最新的移植时间
            List<ArtPlan> list = artPlanService.getList(Wrappers.lambdaQuery(ArtPlan.class).eq(ArtPlan::getCycleId, artCycle.getId())
                .eq(ArtPlan::getPlanStatus, KArtConstants.ArtPlanStatus.DEFAULT).orderByDesc(ArtPlan::getBillingTime));
            if (CollUtil.isNotEmpty(list)) {
                ArtPlan artPlan = list.get(0);
                List<ArtPlanEt> artPlanEts = artPlanEtService.getList(
                    Wrappers.lambdaQuery(ArtPlanEt.class).eq(ArtPlanEt::getPlanId, artPlan.getId()).orderByDesc(ArtPlanEt::getEtTime));
                if (CollectionUtil.isNotEmpty(artPlanEts)) {
                    ArtPlanEt artPlanEt = artPlanEts.get(0);
                    artCycle.setEtTime(artPlanEt.getEtTime());
                }
            }
        });
        return artCycles;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editNextVisitDate(EditNextVisitDateVO editNextVisitDateVO) {
        ArtCycle artCycle = this.get(editNextVisitDateVO.getId());
        if (null != artCycle) {
            artCycle.setNextVisitDate(editNextVisitDateVO.getNextVisitDate());
            artCycle.setNextVisitRemark(editNextVisitDateVO.getNextVisitRemark());
            this.update(artCycle);
            if (ObjectUtil.isNotNull(editNextVisitDateVO.getNextVisitDate())) {
                todoCohService.addTodo(artCycle.getId(), KArtConstants.TodoCohItem.COH_MONITOR, editNextVisitDateVO.getNextVisitDate());
            }
        }
        // 明日促排卵复诊短信
        sendTomorrowCoh(artCycle);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editCourtAmh(EditCourtAmhVo editCourtAmhVo) {
        ArtCycle artCycle = this.get(editCourtAmhVo.getId());
        if (artCycle != null) {
            artCycle.setCourtAmh(editCourtAmhVo.getCourtAmh());
            artCycleMapper.updateById(artCycle);
        } else {
            throw new BusinessException(ResultEnum.FAILED.getCode(), "找不到对应周期数据");
        }
    }

    @Override
    public String getProcessByCycleId(Long cycleId) {
        ArtCycle artCycle = this.get(cycleId);
        if (null != artCycle) {
            StringBuilder stringBuilder = new StringBuilder("患者治疗前经充分准备，");
            // 治疗前用药
            String medicineInfo = artPrescriptionService.getCycleProcessMedicalByCycleId(cycleId, 0);
            stringBuilder.append(medicineInfo);
            // 用药信息
            String artMedicineInfo = artPrescriptionService.getCycleProcessMedicalByCycleId(cycleId, 1);
            stringBuilder.append(artMedicineInfo);
            if (null != artCycle.getActualTreatmentPlan()) {
                if (artCycle.getActualTreatmentPlan().intValue() != KArtConstants.ActualTreatmentPlan.AIH && artCycle.getActualTreatmentPlan()
                    .intValue() != KArtConstants.ActualTreatmentPlan.AID) {
                    if (artCycle.getActualTreatmentPlan().intValue() == KArtConstants.ActualTreatmentPlan.FET) {
                        String fetInfo = ftThawStrawEmbryoService.getThawContent(cycleId);
                        stringBuilder.append(fetInfo);
                    } else {
                        // 取卵信息
                        AtomicReference<String> eggInfo = new AtomicReference<>("");
                        List<EggOpuSurgeryDetailVO> eggOpuSurgeryDetailVOS = eggSurgeryService.getOpuInfoByCycleId(cycleId);
                        eggOpuSurgeryDetailVOS.forEach(eggOpuSurgeryDetailVO -> {
                            eggInfo.set("于".concat(DateUtil.format(eggOpuSurgeryDetailVO.getOpuBeginTime(), "yyyy年MM月dd日HH时"))
                                .concat("经阴道超声引导下取卵术").concat("，共获取" + eggOpuSurgeryDetailVO.getPickTotal() + "个卵子;"));
                        });
                        stringBuilder.append(eggInfo.get());
                        // 胚胎信息
                        String embryoInfo = "";
                        ArtObSummaryVO artObSummaryVO = artEmbryoObService.getObSummary(cycleId);
                        embryoInfo = embryoInfo.concat("成熟卵子").concat(artObSummaryVO.getMatureAmount() + "个,");
                        embryoInfo = embryoInfo.concat("正常受精").concat(artObSummaryVO.getNormalInseminationAmount() + "个,");
                        if (artObSummaryVO.getD2CleavageAmount() > 0) {
                            embryoInfo = embryoInfo.concat("D2卵裂").concat(artObSummaryVO.getD2CleavageAmount() + "个,");
                        }
                        embryoInfo = embryoInfo.concat("D3卵裂").concat(artObSummaryVO.getD3CleavageAmount() + "个;");
                        embryoInfo = embryoInfo.concat(ftThawStrawEmbryoService.getThawContent(cycleId));
                        stringBuilder.append(embryoInfo);
                    }
                    // 移植信息
                    AtomicReference<String> treatmentPlansInfo = new AtomicReference<>("");
                    List<ArtEmbryoTransfer> artEmbryoTransfers =
                        artEmbryoTransferService.getList(Wrappers.lambdaQuery(ArtEmbryoTransfer.class).eq(ArtEmbryoTransfer::getCycleId, cycleId));
                    artEmbryoTransfers.forEach(artEmbryoTransfer -> {
                        treatmentPlansInfo.set("于".concat(DateUtil.format(artEmbryoTransfer.getSurgeryDate(), DatePattern.CHINESE_DATE_FORMAT))
                            .concat(DateUtil.format(artEmbryoTransfer.getBeginTime(), "HH时")).concat("进行移植手术，"));
                        if (artEmbryoTransfer.getTransplantResult().intValue() == GlobalConstant.YES) {
                            // 移植的胚胎数
                            AtomicInteger transferNum = new AtomicInteger();
                            AtomicReference<String> fzScore = new AtomicReference<>("");
                            AtomicReference<String> freshScore = new AtomicReference<>("");
                            List<ArtEmbryo> artEmbryos =
                                artEmbryoService.getList(Wrappers.lambdaQuery(ArtEmbryo.class).eq(ArtEmbryo::getEtId, artEmbryoTransfer.getId()));
                            artEmbryos.forEach(artEmbryo -> {
                                transferNum.set(transferNum.get() + 1);
                                if (artEmbryo.getType().intValue() == GlobalConstant.NO) {
                                    String str = "";
                                    if (KArtObConstants.MID.contains(artEmbryo.getOutcomeStage().intValue())) {
                                        if (StrUtil.isNotEmpty(artEmbryo.getOutcomeStageType())) {
                                            str = str + artEmbryo.getOutcomeStageType();
                                        }
                                    } else if (KArtObConstants.LATER.contains(artEmbryo.getOutcomeStage())) {
                                        if (StrUtil.isNotEmpty(artEmbryo.getOutcomeStageScore())) {
                                            str = str + artEmbryo.getOutcomeStageScore();
                                        }
                                    }
                                    freshScore.set(freshScore.get() + str + ",");
                                } else {
                                    String str = "";
                                    FtEmbryoRecoveryDetail ftEmbryoRecoveryDetail =
                                        ftEmbryoRecoveryDetailService.getNewestByEmbryoId(artEmbryo.getLastEmbryoId());
                                    if (null != ftEmbryoRecoveryDetail) {
                                        ArtEmbryo artEmbryo1 = artEmbryoService.get(artEmbryo.getLastEmbryoId());
                                        if (KArtObConstants.MID.contains(artEmbryo1.getOutcomeStage().intValue())) {
                                            if (StrUtil.isNotEmpty(artEmbryo.getOutcomeStageType())) {
                                                str = str + artEmbryo1.getOutcomeStageType() + "->";
                                            }
                                        } else if (KArtObConstants.LATER.contains(artEmbryo1.getOutcomeStage())) {
                                            if (StrUtil.isNotEmpty(artEmbryo.getOutcomeStageScore())) {
                                                str = str + artEmbryo1.getOutcomeStageScore() + "->";
                                            }
                                        }
                                    }
                                    if (ObjectUtil.isNotNull(artEmbryo.getOutcomeStage())) {
                                        if (KArtObConstants.MID.contains(artEmbryo.getOutcomeStage().intValue())) {
                                            if (StrUtil.isNotEmpty(artEmbryo.getOutcomeStageType())) {
                                                str = str + artEmbryo.getOutcomeStageType();
                                            }
                                        } else if (KArtObConstants.LATER.contains(artEmbryo.getOutcomeStage())) {
                                            if (StrUtil.isNotEmpty(artEmbryo.getOutcomeStageScore())) {
                                                str = str + artEmbryo.getOutcomeStageScore();
                                            }
                                        }
                                    } else if (KArtObConstants.MID.contains(artEmbryo.getThawStage().intValue())) {
                                        if (StrUtil.isNotEmpty(artEmbryo.getOutcomeStageType())) {
                                            str = str + artEmbryo.getOutcomeStageType();
                                        }
                                    } else if (KArtObConstants.LATER.contains(artEmbryo.getThawStage())) {
                                        if (StrUtil.isNotEmpty(artEmbryo.getOutcomeStageScore())) {
                                            str = str + artEmbryo.getOutcomeStageScore();
                                        }
                                    }
                                    fzScore.set(fzScore.get() + str + ",");
                                }
                            });
                            if (StrUtil.isNotEmpty(freshScore.get())) {
                                freshScore.set("(" + freshScore.get().substring(0, freshScore.get().length() - 1) + ")");
                            }
                            if (StrUtil.isNotEmpty(fzScore.get())) {
                                fzScore.set("(" + fzScore.get().substring(0, fzScore.get().length() - 1) + ")");
                            }
                            treatmentPlansInfo.set(
                                treatmentPlansInfo.get().concat("手术成功，移植").concat("胚胎" + transferNum + "个" + freshScore + fzScore + "；"));
                        } else {
                            treatmentPlansInfo.set(treatmentPlansInfo.get().concat("手术失败；"));
                        }
                    });
                    stringBuilder.append(treatmentPlansInfo);
                    Long ftNum = artEmbryoService.count(Wrappers.lambdaQuery(ArtEmbryo.class).eq(ArtEmbryo::getCycleId, cycleId)
                        .eq(ArtEmbryo::getStatus, KArtConstants.EmbryoStatus.FROZEN));
                    Long discardedNum = artEmbryoService.count(Wrappers.lambdaQuery(ArtEmbryo.class).eq(ArtEmbryo::getCycleId, cycleId)
                        .eq(ArtEmbryo::getStatus, KArtConstants.EmbryoStatus.DISCARDED));
                    if (ftNum > 0) {
                        stringBuilder.append("冷冻胚胎" + ftNum + "个，");
                    } else {
                        stringBuilder.append("无冷冻胚胎，");
                    }
                    if (discardedNum > 0) {
                        stringBuilder.append("丢弃胚胎" + discardedNum + "个；");
                    } else {
                        stringBuilder.append("无丢弃胚胎；");
                    }
                } else {
                    // 人授信息
                    AtomicReference<String> aiInfo = new AtomicReference<>("");
                    List<ArtArtificialInsemination> artArtificialInseminations = artArtificialInseminationService.getList(
                        Wrappers.lambdaQuery(ArtArtificialInsemination.class).eq(ArtArtificialInsemination::getCycleId, cycleId));
                    artArtificialInseminations.forEach(artArtificialInsemination -> {
                        aiInfo.set("于".concat(DateUtil.format(artArtificialInsemination.getSurgeryDate(), DatePattern.CHINESE_DATE_FORMAT))
                            .concat(DateUtil.format(artArtificialInsemination.getBeginTime(), "HH时")).concat(KArtConstants.Symbols.PAUSE));
                    });
                    if (StrUtil.isNotEmpty(aiInfo.get())) {
                        stringBuilder.append(aiInfo.get().substring(0, aiInfo.get().length() - 1).concat("进行人工授精手术；"));
                    }
                }
            }
            return stringBuilder.toString();
        }
        return null;
    }

    /*@Override
    public ArtCycleSpecialOperate getTerminationInfoByCycleId(Long cycleId) {
        List<ArtCycleSpecialOperate> artCycleSpecialOperates =
            artCycleSpecialOperateService.getList(Wrappers.lambdaQuery(ArtCycleSpecialOperate.class)
                .eq(ArtCycleSpecialOperate::getCycleId, cycleId).orderByDesc(ArtCycleSpecialOperate::getRecorderTime));
        if (CollectionUtil.isNotEmpty(artCycleSpecialOperates)) {
            return artCycleSpecialOperates.get(0);
        }
        return null;
    }*/

    @Override
    public CycleOtherSituationVO getOtherSituationByCycleId(Long cycleId) {
        CycleOtherSituationVO cycleOtherSituationVO = new CycleOtherSituationVO();
        ArtCycle artCycle = this.get(cycleId);

        List<FrozenStockVO> frozenStockList = new ArrayList<>();
        List<FrozenStockVO> frozenStockVOS = ftFrozenStockService.getFrozenStockByPatientFemaleId(artCycle.getPatientFemaleId());
        if (CollectionUtil.isNotEmpty(frozenStockVOS)) {
            frozenStockList.addAll(frozenStockVOS);
        }
        List<FrozenStockVO> frozenSpermStockVOS = ftFrozenStockService.getFrozenStockByPatientMaleId(artCycle.getPatientMaleId());
        if (CollectionUtil.isNotEmpty(frozenSpermStockVOS)) {
            frozenStockList.addAll(frozenSpermStockVOS);
        }
        cycleOtherSituationVO.setFrozenStockVOS(frozenStockList);
        // 老的，按周期获取
        List<SurgerySpecialOrder> surgerySpecialOrders = surgerySpecialOrderService.getFinishListByCycleId(cycleId);
        //  List<SurgerySpecialOrder> surgerySpecialOrders = surgerySpecialOrderService.getFinishListByFemaleId(artCycle.getPatientFemaleId());
        if (CollectionUtil.isNotEmpty(surgerySpecialOrders)) {
            cycleOtherSituationVO.setSurgerySpecialOrders(surgerySpecialOrders);
        }
        return cycleOtherSituationVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IPage<ArtCycle> getList(PageParams page) {
        Map<String, Object> params = page.getParams();
        if (params.containsKey("actualTreatmentPlan")) {
            params.put("actualTreatmentPlan", ArtCycleServiceImpl.changeActualTreatmentPlan(params.get("actualTreatmentPlan").toString()));
        }
        if (params.containsKey("idCard")) {
            PatientInfo patientInfo =
                patientInfoService.get(Wrappers.lambdaQuery(PatientInfo.class).eq(PatientInfo::getIdCard, params.get("idCard")));
            if (patientInfo != null) {
                params.put("id", patientInfo.getIdentifyId());
            } else {
                return new Page<>();
            }
        }
        LambdaQueryWrapper<ArtCycle> lambdaQueryWrapper = Wrappers.lambdaQuery(ArtCycle.class)
            .eq(params.containsKey("ourHospitalTreatment"), ArtCycle::getOurHospitalTreatment, params.get("ourHospitalTreatment"))
            .and(params.containsKey("id"), wrapper -> wrapper.eq(ArtCycle::getPatientFemaleIdentifyId, params.get("id")).or()
                .eq(ArtCycle::getPatientMaleIdentifyId, params.get("id"))).and(params.containsKey("cardNo"),
                wrapper -> wrapper.eq(ArtCycle::getPatientFemaleCardNo, params.get("cardNo")).or()
                    .eq(ArtCycle::getPatientMaleCardNo, params.get("cardNo"))).and(params.containsKey("name"),
                wrapper -> wrapper.eq(ArtCycle::getPatientFemaleName, params.get("name")).or().eq(ArtCycle::getPatientMaleName, params.get("name")))
            .eq(params.containsKey("artDoctorId"), ArtCycle::getArtDoctorId, params.get("artDoctorId"))
            .eq(params.containsKey("treatmentPlan"), ArtCycle::getTreatmentPlan, params.get("treatmentPlan"))
            .in(params.containsKey("actualTreatmentPlan"), ArtCycle::getActualTreatmentPlan, ((List<Integer>)params.get("actualTreatmentPlan")))
            .eq(params.containsKey("cohPlan"), ArtCycle::getCohPlan, params.get("cohPlan"))
            .eq(params.containsKey("teamId"), ArtCycle::getTeamId, params.get("teamId"))
            .eq(params.containsKey("donor"), ArtCycle::getDonor, params.get("donor"))
            .eq(params.containsKey("cycleStatus"), ArtCycle::getCycleStatus, params.get("cycleStatus"))
            .eq(params.containsKey("cycleOutcome"), ArtCycle::getCycleOutcome, params.get("cycleOutcome"))
            .eq(params.containsKey("followupOutcome"), ArtCycle::getFollowupOutcome, params.get("followupOutcome"))
            .ge(params.containsKey("beginCycleBeginTime"), ArtCycle::getCycleBeginTime, params.get("beginCycleBeginTime") == null ? null
                : params.get("beginCycleBeginTime").toString().concat(KArtConstants.FillTime.BEGINTIME))
            .le(params.containsKey("endCycleBeginTime"), ArtCycle::getCycleBeginTime,
                params.get("endCycleBeginTime") == null ? null : params.get("endCycleBeginTime").toString().concat(KArtConstants.FillTime.ENDTIME))
            .ge(params.containsKey("beginCycleEndTime"), ArtCycle::getCycleEndTime,
                params.get("beginCycleEndTime") == null ? null : params.get("beginCycleEndTime").toString().concat(KArtConstants.FillTime.BEGINTIME))
            .le(params.containsKey("endCycleEndTime"), ArtCycle::getCycleEndTime,
                params.get("endCycleEndTime") == null ? null : params.get("endCycleEndTime").toString().concat(KArtConstants.FillTime.ENDTIME))
            .eq(params.containsKey("caseNo"), ArtCycle::getCaseNo, params.get("caseNo")).orderByDesc(ArtCycle::getCycleBeginTime);
        Page<ArtCycle> artCyclePage = artCycleMapper.selectPage(convertPage(page), lambdaQueryWrapper);
        artCyclePage.getRecords().forEach(artCycle -> {
            // 20230823 整改调整，去除更改周期年龄操作
            /*if (artCycle.getCycleStatus().intValue() != KArtConstants.CycleStatus.OBJECTIVETERMINATION
                && artCycle.getCycleStatus().intValue() != KArtConstants.CycleStatus.FINISH
                && null != artCycle.getTermination() && artCycle.getTermination().intValue() != GlobalConstant.YES) {
                updateAge(artCycle);
            }*/
            // 20230804 直接从移植计划里拉最新的移植时间
            List<ArtPlan> list = artPlanService.getList(Wrappers.lambdaQuery(ArtPlan.class).eq(ArtPlan::getCycleId, artCycle.getId())
                .eq(ArtPlan::getPlanStatus, KArtConstants.ArtPlanStatus.DEFAULT).orderByDesc(ArtPlan::getBillingTime));
            if (CollUtil.isNotEmpty(list)) {
                ArtPlan artPlan = list.get(0);
                List<ArtPlanEt> artPlanEts = artPlanEtService.getList(
                    Wrappers.lambdaQuery(ArtPlanEt.class).eq(ArtPlanEt::getPlanId, artPlan.getId()).orderByDesc(ArtPlanEt::getEtTime));
                if (CollectionUtil.isNotEmpty(artPlanEts)) {
                    ArtPlanEt artPlanEt = artPlanEts.get(0);
                    artCycle.setEtTime(artPlanEt.getEtTime());
                }
            }
        });
        return artCyclePage;
    }

    private static List<Integer> changeActualTreatmentPlan(String actualTreatmentPlan) {
        List<Integer> list = new ArrayList<>();
        int i = Integer.parseInt(actualTreatmentPlan);
        if (i < 5) {
            list = KArtActualTreatmentPlanEnum.getCodes(actualTreatmentPlan);
        } else {
            list.add(i);
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void terminationCycle(Long id, Integer cycleOutcome, Integer type, String remark) {
        ArtCycle artCycle = this.get(id);
        if (null != artCycle) {
            if (artCycle.getOurHospitalTreatment() == GlobalConstant.YES) {
                throw new BusinessException(ResultEnum.FAILED.getCode(), "周期为外院周期，无法进行操作");
            }
            if (artCycle.getTreatmentPlan().intValue() == KArtConstants.ApplicableTreatmentPlan.AID || artCycle.getTreatmentPlan()
                .intValue() == KArtConstants.ApplicableTreatmentPlan.AIH) {
                checkAi(id, cycleOutcome);
            } else {
                checkOvumAndEmbryo(id);
                checkSperm(id);
            }
            ArtCycleStatusChangeService artCycleStatusChangeService = SpringUtil.getBean(ArtCycleStatusChangeService.class);
            if (type == KArtConstants.CycleFinish.CANCEL.intValue()) {
                if (cycleOutcome.intValue() == KArtConstants.CycleOutcome.MONITORCANCEL || cycleOutcome.intValue() == KArtConstants.CycleOutcome.NOTOPUSTAGE) {
                    List<EggSurgery> eggSurgeryList = eggSurgeryService.getSurgeryListByCycleId(id);
                    if (CollectionUtils.isNotEmpty(eggSurgeryList)) {
                        throw new BusinessException(ResultEnum.FAILED.getCode(), "有已完成的取卵记录，无法进行操作");
                    }
                } else if (cycleOutcome.intValue() == KArtConstants.CycleOutcome.NOTLUTEALTRANSFORMATION) {
                    List<MedicationDetailVO> medicationDetailVOS = artMedicationDetailService.getListByCycleIdAndUseDrugDate(id, 4, null);
                    if (CollectionUtils.isNotEmpty(medicationDetailVOS)) {
                        throw new BusinessException(ResultEnum.FAILED.getCode(), "有开黄体转化用药，无法进行操作");
                    }
                }
                artCycle.setTermination(GlobalConstant.YES);
                if (StrUtil.isNotEmpty(artCycle.getCaseNo())) {
                    Map<Object, Object> caseNoMaps = redisMapUtil.hmget(recycleKey);
                    String nowDate = DateUtil.format(new Date(), "yyyy");
                    if (caseNoMaps.containsKey(nowDate)) {
                        List<String> caseNos = (List<String>)caseNoMaps.get(nowDate);
                        caseNos.add(artCycle.getCaseNo());
                    } else {
                        List<String> caseNos = new ArrayList<>();
                        caseNos.add(artCycle.getCaseNo());
                        caseNoMaps.put(nowDate, caseNos);
                    }
                    redisMapUtil.hmsetByObject(recycleKey, caseNoMaps);
                }
                artCycle.setCaseNo(null);
                artCycle.setCycleStatus(KArtConstants.CycleStatus.OBJECTIVETERMINATION);
            } else if (type == KArtConstants.CycleFinish.FINISH.intValue()) {
                if (cycleOutcome.intValue() == KArtConstants.CycleOutcome.TRANSPLANTED) {
                    List<ArtEmbryoTransfer> artEmbryoTransfers = artEmbryoTransferService.getSuccessListByCycleId(id);
                    if (CollectionUtils.isEmpty(artEmbryoTransfers)) {
                        throw new BusinessException(ResultEnum.FAILED.getCode(), "没有移植成功的记录，无法进行操作");
                    }
                }
                artCycle.setCycleStatus(KArtConstants.CycleStatus.FINISH);
                artCycleStatusChangeService.createArtCycleStatusChange(id, KArtConstants.CycleStatus.FINISH);
            } else {
                artCycle.setCycleStatus(KArtConstants.CycleStatus.OBJECTIVETERMINATION);
                artCycleStatusChangeService.createArtCycleStatusChange(id, KArtConstants.CycleStatus.OBJECTIVETERMINATION);
                if (StrUtil.isNotEmpty(artCycle.getCaseNo())) {
                    Map<Object, Object> caseNoMaps = redisMapUtil.hmget(recycleKey);
                    String nowDate = DateUtil.format(new Date(), "yyyy");
                    if (caseNoMaps.containsKey(nowDate)) {
                        List<String> caseNos = (List<String>)caseNoMaps.get(nowDate);
                        caseNos.add(artCycle.getCaseNo());
                    } else {
                        List<String> caseNos = new ArrayList<>();
                        caseNos.add(artCycle.getCaseNo());
                        caseNoMaps.put(nowDate, caseNos);
                    }
                    redisMapUtil.hmsetByObject(recycleKey, caseNoMaps);
                }
                artCycle.setCaseNo(null);
            }
            artCycle.setCycleEndTime(new Date());
            artCycle.setRecorderTime(new Date());
            artCycle.setRecorderName(AuthUtil.getCurrentAuthInfo().getName());
            artCycle.setRecorderId(AuthUtil.getCurrentAuthInfo().getUserId());
            artCycle.setCycleOutcome(cycleOutcome);
            artCycle.setRemark(remark);
            this.update(artCycle);
            // 取消短信
            List<ConfigMessage> configMessages =
                configMessageService.getList(Wrappers.lambdaQuery(ConfigMessage.class).eq(ConfigMessage::getCycleId, artCycle.getId()));
            configMessages.forEach(configMessage -> {
                configMessage.setStatus(2);
                configMessageService.update(configMessage);
            });
            this.cancelTodo(artCycle.getId());
        }
    }
    private void cancelTodo(Long cycleId){
        List<TodoClinical> todoClinicals = todoClinicalService.getList(Wrappers.lambdaQuery(TodoClinical.class).eq(TodoClinical::getCycleId, cycleId)
                .ne(TodoClinical::getStatus, KArtConstants.TodoStatus.FINISH));
        if (CollUtil.isNotEmpty(todoClinicals)){
            todoClinicals.forEach(todoClinical -> {
                todoClinical.setStatus(KArtConstants.TodoStatus.CANCEL);
            });
            todoClinicalService.updateBatch(todoClinicals);
        }
        List<TodoLab> todoLabs = todoLabService.getList(Wrappers.lambdaQuery(TodoLab.class).eq(TodoLab::getCycleId, cycleId)
                .ne(TodoLab::getStatus, KArtConstants.TodoStatus.FINISH));
        if (CollUtil.isNotEmpty(todoLabs)){
            todoLabs.forEach(todoLab -> {
                todoLab.setStatus(KArtConstants.TodoStatus.CANCEL);
            });
            todoLabService.updateBatch(todoLabs);
        }
        List<TodoEt> todoEts = todoEtService.getList(Wrappers.lambdaQuery(TodoEt.class).eq(TodoEt::getCycleId, cycleId)
                .ne(TodoEt::getStatus, KArtConstants.TodoStatus.FINISH));
        if (CollUtil.isNotEmpty(todoEts)){
            todoEts.forEach(todoEt -> {
                todoEt.setStatus(KArtConstants.TodoStatus.CANCEL);
            });
            todoEtService.updateBatch(todoEts);
        }
        List<TodoSpecialSurgery> todoSpecialSurgeries=todoSpecialSurgeryService.getList(Wrappers.lambdaQuery(TodoSpecialSurgery.class).eq(TodoSpecialSurgery::getCycleId, cycleId)
                .ne(TodoSpecialSurgery::getStatus, KArtConstants.TodoStatus.FINISH));
        if (CollUtil.isNotEmpty(todoSpecialSurgeries)){
            todoSpecialSurgeries.forEach(todoSpecialSurgery -> {
                todoSpecialSurgery.setStatus(KArtConstants.TodoStatus.CANCEL);
            });
            todoSpecialSurgeryService.updateBatch(todoSpecialSurgeries);
        }
        List<ArtSpermProcessing> artSpermProcessings = artSpermProcessingService.getList(Wrappers.lambdaQuery(ArtSpermProcessing.class).eq(ArtSpermProcessing::getCycleId, cycleId)
                .ne(ArtSpermProcessing::getStatus, KArtConstants.TodoStatus.FINISH));
        if (CollUtil.isNotEmpty(artSpermProcessings)){
            artSpermProcessings.forEach(artSpermProcessing -> {
                artSpermProcessing.setStatus(KArtConstants.TodoStatus.CANCEL);
            });
            artSpermProcessingService.updateBatch(artSpermProcessings);
        }
    }
    private void checkOvumAndEmbryo(Long id) {
        List<ArtOvum> artOvumList = artOvumService.getList(Wrappers.lambdaQuery(ArtOvum.class).eq(ArtOvum::getCycleId, id));
        if (CollectionUtil.isNotEmpty(artOvumList)) {
            artOvumList.forEach(artOvum -> {
                if (null != artOvum.getOutcome() && null != artOvum.getIsFrozen() && artOvum.getOutcome()
                    .intValue() != KArtConstants.OvumOutcome.DISCARDED && artOvum.getIsFrozen() == GlobalConstant.NO) {
                    if (artOvum.getOutcome().intValue() != KArtConstants.OvumOutcome.FROZEN) {
                        ArtEmbryo artEmbryo = artEmbryoService.get(
                            Wrappers.lambdaQuery(ArtEmbryo.class).eq(ArtEmbryo::getCycleId, id).eq(ArtEmbryo::getOvumId, artOvum.getId())
                                .ne(ArtEmbryo::getStatus, KArtConstants.EmbryoStatus.OUTCOMELICSI));
                        if (null != artEmbryo) {
                            if (null != artEmbryo.getStatus() && artEmbryo.getStatus()
                                .intValue() != KArtConstants.EmbryoStatus.DISCARDED && artEmbryo.getStatus()
                                .intValue() != KArtConstants.EmbryoStatus.FROZEN && artEmbryo.getStatus()
                                .intValue() != KArtConstants.EmbryoStatus.PORTED) {
                                throw new BusinessException(ResultEnum.FAILED.getCode(), "有胚胎在继续培养，无法进行操作");
                            }
                        } else {
                            throw new BusinessException(ResultEnum.FAILED.getCode(), "有卵子在继续培养，无法进行操作");
                        }
                    } else {
                        throw new BusinessException(ResultEnum.FAILED.getCode(), "有卵子未冷冻，无法进行操作");
                    }
                }
            });
        }
    }

    private void checkSperm(Long id) {
        List<ArtIvf> artIvfs = artIvfService.getList(Wrappers.lambdaQuery(ArtIvf.class).eq(ArtIvf::getCycleId, id));
        List<ArtIcsi> artIcsis = artIcsiService.getList(Wrappers.lambdaQuery(ArtIcsi.class).eq(ArtIcsi::getCycleId, id));
        if (CollectionUtils.isEmpty(artIvfs) && CollectionUtils.isEmpty(artIcsis)) {
            List<ArtSpermProcessingPostoperation> artSpermProcessingPostoperations = artSpermProcessingPostoperationService.getListByCycleId(id);
            artSpermProcessingPostoperations.forEach(artSpermProcessingPostoperation -> {
                if (artSpermProcessingPostoperation.getOutcome() == 0) {
                    throw new BusinessException(ResultEnum.FAILED.getCode(), "有精液转归授精，无法进行操作");
                } else if (artSpermProcessingPostoperation.getOutcome() == 2) {
                    Long count = todoLabService.unfinishedCount(id, new ArrayList<>(KArtConstants.TodoLabItem.SPERM_FREEZING));
                    if (count > 0) {
                        throw new BusinessException(ResultEnum.FAILED.getCode(), "有精液未冷冻，无法进行操作");
                    }
                }
            });
        }
    }

    private void checkAi(Long id, Integer cycleOutcome) {
        List<ArtArtificialInseminationVO> artArtificialInseminations = artArtificialInseminationService.getListByCycleId(id);
        if (CollectionUtils.isEmpty(artArtificialInseminations)) {
            if (cycleOutcome.intValue() != KArtConstants.CycleOutcome.NOAI) {
                throw new BusinessException(ResultEnum.FAILED.getCode(), "没有已完成的人工授精记录，无法进行操作");
            }
            List<ArtSpermProcessingPostoperation> artSpermProcessingPostoperations = artSpermProcessingPostoperationService.getListByCycleId(id);
            artSpermProcessingPostoperations.forEach(artSpermProcessingPostoperation -> {
                if (artSpermProcessingPostoperation.getOutcome() == 1) {
                    throw new BusinessException(ResultEnum.FAILED.getCode(), "有精液转归AID/AIH，无法进行操作");
                } else if (artSpermProcessingPostoperation.getOutcome() == 2) {
                    Long count = todoLabService.unfinishedCount(id, new ArrayList<>(KArtConstants.TodoLabItem.SPERM_FREEZING));
                    if (count > 0) {
                        throw new BusinessException(ResultEnum.FAILED.getCode(), "有精液未冷冻，无法进行操作");
                    }
                }
            });
        } else if (cycleOutcome.intValue() != KArtConstants.CycleOutcome.FINISHAI) {
            throw new BusinessException(ResultEnum.FAILED.getCode(), "有已完成的人工授精记录，无法进行操作");
        }
    }

    /*@Override
    public List<ArtCycleSpecialOperate> getTerminationCycleList(Long id) {
        return artCycleSpecialOperateService.getList(Wrappers.lambdaQuery(ArtCycleSpecialOperate.class)
            .eq(ArtCycleSpecialOperate::getCycleId, id).orderByDesc(ArtCycleSpecialOperate::getRecorderTime));
    }*/

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAllCycle(ArtCycle artCycle) {
        updateCycle(artCycle);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ArtCycle getCycleById(Long id) {
        ArtCycle artCycle = this.get(id);
        log.error("获取周期信息：{}", artCycle);
        if (artCycle == null) {
            throw new BusinessException(ResultEnum.FAILED.getCode(), "周期不存在！");
        }
        if (artCycle.getArchive().intValue() == GlobalConstant.YES) {
            artCycle.setProcess(getProcessByCycleId(id));
        }
        artCycle.setMedication(artPrescriptionService.getCycleSummaryMedicalByCycleId(id));
        List<ArtDiagnos> femaleArtDiagnos = artDiagnosService.getListByCycleIdAndPatientId(artCycle.getId(), artCycle.getPatientFemaleId(), 1);
        StringBuilder diagnos = new StringBuilder();
        for (ArtDiagnos femaleArtDiagno : femaleArtDiagnos) {
            diagnos.append(femaleArtDiagno.getDiagnosesContent()).append(KArtConstants.Symbols.PAUSE);
        }
        if (diagnos.length() > 0) {
            artCycle.setFemaleDiagnosesContent(diagnos.substring(0, diagnos.length() - 1));
        }
        List<ArtDiagnos> maleArtDiagnos = artDiagnosService.getListByCycleIdAndPatientId(artCycle.getId(), artCycle.getPatientMaleId(), 1);
        diagnos = new StringBuilder();
        for (ArtDiagnos maleArtDiagno : maleArtDiagnos) {
            diagnos.append(maleArtDiagno.getDiagnosesContent()).append(KArtConstants.Symbols.PAUSE);
        }
        if (diagnos.length() > 0) {
            artCycle.setMaleDiagnosesContent(diagnos.substring(0, diagnos.length() - 1));
        }
        if (artCycle.getOurHospitalTreatment() == GlobalConstant.NO) {
            // 20230823 整改调整，去除更改周期年龄操作
            /*if (artCycle.getCycleStatus().intValue() != KArtConstants.CycleStatus.OBJECTIVETERMINATION
                && artCycle.getCycleStatus().intValue() != KArtConstants.CycleStatus.FINISH
                && null != artCycle.getTermination() && artCycle.getTermination().intValue() != GlobalConstant.YES) {
                updateAge(artCycle);
            }*/
        } else {
            PatientInfo patientInfo = patientInfo = patientInfoService.get(artCycle.getPatientFemaleId());
            if (null != patientInfo) {
                if (null != patientInfo.getBirthday()) {
                    artCycle.setPatientFemaleAge(DateUtil.age(patientInfo.getBirthday(), artCycle.getCycleEndTime()));
                }
            }
            patientInfo = patientInfoService.get(artCycle.getPatientMaleId());
            if (null != patientInfo) {
                if (null != patientInfo.getBirthday()) {
                    artCycle.setPatientMaleAge(DateUtil.age(patientInfo.getBirthday(), artCycle.getCycleEndTime()));
                }
            }
        }
        // 20230804 直接从移植计划里拉最新的移植时间
        List<ArtPlan> list = artPlanService.getList(Wrappers.lambdaQuery(ArtPlan.class).eq(ArtPlan::getCycleId, artCycle.getId())
            .eq(ArtPlan::getPlanStatus, KArtConstants.ArtPlanStatus.DEFAULT).orderByDesc(ArtPlan::getBillingTime));
        if (CollUtil.isNotEmpty(list)) {
            ArtPlan artPlan = list.get(0);
            List<ArtPlanEt> artPlanEts = artPlanEtService.getList(
                Wrappers.lambdaQuery(ArtPlanEt.class).eq(ArtPlanEt::getPlanId, artPlan.getId()).orderByDesc(ArtPlanEt::getEtTime));
            if (CollectionUtil.isNotEmpty(artPlanEts)) {
                ArtPlanEt artPlanEt = artPlanEts.get(0);
                artCycle.setEtTime(artPlanEt.getEtTime());
            }
        }
        if (artCycle.getArchive().intValue() == GlobalConstant.YES) {
            // 不懂为什么查询还要修改，先做下调整，只有归档的才更新吧
            this.update(artCycle);
        }

        return artCycle;
    }

    @Override
    public List<ArtCycle> getCycleByIds(String ids) {
        if (StrUtil.isEmpty(ids)) {
            return null;
        }
        List<Long> cycleIds = StrUtil.split(ids, CharUtil.COMMA).stream().mapToLong(Long::valueOf).boxed().collect(Collectors.toList());
        return artCycleMapper.selectList(Wrappers.lambdaQuery(ArtCycle.class).in(CollectionUtil.isNotEmpty(cycleIds), ArtCycle::getId, cycleIds));
        // .ge(ArtCycle::getNextVisitDate,DateUtil.beginOfDay(new Date())));
    }

    @Override
    public Map<String, Object> getTimeAndMedSituationByCycleId(Long cycleId) {
        ArtCycle artCycle = cycleInfoCache.getCyeleInfo(cycleId);// =this.get(cycleId);
        if (artCycle == null) {
            artCycle = this.get(cycleId);
        }
        // 加载计划 com.yuxin.art.modules.art.plan.vo.PlanProfileVo
        Map<String, Object> map = new HashMap<>(artPlanService.getProfile(cycleId));
        List<Date> hcgDateList = artHcgAdviceService.getHcgDateList(cycleId);
        map.put("hcgDateList", hcgDateList);
        String hcgDetail = artPrescriptionService.getHcgDetail(cycleId);
        map.put("hcgDetail", hcgDetail);
        String gnDetail = artPrescriptionService.getGnDetail(cycleId);
        map.put("gnDetail", gnDetail);
        // 20230804 直接从取卵计划里temleps
        List<ArtPlan> list = artPlanService.getList(Wrappers.lambdaQuery(ArtPlan.class).eq(ArtPlan::getCycleId, artCycle.getId())
            .eq(ArtPlan::getPlanStatus, KArtConstants.ArtPlanStatus.DEFAULT).orderByDesc(ArtPlan::getBillingTime));
        if (CollUtil.isNotEmpty(list)) {
            ArtPlan artPlan = list.get(0);
            List<ArtPlanOvum> artPlanOvumList = artPlanOvumService.getList(
                Wrappers.lambdaQuery(ArtPlanOvum.class).eq(ArtPlanOvum::getPlanId, artPlan.getId()).orderByDesc(ArtPlanOvum::getOpuTime));
            if (CollectionUtil.isNotEmpty(artPlanOvumList)) {
                map.put("timelapse", artPlanOvumList.get(0).getTimelapse());
            }
        }
        List<ArtEmbryo> artEmbryos = artEmbryoService.getByCycleId(cycleId);
        // 通过周期胚胎ID去找冷冻胚胎是否做皱缩
        if (CollectionUtil.isNotEmpty(artEmbryos)) {
            for (ArtEmbryo embryo : artEmbryos) {
                FtDoEmbryo ftDoEmbryo = ftDoEmbryoService.get(Wrappers.lambdaQuery(FtDoEmbryo.class).eq(FtDoEmbryo::getEmbryoId, embryo.getId()));
                if (ftDoEmbryo != null && ObjectUtil.isNotNull(ftDoEmbryo.getIsShrinkage()) && ftDoEmbryo.getIsShrinkage() == 1) {
                    map.put("isShrinkage", 1);
                    break;
                }
            }
        }
        List<UltrasoundBmi> ultrasoundBmis = ultrasoundBmiService.getByCycleIdAndPatientId(cycleId, artCycle.getPatientFemaleId());
        if (CollUtil.isNotEmpty(ultrasoundBmis)) {
            UltrasoundBmi ultrasoundBmi = ultrasoundBmis.get(0);
            map.put("bmiContent", "" + (ultrasoundBmi.getCreateTime() == null ? "" : DateUtil.format(ultrasoundBmi.getCreateTime(),
                "yyyy-MM-dd")) + "  身高(cm)：" + ultrasoundBmi.getGiHeight() + "  体重(kg)：" + ultrasoundBmi.getGiWeight() + "  BMI(kg/m*m)：" + ultrasoundBmi.getBmi());
        } else {
            // 当前周期的bmi没有就去找病人一般检查里找
            PatientGeneralInspectionService patientGeneralInspectionService = SpringUtil.getBean(PatientGeneralInspectionService.class);
            List<PatientGeneralInspection> generalInspectionList = patientGeneralInspectionService.findListByPatientId(artCycle.getPatientFemaleId());
            if (CollUtil.isNotEmpty(generalInspectionList)) {
                // 取最后一条
                PatientGeneralInspection patientGeneralInspection = generalInspectionList.get(generalInspectionList.size() - 1);
                map.put("bmiContent", "" + (patientGeneralInspection.getCreateTime() == null ? ""
                    : DateUtil.format(patientGeneralInspection.getCreateTime(),
                        "yyyy-MM-dd")) + "  身高(cm)：" + patientGeneralInspection.getHeight() + "  体重(kg)：" + patientGeneralInspection.getWeight() + "  BMI(kg/m*m)：" + patientGeneralInspection.getBmi());
                // 20250405 增加血压获取
                map.put("systolicPressure", patientGeneralInspection.getSystolicPressure());// 收缩压
                map.put("expansionPressure", patientGeneralInspection.getExpansionPressure());// 扩张压

            } else {
                // 周期里没有导入且病人资料里再没有的话就去各周期的女方病历里找。还是找不着那是真的没有了
                List<ArtFemalePhysical> artFemalePhysicals = artFemalePhysicalService.getList(
                    Wrappers.lambdaQuery(ArtFemalePhysical.class).eq(ArtFemalePhysical::getPatientId, artCycle.getPatientFemaleId())
                        .orderByDesc(ArtFemalePhysical::getCreateTime));
                if (CollUtil.isNotEmpty(artFemalePhysicals)) {
                    for (ArtFemalePhysical artFemalePhysical : artFemalePhysicals) {
                        if (ObjectUtil.isNotNull(artFemalePhysical.getGiBmi())) {
                            map.put("bmiContent", "" + (artFemalePhysical.getCreateTime() == null ? ""
                                : DateUtil.format(artFemalePhysical.getCreateTime(),
                                    "yyyy-MM-dd")) + "  身高(cm)：" + artFemalePhysical.getGiHeight() + "  体重(kg)：" + artFemalePhysical.getGiWeight() + "  BMI(kg/m*m)：" + artFemalePhysical.getGiBmi());
                            // 20250405 增加血压获取
                            map.put("systolicPressure", artFemalePhysical.getGiSystolicPressure());// 收缩压
                            map.put("expansionPressure", artFemalePhysical.getGiExpansionPressure());// 扩张压
                            break;
                        }
                    }
                }
            }

        }
        // 20250405 增加血型获取
        PatientInfo patientInfo = patientInfoService.get(artCycle.getPatientFemaleId());
        map.put("bloodType", patientInfo.getBloodType());// ABO血型
        map.put("rhBloodType", patientInfo.getRhBloodType());// RH血型
        return map;
    }

    @Override
    public List<Map<String, Object>> getNurseCheckListByCycleId(Long cycleId) {
        List<Map<String, Object>> list = new ArrayList<>();
        ArtCycle artCycle = this.get(cycleId);
        // 核对信息取自几个地方 建档的创建者 男/女病史的主诉和现病史 各手术的巡回护士和洗手护士
        PatientInfo patientInfo = patientInfoService.get(artCycle.getPatientFemaleId());
        Map<String, Object> map = new HashMap<>();
        map.put("title", "建档证件查看");
        map.put("operationName", patientInfo.getCreaterName());
        map.put("operationTime", patientInfo.getCreateTime());
        list.add(map);
        ArtFemaleHistory artFemaleHistory = artFemaleHistoryService.getArtFemaleHistoryByCycleId(artCycle.getId());
        if (ObjectUtil.isNotNull(artFemaleHistory)) {
            map = new HashMap<>();
            map.put("title", "ART女病史记录");
            map.put("operationName", artFemaleHistory.getRecorderName());
            map.put("operationTime", artFemaleHistory.getRecorderDate());
            list.add(map);
        }
        ArtMaleHistory artMaleHistory = artMaleHistoryService.getArtMaleHistoryByCycleId(artCycle.getId());
        if (ObjectUtil.isNotNull(artMaleHistory)) {
            map = new HashMap<>();
            map.put("title", "ART男病史记录");
            map.put("operationName", artMaleHistory.getRecorderName());
            map.put("operationTime", artMaleHistory.getRecorderDate());
            list.add(map);
        }
        List<EggSurgery> eggSurgeryList = eggSurgeryService.getSurgeryListByCycleId(artCycle.getId());
        if (CollUtil.isNotEmpty(eggSurgeryList)) {
            for (EggSurgery eggSurgery : eggSurgeryList) {
                map = new HashMap<>();
                map.put("title", "取卵手术洗手护士");
                map.put("operationName", eggSurgery.getOpuNurseName());
                map.put("operationTime", eggSurgery.getOpuRecorderTime());
                list.add(map);
                map = new HashMap<>();
                map.put("title", "取卵手术巡回护士");
                map.put("operationName", eggSurgery.getTourNurseName());
                map.put("operationTime", eggSurgery.getOpuRecorderTime());
                list.add(map);
            }
        }
        List<ArtArtificialInseminationVO> artArtificialInseminations = artArtificialInseminationService.getListByCycleId(artCycle.getId());
        if (CollUtil.isNotEmpty(artArtificialInseminations)) {
            for (ArtArtificialInseminationVO artArtificialInseminationVO : artArtificialInseminations) {
                map = new HashMap<>();
                map.put("title", "人授手术洗手护士");
                map.put("operationName", artArtificialInseminationVO.getSurgicalNurseName());
                map.put("operationTime", artArtificialInseminationVO.getRecorderTime());
                list.add(map);
                map = new HashMap<>();
                map.put("title", "人授手术巡回护士");
                map.put("operationName", artArtificialInseminationVO.getTourNurseName());
                map.put("operationTime", artArtificialInseminationVO.getRecorderTime());
                list.add(map);
            }
        }
        List<ArtEmbryoTransfer> artEmbryoTransfers = artEmbryoTransferService.getByCycleId(artCycle.getId(), 1);
        if (CollUtil.isNotEmpty(artEmbryoTransfers)) {
            for (ArtEmbryoTransfer artEmbryoTransfer : artEmbryoTransfers) {
                map = new HashMap<>();
                map.put("title", "移植手术洗手护士");
                map.put("operationName", artEmbryoTransfer.getSurgicalNurseName());
                map.put("operationTime", artEmbryoTransfer.getRecorderTime());
                list.add(map);
                map = new HashMap<>();
                map.put("title", "移植手术巡回护士");
                map.put("operationName", artEmbryoTransfer.getTourNurseName());
                map.put("operationTime", artEmbryoTransfer.getRecorderTime());
                list.add(map);
            }
        }
        return list;
    }

    @Override
    public ByteArrayOutputStream exportPdf(ArtCycle artCycle, String fileName) {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("hospitalName", configRuleCache.getConfigRule().get(0).getHospitalName());
            map.putAll(PrintUtils.getCycleBasicData(artCycle));
            map.put("patientFemaleId", artCycle.getPatientFemaleIdentifyId());
            map.put("patientMaleId", artCycle.getPatientMaleIdentifyId());
            StringBuilder planStr = new StringBuilder();
            if (null != artCycle.getTreatmentPlan()) {
                planStr.append(baseDataPrintCache.getByCodeAndValue("applicableTreatmentPlan",
                    artCycle.getTreatmentPlan().toString()) + KArtConstants.Symbols.SEMICOLON);
            }
            if (StrUtil.isNotEmpty(artCycle.getEggSource())) {
                String[] eggSources = artCycle.getEggSource().split(",");
                for (String str : eggSources) {
                    planStr.append(baseDataPrintCache.getByCodeAndValue("eggSources", str) + KArtConstants.Symbols.SEMICOLON);
                }
            }
            if (StrUtil.isNotEmpty(artCycle.getSpermSource())) {
                String[] spermSources = artCycle.getSpermSource().split(",");
                for (String str : spermSources) {
                    planStr.append(baseDataPrintCache.getByCodeAndValue("spermSources", str) + KArtConstants.Symbols.SEMICOLON);
                }
            }
            map.put("plan", planStr);
            if (null != artCycle.getCohPlan()) {
                map.put("coh", baseDataPrintCache.getByCodeAndValue("cohPlans", artCycle.getCohPlan().toString()));
            }
            map.put("process", artCycle.getProcess());
            String med = artPrescriptionService.getCycleSummaryMedicalByCycleId(artCycle.getId());
            map.put("unescapeMed", med);
            return PrintUtils.printPdf(fileName, map);
        } catch (Exception e) {
            ArtCycleServiceImpl.log.error("打印PDF异常", e);
            throw new BusinessException(ResultEnum.FAILED.getCode(), "打印PDF异常");
        }
    }

    @Override
    public List<ArtCycle> getInfoBypatientIds(String patientIds) {
        if (StrUtil.isEmpty(patientIds)) {
            return null;
        }
        List<Long> ids = StrUtil.split(patientIds, CharUtil.COMMA).stream().mapToLong(Long::valueOf).boxed().collect(Collectors.toList());
        List<ArtCycle> artCycles = artCycleMapper.selectList(Wrappers.lambdaQuery(ArtCycle.class)
            .and(wrapper -> wrapper.in(ArtCycle::getPatientFemaleId, ids).or().in(ArtCycle::getPatientMaleId, ids))
            .orderByDesc(ArtCycle::getCycleBeginTime));
        return artCycles;
    }

    @Override
    public ArtCycle getLastCycleInfo(Long id, Long patientId) {
        List<ArtCycle> artCycles = artCycleMapper.selectList(Wrappers.lambdaQuery(ArtCycle.class).ne(ArtCycle::getId, id)
            .and(wrapper -> wrapper.eq(ArtCycle::getPatientFemaleId, patientId).or().eq(ArtCycle::getPatientMaleId, patientId))
            .orderByDesc(ArtCycle::getCycleBeginTime));
        if (CollectionUtils.isNotEmpty(artCycles)) {
            return artCycles.get(0);
        } else {
            return null;
        }
    }

    @Override
    public ByteArrayOutputStream exportIndexPdf(ArtCycle artCycle, String fileName) {
        try {
            Map<String, Object> map = new HashMap<>();
            Long cycleId = artCycle.getId();
            map.put("hospitalName", configRuleCache.getConfigRule().get(0).getHospitalName());
            map.putAll(PrintUtils.getCycleBasicData(artCycle));
            map.put("patientFemaleId", artCycle.getPatientFemaleIdentifyId());
            map.put("patientMaleId", artCycle.getPatientMaleIdentifyId());
            PatientInfo patientInfo = patientInfo = patientInfoService.get(artCycle.getPatientFemaleId());
            map.put("patientFemaleBirthday", DateUtil.format(patientInfo.getBirthday(), DatePattern.NORM_DATE_FORMAT));
            map.put("fIdCardType", baseDataPrintCache.getByCodeAndValue("idCardType", patientInfo.getIdCardType().toString()));
            if (null != patientInfo.getProfession()) {
                patientInfo.setProfession(DataDictionaryUtil.getValue(patientInfo.getProfession()));
            }
            map.put("fPatientInfo", patientInfo);
            patientInfo = patientInfoService.get(artCycle.getPatientMaleId());
            map.put("patientMaleBirthday", DateUtil.format(patientInfo.getBirthday(), DatePattern.NORM_DATE_FORMAT));
            map.put("mIdCardType", baseDataPrintCache.getByCodeAndValue("idCardType", patientInfo.getIdCardType().toString()));
            if (null != patientInfo.getProfession()) {
                patientInfo.setProfession(DataDictionaryUtil.getValue(patientInfo.getProfession()));
            }
            map.put("mPatientInfo", patientInfo);
            if (null != artCycle.getCycleEndTime()) {
                map.put("cycleTime",
                    DateUtil.format(artCycle.getCycleBeginTime(), DatePattern.NORM_DATE_FORMAT) + "~" + DateUtil.format(artCycle.getCycleEndTime(),
                        DatePattern.NORM_DATE_FORMAT));
            } else {
                map.put("cycleTime", DateUtil.format(artCycle.getCycleBeginTime(), DatePattern.NORM_DATE_FORMAT) + "~");
            }
            StringBuilder femaleDiagnos = new StringBuilder();
            List<ArtDiagnos> artDiagnosList = artDiagnosService.getListByCycleIdAndPatientId(cycleId, artCycle.getPatientFemaleId(), 0);
            for (int i = 0; i < artDiagnosList.size(); i++) {
                Map<String, String> diagnosMap = org.apache.commons.beanutils.BeanUtils.describe(artDiagnosList.get(i));
                baseDataPrintCache.changeMap(diagnosMap);
                femaleDiagnos.append("<p>" + (i + 1) + KArtConstants.Symbols.PAUSE);
                if (StrUtil.isNotEmpty(diagnosMap.get("diagnosesSite"))) {
                    femaleDiagnos.append("[" + diagnosMap.get("diagnosesSite") + "]");
                }
                femaleDiagnos.append(diagnosMap.get("diagnosesContent"));
                if (StrUtil.isNotEmpty(diagnosMap.get("diagnosesCode"))) {
                    femaleDiagnos.append(KArtConstants.Symbols.EN_COMMA).append(diagnosMap.get("diagnosesCode"));
                }
                if (StrUtil.isNotEmpty(diagnosMap.get("diagnosesExplain"))) {
                    femaleDiagnos.append("[" + diagnosMap.get("diagnosesExplain") + "]");
                }
                femaleDiagnos.append(KArtConstants.Symbols.SEMICOLON + "</p>");
            }
            map.put("unescapeFemaleDiagnos", femaleDiagnos.toString());
            StringBuilder femaleOutDiagnos = new StringBuilder();
            artDiagnosList = artDiagnosService.getListByCycleIdAndPatientId(cycleId, artCycle.getPatientFemaleId(), 1);
            for (int i = 0; i < artDiagnosList.size(); i++) {
                Map<String, String> diagnosMap = org.apache.commons.beanutils.BeanUtils.describe(artDiagnosList.get(i));
                baseDataPrintCache.changeMap(diagnosMap);
                femaleOutDiagnos.append("<p>" + (i + 1) + KArtConstants.Symbols.PAUSE);
                if (StrUtil.isNotEmpty(diagnosMap.get("diagnosesSite"))) {
                    femaleOutDiagnos.append("[" + diagnosMap.get("diagnosesSite") + "]");
                }
                femaleOutDiagnos.append(diagnosMap.get("diagnosesContent"));
                if (StrUtil.isNotEmpty(diagnosMap.get("diagnosesCode"))) {
                    femaleOutDiagnos.append(KArtConstants.Symbols.EN_COMMA).append(diagnosMap.get("diagnosesCode"));
                }
                if (StrUtil.isNotEmpty(diagnosMap.get("diagnosesExplain"))) {
                    femaleOutDiagnos.append("[" + diagnosMap.get("diagnosesExplain") + "]");
                }
                femaleOutDiagnos.append(KArtConstants.Symbols.SEMICOLON + "</p>");
            }
            map.put("unescapeFemaleOutDiagnos", femaleOutDiagnos.toString());
            StringBuilder maleDiagnos = new StringBuilder();
            artDiagnosList = artDiagnosService.getListByCycleIdAndPatientId(cycleId, artCycle.getPatientMaleId(), 0);
            for (int i = 0; i < artDiagnosList.size(); i++) {
                Map<String, String> diagnosMap = org.apache.commons.beanutils.BeanUtils.describe(artDiagnosList.get(i));
                baseDataPrintCache.changeMap(diagnosMap);
                maleDiagnos.append("<p>" + (i + 1) + KArtConstants.Symbols.PAUSE);
                if (StrUtil.isNotEmpty(diagnosMap.get("diagnosesSite"))) {
                    maleDiagnos.append("[" + diagnosMap.get("diagnosesSite") + "]");
                }
                maleDiagnos.append(diagnosMap.get("diagnosesContent"));
                if (StrUtil.isNotEmpty(diagnosMap.get("diagnosesCode"))) {
                    maleDiagnos.append(KArtConstants.Symbols.EN_COMMA).append(diagnosMap.get("diagnosesCode"));
                }
                if (StrUtil.isNotEmpty(diagnosMap.get("diagnosesExplain"))) {
                    maleDiagnos.append("[" + diagnosMap.get("diagnosesExplain") + "]");
                }
                maleDiagnos.append(KArtConstants.Symbols.SEMICOLON + "</p>");
            }
            map.put("unescapeMaleDiagnos", maleDiagnos.toString());
            StringBuilder maleOutDiagnos = new StringBuilder();
            artDiagnosList = artDiagnosService.getListByCycleIdAndPatientId(cycleId, artCycle.getPatientMaleId(), 1);
            for (int i = 0; i < artDiagnosList.size(); i++) {
                Map<String, String> diagnosMap = org.apache.commons.beanutils.BeanUtils.describe(artDiagnosList.get(i));
                baseDataPrintCache.changeMap(diagnosMap);
                maleOutDiagnos.append("<p>" + (i + 1) + KArtConstants.Symbols.PAUSE);
                if (StrUtil.isNotEmpty(diagnosMap.get("diagnosesSite"))) {
                    maleOutDiagnos.append("[" + diagnosMap.get("diagnosesSite") + "]");
                }
                maleOutDiagnos.append(diagnosMap.get("diagnosesContent"));
                if (StrUtil.isNotEmpty(diagnosMap.get("diagnosesCode"))) {
                    maleOutDiagnos.append(KArtConstants.Symbols.EN_COMMA).append(diagnosMap.get("diagnosesCode"));
                }
                if (StrUtil.isNotEmpty(diagnosMap.get("diagnosesExplain"))) {
                    maleOutDiagnos.append("[" + diagnosMap.get("diagnosesExplain") + "]");
                }
                maleOutDiagnos.append(KArtConstants.Symbols.SEMICOLON + "</p>");
            }
            map.put("unescapeMaleOutDiagnos", maleOutDiagnos.toString());
            if (ObjectUtil.isNotNull(artCycle.getTreatmentPlan())) {
                map.put("applicableTreatmentPlan",
                    baseDataPrintCache.getByCodeAndValue("applicableTreatmentPlan", artCycle.getTreatmentPlan().toString()));
            }
            if (null != artCycle.getActualTreatmentPlan()) {
                map.put("treatmentPlan", KArtActualTreatmentPlanEnum.getName(artCycle.getActualTreatmentPlan()));
            }
            if (null != artCycle.getCohPlan()) {
                map.put("coh", baseDataPrintCache.getByCodeAndValue("cohPlans", artCycle.getCohPlan().toString()));
            }
            StringBuilder eggSourcesStr = new StringBuilder();
            if (StrUtil.isNotEmpty(artCycle.getEggSource())) {
                for (String str : artCycle.getEggSource().split(",")) {
                    eggSourcesStr.append(baseDataPrintCache.getByCodeAndValue("eggSources", str)).append(KArtConstants.Symbols.PAUSE);
                }
                map.put("eggSources", eggSourcesStr.toString().substring(0, eggSourcesStr.toString().length() - 1));
            }
            eggSourcesStr = new StringBuilder();
            if (StrUtil.isNotEmpty(artCycle.getActualEggSource())) {
                for (String str : artCycle.getActualEggSource().split(",")) {
                    eggSourcesStr.append(baseDataPrintCache.getByCodeAndValue("eggSources", str)).append(KArtConstants.Symbols.PAUSE);
                }
                map.put("actualEggSource", eggSourcesStr.toString().substring(0, eggSourcesStr.toString().length() - 1));
            }
            StringBuilder spermSourceStr = new StringBuilder();
            if (StrUtil.isNotEmpty(artCycle.getSpermSource())) {
                for (String str : artCycle.getSpermSource().split(",")) {
                    spermSourceStr.append(baseDataPrintCache.getByCodeAndValue("spermSources", str)).append(KArtConstants.Symbols.PAUSE);
                }
                map.put("spermSources", spermSourceStr.toString().substring(0, spermSourceStr.toString().length() - 1));
            }
            spermSourceStr = new StringBuilder();
            if (StrUtil.isNotEmpty(artCycle.getActualSpermSource())) {
                for (String str : artCycle.getActualSpermSource().split(",")) {
                    spermSourceStr.append(baseDataPrintCache.getByCodeAndValue("spermSample", str)).append(KArtConstants.Symbols.PAUSE);
                }
                map.put("actualSpermSource", spermSourceStr.toString().substring(0, spermSourceStr.toString().length() - 1));
            }
            int i = 1;
            StringBuilder surgery = new StringBuilder();
            List<EggSurgery> eggSurgeries = eggSurgeryService.getSurgeryListByCycleId(cycleId);
            for (EggSurgery eggSurgery : eggSurgeries) {
                surgery.append("<p>").append(i).append(KArtConstants.Symbols.PAUSE).append("取卵手术").append(KArtConstants.Symbols.SPACE)
                    .append(DateUtil.format(eggSurgery.getOpuBeginTime(), DatePattern.NORM_DATE_FORMAT)).append(KArtConstants.Symbols.SPACE)
                    .append(DateUtil.format(eggSurgery.getOpuBeginTime(), "HH:mm")).append(KArtConstants.Symbols.AXLE_WIRE)
                    .append(DateUtil.format(eggSurgery.getOpuEndTime(), "HH:mm")).append("</p>");
                i++;
            }
            List<ArtEmbryoTransfer> artEmbryoTransfers = artEmbryoTransferService.getByCycleId(cycleId, 1);
            for (ArtEmbryoTransfer artEmbryoTransfer : artEmbryoTransfers) {
                surgery.append("<p>").append(i).append(KArtConstants.Symbols.PAUSE).append("移植手术").append(KArtConstants.Symbols.SPACE)
                    .append(DateUtil.format(artEmbryoTransfer.getSurgeryDate(), DatePattern.NORM_DATE_FORMAT)).append(KArtConstants.Symbols.SPACE)
                    .append(DateUtil.format(artEmbryoTransfer.getBeginTime(), "HH:mm")).append(KArtConstants.Symbols.AXLE_WIRE)
                    .append(DateUtil.format(artEmbryoTransfer.getEndTime(), "HH:mm")).append("</p>");
                i++;
            }
            List<ArtArtificialInseminationVO> artificialInseminationVOS = artArtificialInseminationService.getListByCycleId(cycleId);
            for (ArtArtificialInseminationVO artArtificialInseminationVO : artificialInseminationVOS) {
                surgery.append("<p>").append(i).append(KArtConstants.Symbols.PAUSE).append("人授手术").append(KArtConstants.Symbols.SPACE)
                    .append(DateUtil.format(artArtificialInseminationVO.getSurgeryDate(), DatePattern.NORM_DATE_FORMAT))
                    .append(KArtConstants.Symbols.SPACE).append(DateUtil.format(artArtificialInseminationVO.getBeginTime(), "HH:mm")).append("</p>");
                i++;
            }
            List<SurgerySpecialOrder> surgerySpecialOrders = surgerySpecialOrderService.getFinishListByCycleId(cycleId);
            for (SurgerySpecialOrder surgerySpecialOrder : surgerySpecialOrders) {
                surgery.append("<p>").append(i).append(KArtConstants.Symbols.PAUSE)
                    .append(ArtCycleServiceImpl.changeName(surgerySpecialOrder.getName())).append(KArtConstants.Symbols.SPACE)
                    .append(DateUtil.format(surgerySpecialOrder.getSurgeryDate(), DatePattern.NORM_DATE_FORMAT)).append(KArtConstants.Symbols.SPACE)
                    .append(DateUtil.format(surgerySpecialOrder.getStartTime(), "HH:mm")).append(KArtConstants.Symbols.AXLE_WIRE)
                    .append(DateUtil.format(surgerySpecialOrder.getFinishTime(), "HH:mm")).append("</p>");
                i++;
            }
            map.put("unescapeSurgery", surgery);
            map.put("cycleOutcome", baseDataPrintCache.getByCodeAndValue("cycleOutcome", artCycle.getCycleOutcome().toString()));
            map.put("followupOutcome", baseDataPrintCache.getByCodeAndValue("followupOutcome", StrUtil.toString(artCycle.getFollowupOutcome())));
            List<ArtEmbryo> artEmbryos = artEmbryoService.getFzByCycleId(cycleId);
            if (CollectionUtils.isNotEmpty(artEmbryos)) {
                map.put("fzEmbryo", "有");
            } else {
                map.put("fzEmbryo", "无");
            }
            List<PatientAllergicHistory> patientAllergicHistory = patientAllergicHistoryService.findListByPatientId(artCycle.getPatientFemaleId());
            if (CollectionUtils.isNotEmpty(patientAllergicHistory)) {
                map.put("allergicHistory", "有");
            } else {
                map.put("allergicHistory", "无");
            }
            List<FollowUpOhss> followUpOhsses = followUpOhssService.getListByCycleId(cycleId);
            if (CollectionUtils.isNotEmpty(followUpOhsses)) {
                AtomicInteger num = new AtomicInteger(0);
                AtomicReference<String> degree = new AtomicReference<>("");
                followUpOhsses.forEach(followUpOhss -> {
                    if (StrUtil.isNotEmpty(followUpOhss.getDegree())) {
                        if (Integer.parseInt(DataDictionaryUtil.getCode(followUpOhss.getDegree())) >= num.get()) {
                            num.set(Integer.parseInt(DataDictionaryUtil.getCode(followUpOhss.getDegree())));
                            degree.set(DataDictionaryUtil.getValue(followUpOhss.getDegree()));
                        }
                    }
                });
                map.put("followUpOhss", degree.get());
            } else {
                map.put("followUpOhss", "无");
            }
            StringBuilder otherInfo = new StringBuilder();
            List<FollowUpPostoperativeBleeding> followUpPostoperativeBleedings = followUpPostoperativeBleedingService.getListByCycleId(cycleId);
            followUpPostoperativeBleedings.forEach(followUpPostoperativeBleeding -> {
                otherInfo.append("术后出血").append(KArtConstants.Symbols.SPACE)
                    .append(DateUtil.format(followUpPostoperativeBleeding.getOccurrenceDate(), DatePattern.NORM_DATE_FORMAT))
                    .append(KArtConstants.Symbols.PAUSE);
            });
            List<FollowUpPostoperativeInfection> followUpPostoperativeInfections = followUpPostoperativeInfectionService.getListByCycleId(cycleId);
            followUpPostoperativeInfections.forEach(followUpPostoperativeInfection -> {
                otherInfo.append("术后感染").append(KArtConstants.Symbols.SPACE)
                    .append(DateUtil.format(followUpPostoperativeInfection.getInfectionDate(), DatePattern.NORM_DATE_FORMAT))
                    .append(KArtConstants.Symbols.PAUSE);
            });
            List<FollowUpOtherComplication> followUpOtherComplications = followUpOtherComplicationService.getListByCycleId(cycleId);
            followUpOtherComplications.forEach(followUpOtherComplication -> {
                otherInfo.append(followUpOtherComplication.getName()).append(KArtConstants.Symbols.SPACE)
                    .append(DateUtil.format(followUpOtherComplication.getOccurrenceDate(), DatePattern.NORM_DATE_FORMAT))
                    .append(KArtConstants.Symbols.PAUSE);
            });
            if (StrUtil.isNotEmpty(otherInfo)) {
                map.put("otherInfo", otherInfo.substring(0, otherInfo.length() - 1));
            }
            if (artCycle.getHospitalDoctorId() != null) {
                SysUser sysUser = userMapper.selectById(artCycle.getHospitalDoctorId());
                if (sysUser != null) {
                    SignPrintUtil.putUserSignToMap(map, "hospitalSign", sysUser, imgUrl);
                }
            }
            if (artCycle.getAttendingDoctorId() != null) {
                SysUser sysUser = userMapper.selectById(artCycle.getAttendingDoctorId());
                if (sysUser != null) {
                    SignPrintUtil.putUserSignToMap(map, "attendingSign", sysUser, imgUrl);
                }
            }
            if (artCycle.getMaleDoctorId() != null) {
                SysUser sysUser = userMapper.selectById(artCycle.getMaleDoctorId());
                if (sysUser != null) {
                    SignPrintUtil.putUserSignToMap(map, "maleSign", sysUser, imgUrl);
                }
            }
            if (artCycle.getQcDoctorId() != null) {
                SysUser sysUser = userMapper.selectById(artCycle.getQcDoctorId());
                if (sysUser != null) {
                    SignPrintUtil.putUserSignToMap(map, "qcSign", sysUser, imgUrl);
                }
            }
            map.put("qcTime", DateUtil.formatDate(artCycle.getQcTime()));
            if (artCycle.getMrDoctorId() != null) {
                SysUser sysUser = userMapper.selectById(artCycle.getMrDoctorId());
                if (sysUser != null) {
                    SignPrintUtil.putUserSignToMap(map, "mrSign", sysUser, imgUrl);
                }
            }
            if (artCycle.getArtDoctorId() != null) {
                SysUser sysUser = userMapper.selectById(artCycle.getArtDoctorId());
                if (sysUser != null) {
                    SignPrintUtil.putUserSignToMap(map, "artSign", sysUser, imgUrl);
                }
            }
            map.put("mrLevel", artCycle.getMrLevel());
            return PrintUtils.printPdf(fileName, map);
        } catch (Exception e) {
            ArtCycleServiceImpl.log.error("打印PDF异常", e);
            throw new BusinessException(ResultEnum.FAILED.getCode(), "打印PDF异常");
        }
    }

    private static String changeName(int name) {
        if (name == KArtConstants.SurgerySpecials.SPERMATORRHEA) {
            return "取精";
        } else if (name == KArtConstants.SurgerySpecials.SURGERYOVARIANCYS) {
            return "卵巢囊肿抽吸";
        } else if (name == KArtConstants.SurgerySpecials.FOLLICULARPUNCTURE) {
            return "卵泡穿刺";
        } else if (name == KArtConstants.SurgerySpecials.TUBALASPIRATION) {
            return "输卵管积液抽吸";
        } else if (name == KArtConstants.SurgerySpecials.DIAGNOSTICCURETTAGE) {
            return "诊断性刮宫";
        } else if (name == KArtConstants.SurgerySpecials.INTRAUTERINEINJECTION) {
            return "宫腔灌注";
        } else if (name == KArtConstants.SurgerySpecials.FETALREDUCTION) {
            return "减胎";
        } else if (name == KArtConstants.SurgerySpecials.UTERINEEFFUSIONASPIRATION) {
            return "宫腔积液抽吸";
        } else if (name == KArtConstants.SurgerySpecials.ENDOMETRIALBIOPSY) {
            return "内膜活检";
        }
        return null;
    }

    private Long insertCycle(ArtCycle entity) {
        // 判断是否外院
        if (entity.getOurHospitalTreatment() == GlobalConstant.YES) {
            String[] sorts = entity.getCycleSort().split("-");
            if (sorts.length == 1) {
                entity.setSorts(Integer.parseInt(sorts[0]));
            } else if (sorts.length == 2) {
                entity.setSorts(Integer.parseInt(sorts[1]));
            } else {
                throw new BusinessException(ResultEnum.FAILED.getCode(), "外院填写的周期序号格式不正确");
            }
            entity.setCycleStatus(KArtConstants.CycleStatus.FINISH);
            entity.setStatus(KArtConstants.AuditStatus.UNAUDITED);
            entity.setRecorderId(AuthUtil.getCurrentAuthInfo().getUserId());
            entity.setRecorderName(AuthUtil.getCurrentAuthInfo().getName());
            entity.setRecorderTime(new Date());
            entity.setArchive(GlobalConstant.NO);
            Long id = super.insert(entity);
            entity.setId(id);
            cycleInfoCache.setCycletInfo(entity);
            return id;
        } else {
            // 判断是否已有进周未结束的周期
            List<ArtCycle> artCycles = artCycleMapper.selectList(
                Wrappers.lambdaQuery(ArtCycle.class).eq(ArtCycle::getPairBondId, entity.getPairBondId())
                    .ne(ArtCycle::getCycleStatus, KArtConstants.CycleStatus.FINISH)
                    .ne(ArtCycle::getCycleStatus, KArtConstants.CycleStatus.OBJECTIVETERMINATION).eq(ArtCycle::getTermination, GlobalConstant.NO));
            if (artCycles.size() > 0) {
                throw new BusinessException(ResultEnum.FAILED.getCode(), "该患者正在周期中，无法新增新周期");
            }
            // domain.setId(KeyGenerate.generateId());
            if (ObjectUtil.isNotNull(entity.getAppointmentId())) {
                ArtAppointment artAppointment = artAppointmentService.get(entity.getAppointmentId());
                // 判断这周期是否已预约
                if (null != artAppointment && artAppointment.getIntoWeek() == GlobalConstant.NO) {
                    entity.setDonor(artAppointment.getIsDonorSperm());
                    entity.setTreatmentPlan(artAppointment.getTreatmentPlan());
                    entity.setCohPlan(artAppointment.getCohPlan());
                    entity.setSpermSource(artAppointment.getSpermSource());
                    entity.setEggSource(artAppointment.getEggSource());
                    artAppointment.setIntoWeek(GlobalConstant.YES);
                    artAppointment.setCycleId(entity.getId());
                    //  artAppointmentService.update(artAppointment);

                    List<ArtWeeklyReviewDetail> artWeeklyReviewDetails = artWeeklyReviewDetailService.getList(
                        Wrappers.lambdaQuery(ArtWeeklyReviewDetail.class).eq(ArtWeeklyReviewDetail::getArtAppointmentId, artAppointment.getId()));
                    if (CollectionUtil.isNotEmpty(artWeeklyReviewDetails)) {
                        artWeeklyReviewDetails.forEach(artWeeklyReviewDetail -> {
                            artWeeklyReviewDetail.setCycleId(entity.getId());
                            artWeeklyReviewDetailService.update(artWeeklyReviewDetail);
                        });
                    }

                    List<ArtWeeklyReviewProcess> artWeeklyReviewProcesses = artWeeklyReviewProcessService.getList(
                        Wrappers.lambdaQuery(ArtWeeklyReviewProcess.class).eq(ArtWeeklyReviewProcess::getArtAppointmentId, artAppointment.getId()));
                    if (CollectionUtil.isNotEmpty(artWeeklyReviewProcesses)) {
                        artWeeklyReviewProcesses.forEach(artWeeklyReviewProcess -> {
                            artWeeklyReviewProcess.setCycleId(entity.getId());
                            artWeeklyReviewProcessService.updateCycleId(artWeeklyReviewProcess);
                        });
                    }
                    if (artAppointment.getIsDonorSperm() == GlobalConstant.YES) {
                        ArtSpermApplication artSpermApplication = artSpermApplicationService.getById(artAppointment.getId());
                        if (null != artSpermApplication) {
                            artSpermApplication.setCycleId(entity.getId());
                            artSpermApplicationService.update(artSpermApplication);
                            entity.setDonor(GlobalConstant.YES);
                        }
                    }
                } else {
                    entity.setAppointmentId(null);
                    entity.setDonor(GlobalConstant.NO);
                }
            } else {
                entity.setDonor(GlobalConstant.NO);
            }
            entity.setCycleStatus(-1);
            entity.setCycleBeginTime(new Date());
            // 获取该患者最大序号
            int maxNum = artCycleMapper.getMaxCycleSort(entity.getPairBondId());
            if (maxNum == 0) {
                entity.setCycleSort("1");
                entity.setSorts(1);
            } else {
                entity.setCycleSort(maxNum + 1 + "");
                entity.setSorts(maxNum + 1);
            }
            entity.setArchive(GlobalConstant.NO);
            entity.setTermination(GlobalConstant.NO);
            entity.setStatus(KArtConstants.AuditStatus.UNAUDITED);
            BaseTreatmentTeam baseTreatmentTeam = baseTreatmentTeamService.getByMenberId(entity.getArtDoctorId());
            if (null != baseTreatmentTeam) {
                entity.setTeamId(baseTreatmentTeam.getId());
                entity.setTeamName(baseTreatmentTeam.getName());
            }
            entity.setRecorderId(AuthUtil.getCurrentAuthInfo().getUserId());
            entity.setRecorderName(AuthUtil.getCurrentAuthInfo().getName());
            entity.setRecorderTime(new Date());
            Long id = super.insert(entity);
            if (ObjectUtil.isNotNull(entity.getAppointmentId())) {
                ArtAppointment artAppointment = artAppointmentService.get(entity.getAppointmentId());
                // 判断这周期是否已预约
                if (null != artAppointment && artAppointment.getIntoWeek() == GlobalConstant.NO) {
                    artAppointment.setIntoWeek(GlobalConstant.YES);
                    artAppointment.setCycleId(id);
                    artAppointmentService.update(artAppointment);

                }
            }
            archivalCycleInfoService.createArchivalCycleInfo(id);
            cycleInfoCache.setCycletInfo(entity);
            return id;
        }
    }

    private void auditCycle(Long id) {
        ArtCycle artCycle = this.get(id);
        if (artCycle != null) {
            if (artCycle.getStatus().intValue() == KArtConstants.AuditStatus.AUDITED) {
                throw new BusinessException(ResultEnum.FAILED.getCode(), "周期已经审核，无法再次审核");
            }
            if (artCycle.getOurHospitalTreatment() == GlobalConstant.NO) {
                ArtCycleServiceImpl.hasCycleError(artCycle);
                artCycle.setCycleStatus(KArtConstants.CycleStatus.WEEK);
                artCycle.setCycleOutcome(KArtConstants.CycleOutcome.INTREATMENT);
                // artCycle.setFollowupOutcome(KArtConstants.FollowupOutcome.NOTPREGNANT);
                // 插入进周建档数据
                ArtCycleReview artCycleReview = new ArtCycleReview();
                BeanUtils.copyProperties(artCycle, artCycleReview);
                artCycleReview.setCycleId(id);
                ArtAppointment artAppointment = artAppointmentService.get(artCycle.getAppointmentId());
                // 判断这周期是否已预约
                if (null != artAppointment && artAppointment.getIntoWeek() == GlobalConstant.NO) {
                    artCycleReview.setAppointmentDate(artAppointment.getEstablishmentDate());
                }
                artCycleReview.setWeeklyStatus(artCycle.getCycleStatus());
                artCycleReviewService.insert(artCycleReview);
                ArtCycleStatusChangeService artCycleStatusChangeService = SpringUtil.getBean(ArtCycleStatusChangeService.class);
                artCycleStatusChangeService.createArtCycleStatusChange(artCycle.getId(), KArtConstants.CycleStatus.WEEK);
            }
            artCycle.setStatus(KArtConstants.AuditStatus.AUDITED);
            this.update(artCycle);
        }
    }

    private void updateCycle(ArtCycle entity) {
        ArtCycle artCycle = this.get(entity.getId());
        if (artCycle != null) {
            if (artCycle.getStatus().intValue() == KArtConstants.AuditStatus.AUDITED) {
                throw new BusinessException(ResultEnum.FAILED.getCode(), "进周已经审核，无法进行操作");
            }
            if (artCycle.getOurHospitalTreatment() == GlobalConstant.NO) {
                ArtCycleServiceImpl.hasCycleError(artCycle);
            } else {
                String[] sorts = entity.getCycleSort().split("-");
                if (sorts.length == 1) {
                    artCycle.setSorts(Integer.parseInt(sorts[0]));
                } else if (sorts.length == 2) {
                    artCycle.setSorts(Integer.parseInt(sorts[1]));
                }
            }
            BaseTreatmentTeam baseTreatmentTeam = baseTreatmentTeamService.getByMenberId(entity.getArtDoctorId());
            if (null != baseTreatmentTeam) {
                artCycle.setTeamId(baseTreatmentTeam.getId());
                artCycle.setTeamName(baseTreatmentTeam.getName());
            }
            artCycle.setRecorderId(AuthUtil.getCurrentAuthInfo().getUserId());
            artCycle.setRecorderName(AuthUtil.getCurrentAuthInfo().getName());
            artCycle.setRecorderTime(new Date());
            if (entity.getArtDoctorId() != null) {
                artCycle.setArtDoctorId(entity.getArtDoctorId());
                artCycle.setArtDoctorName(entity.getArtDoctorName());
            }
            this.update(artCycle);
        }
    }

    private void revokeAuditCycle(Long id) {
        ArtCycle artCycle = this.get(id);
        if (artCycle != null) {
            if (artCycle.getArchive().intValue() == GlobalConstant.YES) {
                throw new BusinessException(ResultEnum.FAILED.getCode(), "周期已归档，无法进行操作");
            }
            if (artCycle.getStatus().intValue() != KArtConstants.AuditStatus.AUDITED) {
                throw new BusinessException(ResultEnum.FAILED.getCode(), "进周未审核，无法进行操作");
            }
            if (artCycle.getOurHospitalTreatment() == GlobalConstant.NO) {
                ArtCycleServiceImpl.hasCycleError(artCycle);
            }
            artCycle.setStatus(KArtConstants.AuditStatus.REVERSED);
            this.update(artCycle);
            ArtCycleReview artCycleReview = artCycleReviewService.get(Wrappers.lambdaQuery(ArtCycleReview.class).eq(ArtCycleReview::getCycleId, id));
            if (null != artCycleReview) {
                artCycleReviewService.delete(artCycleReview.getId());
            }
            ArtCycleStatusChangeService artCycleStatusChangeService = SpringUtil.getBean(ArtCycleStatusChangeService.class);
            artCycleStatusChangeService.deleteArtCycleStatusChange(artCycle.getId(), artCycle.getCycleStatus());
        }
    }

    private void deleteCycle(Long id) {
        ArtCycle artCycle = this.get(id);
        if (artCycle != null) {
            if (artCycle.getStatus().intValue() == KArtConstants.AuditStatus.AUDITED) {
                throw new BusinessException(ResultEnum.FAILED.getCode(), "进周已经审核，无法进行操作");
            }
            if (artCycle.getOurHospitalTreatment() == GlobalConstant.NO) {
                ArtCycleServiceImpl.hasCycleError(artCycle);
                ArtAppointment artAppointment = artAppointmentService.get(artCycle.getAppointmentId());
                // 判断这周期是否已预约
                if (null != artAppointment && artAppointment.getIntoWeek() == GlobalConstant.YES) {
                    artAppointment.setIntoWeek(GlobalConstant.NO);
                    artAppointment.setCycleId(null);
                    artAppointmentService.update(artAppointment);
                }
                archivalCycleInfoService.deleteArchivalCycldInfo(id);
                if (StrUtil.isNotEmpty(artCycle.getCaseNo())) {
                    Map<Object, Object> caseNoMaps = redisMapUtil.hmget(recycleKey);
                    String nowDate = DateUtil.format(new Date(), "yyyy");
                    if (caseNoMaps.containsKey(nowDate)) {
                        List<String> caseNos = (List<String>)caseNoMaps.get(nowDate);
                        caseNos.add(artCycle.getCaseNo());
                    } else {
                        List<String> caseNos = new ArrayList<>();
                        caseNos.add(artCycle.getCaseNo());
                        caseNoMaps.put(nowDate, caseNos);
                    }
                    redisMapUtil.hmsetByObject(recycleKey, caseNoMaps);
                }
            }
            super.delete(id);
            cycleInfoCache.delArtCycle(id);
            // 根据周期删除病史数据
            artFemaleHistoryService.delete(Wrappers.lambdaQuery(ArtFemaleHistory.class).eq(ArtFemaleHistory::getCycleId, id));
            artMaleHistoryService.delete(Wrappers.lambdaQuery(ArtMaleHistory.class).eq(ArtMaleHistory::getCycleId, id));
            artFemalePhysicalService.delete(Wrappers.lambdaQuery(ArtFemalePhysical.class).eq(ArtFemalePhysical::getCycleId, id));
            artMalePhysicalService.delete(Wrappers.lambdaQuery(ArtMalePhysical.class).eq(ArtMalePhysical::getCycleId, id));
            choiceTargetService.delete(Wrappers.lambdaQuery(ArtChoiceTarget.class).eq(ArtChoiceTarget::getCycleId, id));
            artChoiceInspectService.delete(Wrappers.lambdaQuery(ArtChoiceInspect.class).eq(ArtChoiceInspect::getCycleId, id));
            artChoiceUltrasonicService.delete(Wrappers.lambdaQuery(ArtChoiceUltrasonic.class).eq(ArtChoiceUltrasonic::getCycleId, id));
            artSummaryService.delete(Wrappers.lambdaQuery(ArtSummary.class).eq(ArtSummary::getCycleId, id));
            artDiagnosService.delete(Wrappers.lambdaQuery(ArtDiagnos.class).eq(ArtDiagnos::getCycleId, id));
            artIndicationService.delete(Wrappers.lambdaQuery(ArtIndication.class).eq(ArtIndication::getCycleId, id));
            artTreatmentRecommendationService.delete(
                Wrappers.lambdaQuery(ArtTreatmentRecommendation.class).eq(ArtTreatmentRecommendation::getCycleId, id));
            todoLabService.delete(Wrappers.lambdaQuery(TodoLab.class).eq(TodoLab::getCycleId, id));
            todoClinicalService.delete(Wrappers.lambdaQuery(TodoClinical.class).eq(TodoClinical::getCycleId, id));
            todoCohService.delete(Wrappers.lambdaQuery(TodoCoh.class).eq(TodoCoh::getCycleId, id));
            todoEtService.delete(Wrappers.lambdaQuery(TodoEt.class).eq(TodoEt::getCycleId, id));
            todoSpecialSurgeryService.delete(Wrappers.lambdaQuery(TodoSpecialSurgery.class).eq(TodoSpecialSurgery::getCycleId, id));
            artCargilleService.delete(Wrappers.lambdaQuery(ArtCargilleList.class).eq(ArtCargilleList::getCycleId, id));
            surgerySpecialOrderService.delete(Wrappers.lambdaQuery(SurgerySpecialOrder.class).eq(SurgerySpecialOrder::getCycleId, id));
            // 取消短信
            List<ConfigMessage> configMessages =
                configMessageService.getList(Wrappers.lambdaQuery(ConfigMessage.class).eq(ConfigMessage::getCycleId, artCycle.getId()));
            configMessages.forEach(configMessage -> {
                configMessage.setStatus(2);
                configMessageService.update(configMessage);
            });
        }
    }

    private static void hasCycleError(ArtCycle artCycle) {
        /**
         * zhoujb 2023-03-02 17:18:17 关闭进周后不能修改周期控制
         */
        /*if (artCycle.getCycleStatus().intValue() != KArtConstants.CycleStatus.WEEK && artCycle.getCycleStatus() != -1) {
            throw new BusinessException(ResultEnum.FAILED.getCode(), "周期已经产生数据，无法进行操作");
        }*/
        if (artCycle.getTermination() == GlobalConstant.YES) {
            throw new BusinessException(ResultEnum.FAILED.getCode(), "周期已经终止，无法进行操作");
        }
    }

    // @Override
    // public Map<String, Object> getBasicPrintData(Long cycleId) {
    // Map<String, Object> map = new HashMap<>();
    // if (cycleId != null) {
    // ArtCycle artCycle = this.get(cycleId);
    // map.put("patientMaleCardNo", artCycle.getPatientMaleCardNo());
    // map.put("patientFemaleCardNo", artCycle.getPatientFemaleCardNo());
    // map.put("caseNo", artCycle.getCaseNo());
    // map.put("patientMaleName", artCycle.getPatientMaleName());
    // map.put("patientFemaleName", artCycle.getPatientFemaleName());
    // map.put("patientMaleAge", artCycle.getPatientMaleAge());
    // map.put("patientFemaleAge", artCycle.getPatientFemaleAge());
    // map.put("patientFemaleIdentifyId", artCycle.getPatientFemaleIdentifyId());
    // map.put("patientMaleIdentifyId", artCycle.getPatientMaleIdentifyId());
    // }
    // return map;
    // }

    // private void updateAge(ArtCycle cycle) {
    // PatientInfo patientInfo = patientInfoService.get(cycle.getPatientFemaleId());
    // if (null != patientInfo) {
    // if (null != patientInfo.getBirthday()) {
    // try {
    // if (GlobalConstant.NO == patientInfo.getIdCardType()) {
    // cycle.setPatientFemaleAge(IdcardUtil.getAgeByIdCard(patientInfo.getIdCard(), new Date()));
    // } else {
    // cycle.setPatientFemaleAge(DateUtil.ageOfNow(patientInfo.getBirthday()));
    // }
    // } catch (Exception e) {
    //
    // }
    // }
    // }
    // patientInfo = patientInfoService.get(cycle.getPatientMaleId());
    // if (null != patientInfo) {
    // if (null != patientInfo.getBirthday()) {
    // try {
    // if (GlobalConstant.NO == patientInfo.getIdCardType()) {
    // cycle.setPatientMaleAge(IdcardUtil.getAgeByIdCard(patientInfo.getIdCard(), new Date()));
    // } else {
    // cycle.setPatientMaleAge(DateUtil.ageOfNow(patientInfo.getBirthday()));
    // }
    // } catch (Exception e) {
    //
    // }
    // }
    // }
    // this.update(cycle);
    // }

    @Override
    public boolean isNeedTalk(Long cycleId) {
        ArtCycle artCycle = cycleInfoCache.getCyeleInfo(cycleId);// get(cycleId);
        if (artCycle == null) {
            artCycle = get(cycleId);
        }
        if (null != artCycle) {
            List<EggSurgery> eggSurgeries = eggSurgeryService.getListByCycleId(artCycle.getId());
            int eggNum = eggSurgeries.stream().mapToInt(EggSurgery::getPickTotal).sum();
            if (eggNum > 15) {
                return true;
            }
            List<UltrasoundMonitor> ultrasoundMonitors = ultrasoundMonitorService.getList(
                Wrappers.lambdaQuery(UltrasoundMonitor.class).eq(UltrasoundMonitor::getCycleId, artCycle.getId())
                    .orderByDesc(UltrasoundMonitor::getMonitorTime));
            if (CollectionUtil.isNotEmpty(ultrasoundMonitors)) {
                UltrasoundEndocrine ultrasoundEndocrine = ultrasoundEndocrineService.get(
                    Wrappers.lambdaQuery(UltrasoundEndocrine.class).eq(UltrasoundEndocrine::getUltrasoundId, ultrasoundMonitors.get(0).getId()));
                if (null != ultrasoundEndocrine && NumberUtil.isNumber(ultrasoundEndocrine.getE2()) && new BigDecimal(
                    ultrasoundEndocrine.getE2()).compareTo(new BigDecimal(15000)) != -1) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendTomorrowCoh(ArtCycle artCycle) {
        try {
            if (artCycle.getCycleStatus() >= KArtConstants.CycleStatus.EGGRETRIEVAL) {
                return;
            }
            // 下次就诊时间
            Date nextVisiDate = artCycle.getNextVisitDate();
            // 发送时间
            DateTime sendDate = ObjectUtil.isNull(nextVisiDate) ? null : DateUtil.offsetDay(DateUtil.beginOfDay(nextVisiDate), -1);
            String sendDateStr = ObjectUtil.isNull(sendDate) ? null : sendDate.toDateStr();
            Map<String, String> map = Maps.newHashMap();
            map.put("fxm", artCycle.getPatientFemaleName());
            map.put("mxm", artCycle.getPatientMaleName());
            map.put("fxm", artCycle.getPatientFemaleName());
            map.put("yysj", ObjectUtil.isNull(nextVisiDate) ? null : DateUtil.format(nextVisiDate, DatePattern.NORM_DATE_FORMAT));
            map.put("zh", "11楼 分诊站");
            map.put("cxzs", "超声诊室");
            ConfigMessageTemp configMessageTemp =
                configMessageTempService.get(Wrappers.lambdaQuery(ConfigMessageTemp.class).eq(ConfigMessageTemp::getName, "明日促排卵复诊"));
            String templateCode = null;
            if ("ali".equals(smsType)) {
                templateCode = "SMS_228852612";
            } else {
                templateCode = configMessageTemp.getCode();// "art003";
            }
            SmsTemplate smsTemplate =
                SmsTemplate.builder().templateCode(templateCode).templateParam(map).messageType(MessageTypeConstants.MessageType.TOMORROW_COH)
                    .messageTempName("明日促排卵复诊").sendTime(sendDateStr).build();
            sendService.generalSend(artCycle, smsTemplate, artCycle.getId());
        } catch (Exception e) {
            ArtCycleServiceImpl.log.error("明日促排卵复诊短信异常", e);
            // throw new BusinessException(ResultEnum.PARAM_PARSE_FAILED.getCode(), "不能重复提交");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PatientCycleVO getByPatientIdentify(String patientIdentify) {
        PatientCycleVO patientCycleVO = new PatientCycleVO();
        PatientInfo patientInfo = patientInfoService.getByUniqueIdentify(patientIdentify);
        if (null == patientInfo) {
            PatientInfo hisPatientInfo = hisPatientService.getPatientInfo(patientIdentify);
            if (null != hisPatientInfo) {
                patientInfo = patientInfoService.getByUniqueIdentify(hisPatientInfo.getCardNo());
                // if (null == patientInfo) {
                // throw new BusinessException(ResultEnum.FAILED.getCode(), "该患者未在生殖系统录入信息，请前往录入");
                // }
                if (patientInfo != null) {
                    List<PatientInfo> patientInfos = new ArrayList<>();
                    patientInfos.add(patientInfo);
                    patientInfoService.saveLongCardNo(patientInfos, patientIdentify, hisPatientInfo.getPatientCaseNo(),
                        hisPatientInfo.getInpatientNo());
                }
            }
        }
        if (patientInfo != null) {
            // 女性患者
            if (GlobalConstant.NO == patientInfo.getGender()) {
                patientCycleVO.setPatientFemale(patientInfo);
                // 当前配偶ID不为空时才去查询配偶信息
                if (patientCycleVO.getPatientFemale().getCurSpouseId() != null) {
                    PatientInfo patientMale = patientInfoService.get(patientCycleVO.getPatientFemale().getCurSpouseId());
                    patientCycleVO.setPatientMale(patientMale);
                }

                // 周期信息查询
                List<ArtCycle> artCycles = artCycleMapper.selectList(
                    Wrappers.lambdaQuery(ArtCycle.class).eq(ArtCycle::getPatientFemaleId, patientInfo.getId())
                        .orderByDesc(ArtCycle::getCycleBeginTime));

                // 20230823 整改调整，去除更改周期年龄操作
                /* artCycles.forEach(artCycle -> {
                    if (artCycle.getCycleStatus().intValue() != KArtConstants.CycleStatus.OBJECTIVETERMINATION
                        && artCycle.getCycleStatus().intValue() != KArtConstants.CycleStatus.FINISH
                        && null != artCycle.getTermination()
                        && artCycle.getTermination().intValue() != GlobalConstant.YES) {
                        updateAge(artCycle);
                    }
                });*/
                patientCycleVO.setArtCycles(artCycles);
            } else {
                // 男性患者
                patientCycleVO.setPatientMale(patientInfo);
            }
        }
        return patientCycleVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCaseNo(Long cycleId) {
        ArtCycle artCycle = this.get(cycleId);
        // ArtCycle artCycle = artCycleMapper.selectById(cycleId);
        String nowDate = DateUtil.format(new Date(), "yyyy");
        List<String> caseNos = (List<String>)redisMapUtil.hget(recycleKey, nowDate);
        if (CollectionUtil.isNotEmpty(caseNos)) {
            artCycle.setCaseNo(caseNos.get(0));
            caseNos.remove(0);
            redisMapUtil.hset(recycleKey, nowDate, caseNos);
        } else {
            artCycle.setCaseNo(serialNoGenerator.generatorSerialNo("art:artcycle:caseNo", 5, "art:artcycle:caseNoDate", "yyyy", null, "ArtCycle"));
        }
        artCycleMapper.updateById(artCycle);
        cycleInfoCache.setCycletInfo(artCycle);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDoctor(EditDoctorVO editDoctorVO) {
        ArtCycle artCycle = this.get(editDoctorVO.getId());
        // ArtCycle artCycle = artCycleMapper.selectById(editDoctorVO.getId());
        if (ObjectUtil.isNotEmpty(artCycle)) {
            BeanUtils.copyProperties(editDoctorVO, artCycle);
            if (StrUtil.isNotEmpty(editDoctorVO.getQcTime())) {
                artCycle.setQcTime(DateUtil.parseDate(editDoctorVO.getQcTime()));
            } else {
                artCycle.setQcTime(null);
            }
            artCycleMapper.updateById(artCycle);
            cycleInfoCache.setCycletInfo(artCycle);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ArtCycle syncBedNo(Long cycleId) {
        ArtCycle artCycle = this.get(cycleId);
        // ArtCycle artCycle = artCycleMapper.selectById(cycleId);
        if (artCycle != null && artCycle.getPatientFemaleId() != null) {
            PatientInfo info = patientInfoService.get(artCycle.getPatientFemaleId());
            if (null == info) {
                return artCycle;
                // throw new BusinessException(ResultEnum.FAILED.getCode(), "该患者未在生殖系统录入信息，请前往录入");
            }
            PatientInfo hisPatientInfo = hisPatientService.getPatientInfo(info.getCardNo(), info.getName());
            if (hisPatientInfo == null) {
                return artCycle;
            }

            if (hisPatientInfo instanceof SyncPatientInfo) {
                SyncPatientInfo syncInfo = (SyncPatientInfo)hisPatientInfo;
                artCycle.setInpatientNo(syncInfo.getInpatientNo());
                artCycle.setBedNo(syncInfo.getBedNo());
                this.update(artCycle);
            }
        }
        return artCycle;
    }

    @Override
    public ArtCycle getLastNoThawCycleByIndentifyId(String identifyId) {
        ArtCycle artCycle = null;
        List<ArtCycle> artCycles = artCycleMapper.selectList(new QueryWrapper<ArtCycle>().lambda().ne(ArtCycle::getActualTreatmentPlan, 22)
            .and(wrapper -> wrapper.eq(ArtCycle::getPatientFemaleIdentifyId, identifyId).or().eq(ArtCycle::getPatientMaleIdentifyId, identifyId)));
        for (ArtCycle item : artCycles) {
            List<FtFrozenStockEmbryoDetail> ftFrozenStockEmbryoDetails = ftFrozenStockEmbryoDetailService.getList(
                Wrappers.lambdaQuery(FtFrozenStockEmbryoDetail.class)
                    .eq(FtFrozenStockEmbryoDetail::getPatientFemaleIdentifyId, item.getPatientFemaleIdentifyId())
                    .eq(FtFrozenStockEmbryoDetail::getStatus, 0));
            if (CollUtil.isNotEmpty(ftFrozenStockEmbryoDetails)) {
                artCycle = item;
                break;
            }
            List<FtFrozenStockOvumDetail> ftFrozenStockOvumDetails = ftFrozenStockOvumDetailService.getList(
                Wrappers.lambdaQuery(FtFrozenStockOvumDetail.class)
                    .eq(FtFrozenStockOvumDetail::getPatientFemaleIdentifyId, item.getPatientFemaleIdentifyId())
                    .eq(FtFrozenStockOvumDetail::getStatus, 0));
            if (CollUtil.isNotEmpty(ftFrozenStockOvumDetails)) {
                artCycle = item;
                break;
            }
            List<FtFrozenStockSpermDetail> ftFrozenStockSpermDetails = ftFrozenStockSpermDetailService.getList(
                Wrappers.lambdaQuery(FtFrozenStockSpermDetail.class)
                    .eq(FtFrozenStockSpermDetail::getPatientMaleIdentifyId, item.getPatientMaleIdentifyId())
                    .eq(FtFrozenStockSpermDetail::getStatus, 0));
            if (CollUtil.isNotEmpty(ftFrozenStockSpermDetails)) {
                artCycle = item;
                break;
            }
        }
        return artCycle;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateImage(Long cycleId, String images) {
        ArtCycle artCycle = this.get(cycleId);
        // ArtCycle artCycle = artCycleMapper.selectById(cycleId);
        artCycle.setObPgtImg(images);
        this.update(artCycle);
    }

    @Override
    public List<DonorSpermCycleVO> getDonorSpermList(ParamDonorSpermStaticVO param) {
        return artCycleMapper.getDonorSpermList(param);
    }

    @Override
    public List<CycleInfoVo> getListByDate(Date date) {
        List<CycleInfoVo> cycleInfoVoList = new ArrayList<>();
        List<TodoClinical> todoClinicals = todoClinicalService.getList(
            Wrappers.lambdaQuery(TodoClinical.class).ge(TodoClinical::getExecuteTime, DateUtil.beginOfDay(date))
                .le(TodoClinical::getExecuteTime, DateUtil.endOfDay(date)));
        if (CollUtil.isNotEmpty(todoClinicals)) {
            List<Long> cycleIds =
                todoClinicals.stream().map(TodoClinical::getCycleId).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
            List<ArtCycle> cycleList = artCycleMapper.selectBatchIds(cycleIds);
            cycleList.forEach(artCycle -> {
                CycleInfoVo cycleInfoVo = new CycleInfoVo();
                cycleInfoVo.setMedicalRecordNo(artCycle.getCaseNo());
                cycleInfoVo.setCycleNo(String.valueOf(artCycle.getCycleNo()));
                cycleInfoVo.setCycleEnterDate(artCycle.getCycleBeginTime());
                cycleInfoVo.setCycleEnd(artCycle.getCycleStatus());
                PatientInfo femaleInfo = patientInfoService.get(artCycle.getPatientFemaleId());
                cycleInfoVo.setFemaleName(femaleInfo.getName());
                cycleInfoVo.setFemaleIdentifyId(femaleInfo.getIdentifyId());
                cycleInfoVo.setFemaleCertificateType(femaleInfo.getIdCardType() + "");
                cycleInfoVo.setFemaleCertificateNo(femaleInfo.getCardNo());
                cycleInfoVo.setFemaleBirthday(femaleInfo.getBirthday());
                PatientInfo maleInfo = patientInfoService.get(artCycle.getPatientMaleId());
                cycleInfoVo.setMaleName(maleInfo.getName());
                cycleInfoVo.setMaleIdentifyId(maleInfo.getIdentifyId());
                cycleInfoVo.setMaleCertificateType(maleInfo.getIdCardType() + "");
                cycleInfoVo.setMaleCertificateNo(maleInfo.getCardNo());
                cycleInfoVo.setMaleBirthday(maleInfo.getBirthday());
                cycleInfoVo.setArtPlan(artCycle.getActualTreatmentPlan() + "");
                List<ArtPlan> artPlans = artPlanService.getList(Wrappers.lambdaQuery(ArtPlan.class).eq(ArtPlan::getCycleId, artCycle.getId())
                    .eq(ArtPlan::getPlanStatus, KArtConstants.ArtPlanStatus.DEFAULT).orderByDesc(ArtPlan::getBillingTime));
                if (CollUtil.isNotEmpty(artPlans)) {
                    ArtPlan artPlan = artPlans.get(0);
                    List<ArtPlanAi> artPlanAis = artPlanAiService.getByPlanId(artPlan.getId());
                    if (CollUtil.isNotEmpty(artPlanAis)) {
                        cycleInfoVo.setAiDate(artPlanAis.get(0).getAiTime());
                    }
                    List<ArtPlanEt> artPlanEts = artPlanEtService.getByPlanId(artPlan.getId());
                    if (CollUtil.isNotEmpty(artPlanEts)) {
                        cycleInfoVo.setEtDate(artPlanEts.get(0).getEtTime());
                    }
                    ArtPlanOvum artPlanOvum = artPlanOvumService.getByPlanId(artPlan.getId());
                    if (artPlanOvum != null) {
                        cycleInfoVo.setOpuDate(artPlanOvum.getOpuTime());
                    }
                    List<ArtPlanThawEmbryo> artPlanThawEmbryos = artPlanThawEmbryoService.getByPlanId(artPlan.getId());
                    if (CollUtil.isNotEmpty(artPlanThawEmbryos)) {
                        cycleInfoVo.setEtDate(artPlanThawEmbryos.get(0).getThawTime());
                    }
                }
                cycleInfoVo.setOtherOperationsDate(null);
                cycleInfoVo.setFollicleNumber(0);
                cycleInfoVo.setSemenSource(artCycle.getSpermSource());
                cycleInfoVoList.add(cycleInfoVo);
                // BeanUtils.copyProperties(artCycle,cycleInfoVo);

            });

        }

        return cycleInfoVoList;
    }
}
