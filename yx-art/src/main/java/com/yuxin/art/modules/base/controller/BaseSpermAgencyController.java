/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:BaseSpermAgencyController.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.base.controller;

import cn.hutool.core.bean.BeanUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.yuxin.api.Result;
import com.yuxin.art.domain.base.BaseSpermAgency;
import com.yuxin.art.modules.base.service.BaseSpermAgencyService;
import com.yuxin.art.modules.base.vo.AddBaseSpermAgencyVO;
import com.yuxin.art.modules.base.vo.EditBaseSpermAgencyVO;
import com.yuxin.constant.GlobalConstant;
import com.yuxin.framework.mvc.controller.BaseController;
import com.yuxin.framework.mvc.vo.IdVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 供精机构 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-29
 */
@RestController
@RequestMapping(value = "/base/spermAgency", produces = MediaType.APPLICATION_JSON_VALUE)
@Validated
@Api(tags = "供精机构")
@ApiSort(1)
public class BaseSpermAgencyController extends BaseController {
    @Autowired
    private BaseSpermAgencyService baseSpermAgencyService;

    /**
     * 新增
     *
     * @param addBaseSpermAgencyVO
     * @return
     */
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "新增", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 1)
    // @RequiresPermissions("base:spermAgency:create")
    public Result<IdVO> create(@RequestBody @Validated AddBaseSpermAgencyVO addBaseSpermAgencyVO) {
        BaseSpermAgency baseSpermAgency = new BaseSpermAgency();
        BeanUtil.copyProperties(addBaseSpermAgencyVO, baseSpermAgency);
        Long id = baseSpermAgencyService.save(baseSpermAgency);
        return Result.success(new IdVO(id));
    }

    /**
     * 删除
     *
     * @param id
     */
    @DeleteMapping
    @ApiOperation(value = "删除")
    @ApiImplicitParam(name = "id", value = "id", required = true, paramType = "query", dataType = "String")
    @ApiOperationSupport(order = 2)
    // @RequiresPermissions("base:spermAgency:delete")
    public Result<Void> delete(@NotNull(message = "id不能为空") Long id) {
        baseSpermAgencyService.delete(id);
        return Result.success();
    }

    /**
     * 编辑
     *
     * @param editBaseSpermAgencyVO
     */
    @PutMapping(consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "编辑", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 3)
    // @RequiresPermissions("base:spermAgency:edit")
    public Result<Void> edit(@RequestBody @Validated EditBaseSpermAgencyVO editBaseSpermAgencyVO) {
        BaseSpermAgency baseSpermAgency = new BaseSpermAgency();
        BeanUtil.copyProperties(editBaseSpermAgencyVO, baseSpermAgency);
        baseSpermAgencyService.save(baseSpermAgency);
        return Result.success();
    }

    /**
     * 查询
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "查询")
    @ApiImplicitParam(name = "id", value = "id", required = true, paramType = "path", dataType = "String")
    @ApiOperationSupport(order = 4)
    // @RequiresPermissions("base:spermAgency:get")
    public Result<BaseSpermAgency> get(@NotNull(message = "id不能为空") @PathVariable("id") Long id) {
        BaseSpermAgency baseSpermAgency = baseSpermAgencyService.get(id);
        return Result.success(baseSpermAgency);
    }

    /**
     * 列表查询
     *
     * @return
     */
    @GetMapping("/getList")
    @ApiOperation(value = "列表查询")
    @ApiOperationSupport(order = 7)
    // @RequiresPermissions("base:spermAgency:getList")
    public Result<List<BaseSpermAgency>> getList() {
        List<BaseSpermAgency> list = baseSpermAgencyService.getList();
        return Result.success(list);
    }

    /**
     * 可用供精机构查询
     *
     * @return
     */
    @GetMapping("/available")
    @ApiOperation(value = "可用供精机构查询")
    @ApiOperationSupport(order = 8)
    // @RequiresPermissions("base:spermAgency:available")
    public Result<List<BaseSpermAgency>> available() {
        List<BaseSpermAgency> list = baseSpermAgencyService.getList(GlobalConstant.YES);
        return Result.success(list);
    }
}
