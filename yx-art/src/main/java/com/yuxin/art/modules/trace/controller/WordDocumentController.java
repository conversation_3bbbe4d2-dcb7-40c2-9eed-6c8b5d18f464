/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称：yx-art
 * 文件名称:WordDocumentController.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.trace.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.yuxin.api.Result;
import com.yuxin.art.modules.trace.enums.BizModuleEnum;
import com.yuxin.art.modules.trace.service.WordDocumentService;
import com.yuxin.framework.mvc.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * Word文档生成控制器
 * <p>
 * 提供基于模板的Word文档生成功能，支持带批注和不带批注两种模式。
 * 根据业务数据ID和业务模块生成对应的Word文档。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Api(tags = "Word文档生成")
@ApiSort(1)
@RestController
@RequestMapping("/api/word")
@Validated
@Slf4j
public class WordDocumentController extends BaseController {

    @Autowired
    private WordDocumentService wordDocumentService;

    /**
     * 生成带批注的Word文档
     */
    @PostMapping("/generate-with-comments")
    @ApiOperation(value = "生成带批注的Word文档")
    @ApiOperationSupport(order = 1)
    public void generateWordWithComments(
        @NotNull @ApiParam("业务数据ID") @RequestParam Long bizId,
        @NotNull @ApiParam("业务模块") @RequestParam BizModuleEnum bizModule,
        HttpServletResponse response) throws IOException {
        
        log.info("生成带批注的Word文档：bizId={}, bizModule={}", bizId, bizModule);
        
        try {
            // 检查业务模块是否支持
            if (!wordDocumentService.isModuleSupported(bizModule)) {
                throw new RuntimeException("不支持的业务模块：" + bizModule.getDisplayName());
            }
            
            // 生成Word文档
            byte[] wordBytes = wordDocumentService.generateWordWithComments(bizId, bizModule);
            
            // 设置响应头
            String fileName = bizModule.getDisplayName() + "_" + bizId + "_带批注.docx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            response.setContentLength(wordBytes.length);
            
            // 输出文件
            response.getOutputStream().write(wordBytes);
            response.getOutputStream().flush();
            
            log.info("Word文档生成成功：bizId={}, bizModule={}, size={} bytes", bizId, bizModule, wordBytes.length);
            
        } catch (Exception e) {
            log.error("生成带批注的Word文档失败：bizId={}, bizModule={}", bizId, bizModule, e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":500,\"msg\":\"生成Word文档失败：" + e.getMessage() + "\"}");
        }
    }

    /**
     * 生成不带批注的Word文档
     */
    @PostMapping("/generate-without-comments")
    @ApiOperation(value = "生成不带批注的Word文档")
    @ApiOperationSupport(order = 2)
    public void generateWordWithoutComments(
        @NotNull @ApiParam("业务数据ID") @RequestParam Long bizId,
        @NotNull @ApiParam("业务模块") @RequestParam BizModuleEnum bizModule,
        HttpServletResponse response) throws IOException {
        
        log.info("生成不带批注的Word文档：bizId={}, bizModule={}", bizId, bizModule);
        
        try {
            // 检查业务模块是否支持
            if (!wordDocumentService.isModuleSupported(bizModule)) {
                throw new RuntimeException("不支持的业务模块：" + bizModule.getDisplayName());
            }
            
            // 生成Word文档
            byte[] wordBytes = wordDocumentService.generateWordWithoutComments(bizId, bizModule);
            
            // 设置响应头
            String fileName = bizModule.getDisplayName() + "_" + bizId + ".docx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            response.setContentLength(wordBytes.length);
            
            // 输出文件
            response.getOutputStream().write(wordBytes);
            response.getOutputStream().flush();
            
            log.info("Word文档生成成功：bizId={}, bizModule={}, size={} bytes", bizId, bizModule, wordBytes.length);
            
        } catch (Exception e) {
            log.error("生成不带批注的Word文档失败：bizId={}, bizModule={}", bizId, bizModule, e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":500,\"msg\":\"生成Word文档失败：" + e.getMessage() + "\"}");
        }
    }

    /**
     * 生成Word文档预览URL
     */
    @GetMapping("/preview-url")
    @ApiOperation(value = "生成Word文档预览URL")
    @ApiOperationSupport(order = 3)
    public Result<String> generatePreviewUrl(
        @NotNull @ApiParam("业务数据ID") @RequestParam Long bizId,
        @NotNull @ApiParam("业务模块") @RequestParam BizModuleEnum bizModule,
        @ApiParam("是否包含批注") @RequestParam(defaultValue = "false") Boolean withComments) {
        
        log.info("生成Word文档预览URL：bizId={}, bizModule={}, withComments={}", bizId, bizModule, withComments);
        
        try {
            // 检查业务模块是否支持
            if (!wordDocumentService.isModuleSupported(bizModule)) {
                return Result.result(500, "不支持的业务模块：" + bizModule.getDisplayName());
            }

            String previewUrl = wordDocumentService.generatePreviewUrl(bizId, bizModule, withComments);
            return Result.success(previewUrl);

        } catch (Exception e) {
            log.error("生成预览URL失败：bizId={}, bizModule={}", bizId, bizModule, e);
            return Result.result(500, "生成预览URL失败：" + e.getMessage());
        }
    }

    /**
     * 检查业务模块是否支持Word生成
     */
    @GetMapping("/check-support")
    @ApiOperation(value = "检查业务模块是否支持")
    @ApiOperationSupport(order = 4)
    public Result<Boolean> checkModuleSupport(
        @NotNull @ApiParam("业务模块") @RequestParam BizModuleEnum bizModule) {
        
        try {
            boolean isSupported = wordDocumentService.isModuleSupported(bizModule);
            return Result.success(isSupported);

        } catch (Exception e) {
            log.error("检查模块支持失败：bizModule={}", bizModule, e);
            return Result.result(500, "检查模块支持失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板文件路径
     */
    @GetMapping("/template-path")
    @ApiOperation(value = "获取模板文件路径")
    @ApiOperationSupport(order = 5)
    public Result<String> getTemplatePath(
        @NotNull @ApiParam("业务模块") @RequestParam BizModuleEnum bizModule) {
        
        try {
            String templatePath = wordDocumentService.getTemplatePath(bizModule);
            return Result.success(templatePath);

        } catch (Exception e) {
            log.error("获取模板路径失败：bizModule={}", bizModule, e);
            return Result.result(500, "获取模板路径失败：" + e.getMessage());
        }
    }
}
