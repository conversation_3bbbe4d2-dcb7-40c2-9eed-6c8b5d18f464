/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ArtAppointmentService.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.art.cycle.service;

import com.yuxin.art.domain.art.cycle.ArtAppointment;
import com.yuxin.art.modules.art.cycle.vo.ReserveArtAppointmentVo;
import com.yuxin.framework.mvc.service.BaseService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * ART预约 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
public interface ArtAppointmentService extends BaseService<ArtAppointment> {

    /**
     * 审核
     *
     * @param id
     */
    void audit(Long id);

    /**
     * 反审
     *
     * @param id
     */
    void revokeAudit(Long id);

    /**
     * 预约建档时间
     *
     * @param reserveArtAppointmentVo
     */
    void reserve(ReserveArtAppointmentVo reserveArtAppointmentVo);

    /**
     * 保存+ 审核
     *
     * @param artAppointment
     * @return
     */
    Long insertAudit(ArtAppointment artAppointment);

    /**
     * 反审+ 修改+ 审核
     *
     * @param artAppointment
     */
    void revokeAuditUpdate(ArtAppointment artAppointment);

    /**
     * 反审+ 删除
     *
     * @param id
     */
    void revokeAuditDelete(Long id);

    /**
     * 编辑预约信息
     *
     * @param artAppointment
     */
    void editArtAppointment(ArtAppointment artAppointment);

    /**
     * 根据患者唯一标识查询最新的预约信息
     *
     * @param patientUniqueIdentify
     * @return
     */
    ArtAppointment getByPatientUniqueIdentify(String patientUniqueIdentify);

    ArtAppointment getInfoByUniqueId(String uniqueId);

    /**
     * 根据患者id集合获取预约进周信息
     *
     * @param ids
     * @return
     */
    List<ArtAppointment> getInfoByPaitentIds(String ids);

    Map<String, Object> getDetail(Long id);
}
