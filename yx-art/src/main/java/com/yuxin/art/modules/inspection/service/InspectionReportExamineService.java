/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:InspectionReportExamineService.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.inspection.service;

import com.yuxin.art.domain.inspection.InspectionReportExamine;
import com.yuxin.framework.mvc.service.BaseService;

import java.util.List;

/**
 * 检验报告详情
 *
 * <AUTHOR>
 * @date 2020-08-12
 */
public interface InspectionReportExamineService extends BaseService<InspectionReportExamine> {
    /**
     * 根据报告ID查询
     *
     * @param reportId
     * @return
     */
    List<InspectionReportExamine> getByReportId(Long reportId);

    /**
     * 获取患者最新的内分泌检验指标
     *
     * @param patientId
     * @param reportDate
     * @return
     */
    List<InspectionReportExamine> getLastSecretion(Long patientId, String reportDate);

    /**
     * 根据报告列表查询异常项
     *
     * @param ids
     * @return
     */
    List<InspectionReportExamine> getExamineAbnormalByIds(List<Long> ids);

    /**
     * @param hisSerialNo
     * @return
     */
    InspectionReportExamine getByHisSerialNo(String hisSerialNo);

    /***
     * @param  body
     *存储检验报告
     * */
    void saveExamine(String body);
}
