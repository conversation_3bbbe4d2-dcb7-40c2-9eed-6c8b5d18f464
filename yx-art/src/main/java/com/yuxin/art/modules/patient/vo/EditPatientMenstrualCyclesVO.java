/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:EditPatientMenstrualCyclesVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.patient.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 月经周期表
 * 女性病人所特有
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-16
 */
@Data
@NoArgsConstructor
@ApiModel(value = "EditPatientMenstrualCyclesVO", description = "编辑月经周期表 女性病人所特有")
public class EditPatientMenstrualCyclesVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @ApiModelProperty(value = "ID", required = true)
    @NotNull(message = "id不能为空")
    public Long id;
    /**
     * 患者ID
     */
    @ApiModelProperty(value = "患者ID", required = true)
    @NotNull(message = "患者ID不能为空")
    private Long patientId;
    /**
     * 月经周期
     */
    @ApiModelProperty(value = "月经周期", required = true)
    @NotNull(message = "月经周期不能为空")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date menstrualCycle;

    /**
     * 经期天数
     */
    @ApiModelProperty(value = "经期天数", required = true)
    @NotNull(message = "经期天数不能为空")
    private Integer menstrualDay;

}
