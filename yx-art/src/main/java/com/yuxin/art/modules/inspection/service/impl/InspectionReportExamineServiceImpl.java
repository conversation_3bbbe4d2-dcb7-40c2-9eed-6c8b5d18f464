/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:InspectionReportExamineServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.inspection.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuxin.api.ResultEnum;
import com.yuxin.art.cache.base.ExamineImageCache;
import com.yuxin.art.domain.inspection.InspectionReport;
import com.yuxin.art.domain.inspection.InspectionReportExamine;
import com.yuxin.art.domain.inspection.InspectionReportImage;
import com.yuxin.art.domain.patient.PatientInfo;
import com.yuxin.art.modules.inspection.mapper.InspectionReportExamineMapper;
import com.yuxin.art.modules.inspection.service.InspectionReportExamineService;
import com.yuxin.art.modules.inspection.service.InspectionReportImageService;
import com.yuxin.art.modules.inspection.service.InspectionReportService;
import com.yuxin.art.modules.inspection.vo.ExameTragetsVO;
import com.yuxin.art.modules.inspection.vo.SaveInspectExamineVO;
import com.yuxin.art.modules.patient.service.PatientInfoService;
import com.yuxin.art.modules.utils.KnxDateUtils;
import com.yuxin.constant.GlobalConstant;
import com.yuxin.framework.exception.BusinessException;
import com.yuxin.framework.mvc.mapper.BaseMapper;
import com.yuxin.framework.mvc.service.impl.BaseServiceImpl;
import com.yuxin.json.util.JsonUtil;
import com.yuxin.oss.bean.OSSFile;
import com.yuxin.oss.storage.StorageFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 检验报告详情
 *
 * <AUTHOR>
 * @date 2020-08-12
 */
@Service
@Slf4j
public class InspectionReportExamineServiceImpl extends BaseServiceImpl<InspectionReportExamine> implements InspectionReportExamineService {
    @Autowired
    private InspectionReportExamineMapper inspectionReportExamineMapper;
    @Autowired
    private PatientInfoService patientInfoService;
    @Autowired
    private InspectionReportService inspectionReportService;
    @Autowired
    private InspectionReportImageService inspectionReportImageService;
    @Autowired
    private ExamineImageCache examineImageCache;

    @Override
    protected BaseMapper<InspectionReportExamine> getMapper() {
        return inspectionReportExamineMapper;
    }

    @Override
    public List<InspectionReportExamine> getByReportId(Long reportId) {
        return inspectionReportExamineMapper.selectList(
            Wrappers.lambdaQuery(InspectionReportExamine.class).eq(InspectionReportExamine::getReportId, reportId));
    }

    @Override
    public List<InspectionReportExamine> getLastSecretion(Long patientId, String reportDate) {
        return inspectionReportExamineMapper.selectLastSecretion(patientId, reportDate);
    }

    @Override
    public List<InspectionReportExamine> getExamineAbnormalByIds(List<Long> ids) {
        if (CollectionUtil.isNotEmpty(ids)) {
            return inspectionReportExamineMapper.selectExamineAbnormalByIds(ids);
        } else {
            return null;
        }
    }

    @Override
    public InspectionReportExamine getByHisSerialNo(String hisSerialNo) {
        return inspectionReportExamineMapper.selectOne(
            Wrappers.lambdaQuery(InspectionReportExamine.class).eq(InspectionReportExamine::getHisSerialNo, hisSerialNo));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveExamine(String body) {
        log.info("检验报告数据:{}", body);
        SaveInspectExamineVO inspectExamine = JsonUtil.toObject(body, SaveInspectExamineVO.class);
        PatientInfo patientInfo = null;
        // 身份证和卡号二选一，两者不能同时为空
        if (StrUtil.isBlank(inspectExamine.getIdCard()) && StrUtil.isBlank(inspectExamine.getCardNo())) {
            throw new BusinessException(2, "找不到患者");
        }
        patientInfo = patientInfoService.findOneByIdCardOrCardNo(inspectExamine.getIdCard(), inspectExamine.getCardNo());
        if (patientInfo == null) {
            throw new BusinessException(2, "找不到患者");
        } else {
            InspectionReport inspectionReport = null;
            Long id;
            boolean haveImage = StrUtil.isNotBlank(inspectExamine.getImageFilePath());
            if (StrUtil.isNotBlank(inspectExamine.getIdentity())) {
                inspectionReport = inspectionReportService.getByOcrIdentity(inspectExamine.getIdentity());
            }
            if (inspectionReport == null) {
                inspectionReport = new InspectionReport();
                inspectionReport.setPatientId(patientInfo.getId());
                inspectionReport.setPatientCardNo(patientInfo.getCardNo());
                inspectionReport.setPatientIdentifyId(patientInfo.getIdentifyId());
                inspectionReport.setPatientName(patientInfo.getName());
                inspectionReport.setPatientGender(patientInfo.getGender());
                inspectionReport.setPatientAge(
                    KnxDateUtils.calcAgeNow(patientInfo.getIdCardType(), patientInfo.getIdCard(), patientInfo.getBirthday()));
                inspectionReport.setReportType(0);
                inspectionReport.setHisReportId("0");
            }

            inspectionReport.setItemName(inspectExamine.getItem());
            inspectionReport.setReportHospital(inspectExamine.getReportHospital());
            inspectionReport.setReportDoctorName(inspectExamine.getReportDoctor());
            inspectionReport.setReportDate(inspectExamine.getReportTime());
            inspectionReport.setOcrIdentity(inspectExamine.getIdentity());
            inspectionReport.setHaveImage(haveImage ? GlobalConstant.YES : GlobalConstant.NO);
            inspectionReport.setRecorderTime(DateUtil.date());
            if (ObjectUtil.isNotNull(inspectionReport.getId())) {
                id = inspectionReport.getId();
                inspectionReportService.update(inspectionReport);
            } else {
                id = inspectionReportService.insert(inspectionReport);
            }
            // 检验指标没有标识可以区分，所以每次只能先删除
            this.delete(Wrappers.lambdaQuery(InspectionReportExamine.class).eq(InspectionReportExamine::getReportId, id));
            if (ObjectUtil.isNotNull(inspectExamine.getTragets())) {
                for (ExameTragetsVO exameTragetsVO : inspectExamine.getTragets()) {
                    InspectionReportExamine inspectionReportExamine = new InspectionReportExamine();
                    inspectionReportExamine.setReportId(id);
                    // inspectionReportExamine.setId(KeyGenerate.generateId());
                    inspectionReportExamine.setPatientId(inspectionReport.getPatientId());
                    inspectionReportExamine.setPatientGender(inspectionReport.getPatientGender());
                    inspectionReportExamine.setExamineItemName(inspectExamine.getItem());
                    inspectionReportExamine.setName(exameTragetsVO.getTarget());
                    inspectionReportExamine.setUnit(exameTragetsVO.getUnit());
                    inspectionReportExamine.setNormalReferenceValue(exameTragetsVO.getReferenceRange());
                    Pattern pattern = Pattern.compile("[0-9]+(\\.[0-9]+)?");
                    if (StrUtil.isNotBlank(exameTragetsVO.getMinValue()) && pattern.matcher(exameTragetsVO.getMinValue()).matches()) {
                        inspectionReportExamine.setMinReferenceValue(new BigDecimal(exameTragetsVO.getMinValue()));
                    }
                    if (StrUtil.isNotBlank(exameTragetsVO.getMaxValue()) && pattern.matcher(exameTragetsVO.getMaxValue()).matches()) {
                        inspectionReportExamine.setMaxReferenceValue(new BigDecimal(exameTragetsVO.getMaxValue()));
                    }
                    inspectionReportExamine.setResult(exameTragetsVO.getResult());
                    inspectionReportExamine.setSampleTypeCode(inspectExamine.getSampleClassify());
                    this.insert(inspectionReportExamine);
                }
            }
            if (haveImage) {
                String imagePath = DateUtil.format(new Date(), "yyyyMMdd") + "/" + System.currentTimeMillis() + ".jpg";
                try {
                    byte[] imageByte = null;
                    OSSFile ossFile = null;
                    InspectionReportImage inspectionReportImage = new InspectionReportImage();
                    if (StrUtil.isBlank(examineImageCache.getCacheByReportTypeId(id.toString())) || !examineImageCache.getCacheByReportTypeId(
                        id.toString()).equals(inspectExamine.getImageFilePath())) {
                        imageByte = HttpUtil.downloadBytes(inspectExamine.getImageFilePath());
                        ossFile = StorageFactory.build().upload(imagePath, imageByte);
                        if (ossFile == null) {
                            throw new BusinessException(ResultEnum.FILE_UPLOAD_ERROR);
                        }
                        if (StrUtil.isBlank(examineImageCache.getCacheByReportTypeId(id.toString()))) {
                            examineImageCache.addCache(id.toString(), inspectExamine.getImageFilePath());
                        } else {
                            examineImageCache.updateCache(id.toString(), inspectExamine.getImageFilePath());
                        }
                        if (CollUtil.isNotEmpty(inspectionReportImageService.getByReportId(id))) {
                            inspectionReportImage = inspectionReportImageService.getByReportId(id).get(0);
                            inspectionReportImage.setImage(ossFile.getPath());
                            inspectionReportImageService.update(inspectionReportImage);
                        } else {
                            inspectionReportImage.setImage(ossFile.getPath());
                            inspectionReportImage.setReportId(id);
                            inspectionReportImageService.insert(inspectionReportImage);
                        }
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    throw new BusinessException(3, "其他失败");
                }

            } else {
                inspectionReportImageService.delete(Wrappers.lambdaQuery(InspectionReportImage.class).eq(InspectionReportImage::getReportId, id));
            }

        }

    }

}
