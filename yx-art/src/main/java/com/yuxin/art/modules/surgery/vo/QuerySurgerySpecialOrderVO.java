/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:QuerySurgerySpecialOrderVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.surgery.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.io.Serializable;

/**
 * 查询特殊手术预约单条件
 *
 * <AUTHOR>
 * @date 2020-07-10
 */
@Data
@NoArgsConstructor
@ApiModel(value = "QuerySurgerySpecialOrderVO", description = "查询特殊手术预约单条件")
public class QuerySurgerySpecialOrderVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 患者ID
     */
    @ApiModelProperty(value = "患者ID(20)")
    private String patientIdentifyId;

    /**
     * 患者姓名
     */
    @ApiModelProperty(value = "患者姓名（50）")
    @Length(max = 50, message = "患者姓名不能超过50个字符")
    private String patientName;

    /**
     * 手术名称
     */
    @ApiModelProperty(
        value = "手术名称：0-取精手术 1-卵巢囊肿抽吸 2-卵泡穿刺手术 3-输卵管抽吸 4-诊断性刮宫 5-宫腔注射手术 6-减胎手术 7-宫腔积液抽吸 8-内膜活检")
    @Min(value = 0, message = "无法识别的手术名称")
    @Max(value = 8, message = "无法识别的手术名称")
    private Integer name;

    /**
     * 计划手术时间
     */
    @ApiModelProperty(value = "计划手术时间")
    private String planTime;

    /**
     * 开单人Id
     */
    @ApiModelProperty(value = "开单人Id")
    private Long billingId;

    /**
     * 开单时间
     */
    @ApiModelProperty(value = "开单时间")
    private String billingTime;

    /**
     * 0：已开单 ， 1：已手术
     */
    @ApiModelProperty(value = "0：已开单 ， 1：已手术")
    @Min(value = 0, message = "无法识别的状态")
    @Max(value = 1, message = "无法识别的状态")
    private Integer status;

    /**
     * 周期ID
     */
    @ApiModelProperty(value = "周期ID")
    private Long cycleId;

    /**
     * 是否查询历史记录
     */
    @ApiModelProperty(value = "是否查询历史记录：0-否 1-是 ")
    @Min(value = 0, message = "无法识别的是否查询历史记录")
    @Max(value = 1, message = "无法识别的是否查询历史记录")
    private Integer isHistory;
}
