/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:AddArtPlanVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.art.plan.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * ART计划表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-25
 */
@Data
@NoArgsConstructor
@ApiModel(value = "AddArtPlanVO", description = "新增ART计划表")
public class AddArtPlanVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 周期主键ID
     */
    @ApiModelProperty(value = "周期主键ID", required = true)
    @NotNull(message = "周期主键ID不能为空")
    private Long cycleId;

    /**
     * 计划类型 0:常规ART计划 1:人工授精计划
     */
    @ApiModelProperty(value = "计划类型 0:常规ART计划 1:人工授精计划", required = true)
    @NotNull(message = "计划类型不能为空")
    @Range(min = 0, max = 1, message = "不能识别的计划类型")
    private Integer planType;

    /**
     * 忽略HCG标志 0:不忽略 1:忽略
     */
    @ApiModelProperty(value = "忽略HCG标志 0:不忽略 1:忽略", required = true)
    @NotNull(message = "忽略HCG标志不能为空")
    @Range(min = 0, max = 1, message = "不能识别的忽略HCG标志")
    private Integer ignoreHcg;

    /**
     * 取精计划
     */
    @ApiModelProperty(value = "取精计划", required = true)
    private AddArtPlanSpermVO artPlanSperm;
    /**
     * 取卵计划
     */
    @ApiModelProperty(value = "取卵计划", required = true)
    private AddArtPlanOvumVO artPlanOvum;
    /**
     * 移植计划
     */
    @ApiModelProperty(value = "移植计划", required = true)
    private List<AddArtPlanEtVO> artPlanEtList;
    /**
     * 胚胎解冻计划
     */
    @ApiModelProperty(value = "胚胎解冻计划", required = true)
    private List<AddArtPlanThawEmbryoVO> artPlanThawEmbryoList;
    /**
     * 人工授精计划
     */
    @ApiModelProperty(value = "人工授精计划", required = true)
    private List<AddArtPlanAiVO> artPlanAiList;
}
