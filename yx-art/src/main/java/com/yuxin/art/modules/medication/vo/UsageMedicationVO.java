/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:UsageMedicationVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.medication.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用药记录
 *
 * <AUTHOR>
 * @date 2020/12/15
 */
@Data
@NoArgsConstructor
@ApiModel(value = "UsageMedicationVO", description = "开药信息")
public class UsageMedicationVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 药品ID
     */
    @ApiModelProperty(value = "药品ID")
    private Long drugId;
    /**
     * 药品名称
     */
    @ApiModelProperty(value = "药品名称")
    private String drugName;

    /**
     * 药品类型
     */
    @ApiModelProperty(
        value = "药品类型(0.长效GnRH-a、1.短效GnRH-a、2.GnRH-anta、3.u-FSH、4.r-FSH 、5.HMG、6.口服促排、7.黄体酮制剂、8.生长激素、9.HCG、10.雌激素、11.OC、12.抗菌药物、13.其他西药、14.中成药、15.rHcg）")
    private Integer drugType;

    /**
     * 单次用量
     */
    @ApiModelProperty(value = "单次用量")
    private BigDecimal singleDose;
    /**
     * 剂量单位
     */
    @ApiModelProperty(value = "剂量单位")
    private String unit;

    /**
     * 用药频率.据来源于数据字典
     */
    @ApiModelProperty(value = "用药频率.数据来源于数据字典")
    private String drugUseFrequency;

    /**
     * 用药频率编码
     */
    @ApiModelProperty(value = "用药频率编码")
    private String drugUseFrequencyCode;

    /**
     * 用药方法.数据来源于数据字典
     */
    @ApiModelProperty(value = "用药方法.数据来源于数据字典")
    private String medicationMethod;

    /**
     * 用药方法编码
     */
    @ApiModelProperty(value = "用药方法编码")
    private String medicationMethodCode;

    /**
     * 用药时间
     */
    @ApiModelProperty(value = "用药时间")
    private Date useDrugTime;

    /**
     * 用药天数
     */
    @ApiModelProperty(value = "用药天数")
    private Integer medicationDays;

}
