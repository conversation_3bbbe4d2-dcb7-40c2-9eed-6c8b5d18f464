/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ConsumableBaseRspVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */

package com.yuxin.art.modules.consumable.vo.rsp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 耗材基础信息响应VO
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Data
@ApiModel(value = "ConsumableBaseRspVO", description = "耗材基础信息响应VO")
public class ConsumableBaseRspVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 耗材ID
     */
    @ApiModelProperty(value = "耗材ID")
    private Long consumableId;
    /**
     * 耗材名称
     */
    @ApiModelProperty(value = "耗材名称")
    private String consumableName;
    /**
     * 耗材类型ID
     */
    @ApiModelProperty(value = "耗材类型ID")
    private Long consumableTypeId;
    /**
     * 耗材类型名称
     */
    @ApiModelProperty(value = "耗材类型名称")
    private String consumableTypeName;
    /**
     * 厂商ID
     */
    @ApiModelProperty(value = "厂商ID")
    private Long manufacturerId;
    /**
     * 厂商名称
     */
    @ApiModelProperty(value = "厂商名称")
    private String manufacturerName;
    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    private Long supplierId;
    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    /**
     * 计量单位
     */
    @ApiModelProperty(value = "计量单位")
    private String unit;
    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;
    /**
     * 自定义单价
     */
    @ApiModelProperty(value = "自定义单价")
    private String unitPriceCustom;
    /**
     * 规格-数量
     */
    @ApiModelProperty(value = "规格-数量")
    private BigDecimal specNum;
    /**
     * 规格-单位
     */
    @ApiModelProperty(value = "规格-单位")
    private String specUnit;
    /**
     * 规格-包装单位
     */
    @ApiModelProperty(value = "规格-包装单位")
    private String specPackUnit;
    /**
     * 规格-自定义
     */
    @ApiModelProperty(value = "规格-自定义")
    private String specCustom;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchNumber;
    /**
     * 有效期
     */
    @ApiModelProperty(value = "有效期")
    private Date expiryDate;
    /**
     * 到货数量
     */
    @ApiModelProperty(value = "到货数量")
    private Integer arrivalNum;
    /**
     * 到货计量单位
     */
    @ApiModelProperty(value = "到货计量单位")
    private String arrivalUnit;
    /**
     * 到货日期
     */
    @ApiModelProperty(value = "到货日期")
    private Date arrivalDate;
    /**
     * 检测项目.0:精子存活;1:鼠胚试验
     */
    @ApiModelProperty(value = "检测项目.0:精子存活;1:鼠胚试验")
    private Integer testItem;
    /**
     * 检测状态.0:待检测;1:检测中;2:检测合格;3:检测不合格
     */
    @ApiModelProperty(value = "检测状态.0:待检测;1:检测中;2:检测合格;3:检测不合格")
    private Integer testStatus;
    /**
     * 接收人ID
     */
    @ApiModelProperty(value = "接收人ID")
    private Long receiverId;
    /**
     * 接收人姓名
     */
    @ApiModelProperty(value = "接收人姓名")
    private String receiverName;
    /**
     * 接收时间
     */
    @ApiModelProperty(value = "接收时间")
    private Date receivedDate;
}
