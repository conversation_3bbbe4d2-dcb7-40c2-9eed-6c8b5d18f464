/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ArtDiagnosService.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.art.medicalhistory.service;

import com.yuxin.art.domain.art.medicalhistory.ArtDiagnos;
import com.yuxin.art.external.vo.ArtDiagnosVO;
import com.yuxin.art.modules.art.medicalhistory.vo.PastDiagnosVO;
import com.yuxin.framework.mvc.service.BaseService;

import java.util.List;

/**
 * <p>
 * 诊断 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-13
 */
public interface ArtDiagnosService extends BaseService<ArtDiagnos> {
    /**
     * 保存诊断
     *
     * @param diagnos
     * @return
     */
    Long saveDiagnos(ArtDiagnos diagnos);

    /**
     * 删除诊断
     *
     * @param id
     */
    void deleteDiagnos(Long id);

    /**
     * 根据周期获取诊断
     *
     * @param cycleId
     * @return
     */
    List<ArtDiagnos> getByCycleId(Long cycleId, Integer isShow);

    /**
     * 根据周期和患者获取诊断
     *
     * @param cycleId
     * @return
     */
    List<ArtDiagnos> getListByCycleIdAndPatientId(Long cycleId, Long patientId, Integer isShow);

    /**
     * 根据周期和患者获取既往诊断
     *
     * @param cycleId
     * @param patientId
     * @return
     */
    List<PastDiagnosVO> getPastDiagnosByCycleIdAndPatientId(Long cycleId, Long patientId, Integer isShow);

    /**
     * 根据周期和类型获取诊断信息
     *
     * @param cycleId
     * @return
     */
    List<ArtDiagnosVO> getDiagnosByCycleId(Long cycleId);
}
