/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:TodoAnaesthesiaServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.upcoming.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuxin.api.ResultEnum;
import com.yuxin.art.cache.todo.TodoCacheConstants;
import com.yuxin.art.constant.KArtConstants;
import com.yuxin.art.constant.KArtConstants.TodoStatus;
import com.yuxin.art.domain.art.cycle.ArtCycle;
import com.yuxin.art.domain.patient.PatientInfo;
import com.yuxin.art.domain.upcoming.TodoAnaesthesia;
import com.yuxin.art.external.vo.TodoPageVo;
import com.yuxin.art.modules.art.cycle.service.ArtCycleService;
import com.yuxin.art.modules.patient.service.PatientInfoService;
import com.yuxin.art.modules.upcoming.mapper.TodoAnaesthesiaMapper;
import com.yuxin.art.modules.upcoming.service.TodoAnaesthesiaService;
import com.yuxin.art.modules.upcoming.vo.*;
import com.yuxin.cache.util.SerialNoGenerator;
import com.yuxin.framework.exception.BusinessException;
import com.yuxin.framework.mvc.mapper.BaseMapper;
import com.yuxin.framework.mvc.service.impl.BaseServiceImpl;
import com.yuxin.framework.mvc.vo.PageParams;
import com.yuxin.json.util.JsonUtil;
import com.yuxin.security.util.AuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 麻醉待办列表 ServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-03
 */
@Service
@Slf4j
public class TodoAnaesthesiaServiceImpl extends BaseServiceImpl<TodoAnaesthesia> implements TodoAnaesthesiaService {
    @Autowired
    private TodoAnaesthesiaMapper mapper;
    @Autowired
    private ArtCycleService artCycleService;
    @Autowired
    private SerialNoGenerator serialNoGenerator;
    @Autowired
    private PatientInfoService patientInfoService;

    @Override
    protected BaseMapper<TodoAnaesthesia> getMapper() {
        return mapper;
    }

    @Override
    public Long create(Long cycleId, Integer item, Date datetime, Integer status, String remark, Integer sourceModule, Long sourceModuleId,
        Long subSourceModuleId) {
        return this.create(artCycleService.get(cycleId), item, datetime, status, remark, sourceModule, sourceModuleId, subSourceModuleId);
    }

    @Override
    public Long create(ArtCycle cycle, Integer item, Date datetime, Integer status, String remark, Integer sourceModule, Long sourceModuleId,
        Long subSourceModuleId) {
        TodoAnaesthesia todo = new TodoAnaesthesia();
        if (cycle == null) {
            throw new BusinessException(ResultEnum.FAILED.getCode(), "无效的周期数据");
        }
        if (datetime == null) {
            throw new BusinessException(ResultEnum.FAILED.getCode(), "执行时间不能为空");
        }
        if (item == null) {
            throw new BusinessException(ResultEnum.FAILED.getCode(), "项目类型不能为空");
        }
        BeanUtil.copyProperties(cycle, todo, "recorderId", "recorderName", "recorderTime");
        // Long id = KeyGenerate.generateId();
        // todo.setId(id);
        todo.setId(null);
        todo.setItem(item);
        todo.setCycleId(cycle.getId());
        todo.setExecuteTime(datetime);
        todo.setSourceModule(sourceModule);
        todo.setSourceModuleId(sourceModuleId);
        todo.setSubSourceModuleId(subSourceModuleId);
        todo.setRemark(remark);
        todo.setStatus(status);
        todo.setOrderNo(serialNoGenerator.generatorSerialNo(TodoCacheConstants.TODO_ANAESTHESIA_ORDER_CACHE_KEY, 3,
            TodoCacheConstants.TODO_ANAESTHESIA_ORDER_DATE_CACHE_KEY, "todoAnaesthesia"));
        return super.insert(todo);
    }

    // @Override
    // public Long createCycleUnique(Long cycleId, Integer item, Date datetime, Integer status, String remark, Integer
    // sourceModule, Long sourceModuleId,
    // Long subSourceModuleId) {
    // return this.createCycleUnique(artCycleService.get(cycleId), item, datetime, status, remark, sourceModule,
    // sourceModuleId, subSourceModuleId);
    // }

    @Override
    public Long createCycleUnique(ArtCycle cycle, Integer item, Date datetime, Integer status, String remark, Integer sourceModule,
        Long sourceModuleId, Long subSourceModuleId) {
        TodoAnaesthesia todo = this.getOne(cycle.getId(), item, datetime, status, sourceModule, sourceModuleId, subSourceModuleId, true);
        if (todo != null) {
            if (ObjectUtil.notEqual(remark, todo.getRemark())) {
                todo.setRemark(remark);
                super.update(todo);
            }
            return todo.getId();
        }
        return this.create(cycle, item, datetime, status, remark, sourceModule, sourceModuleId, subSourceModuleId);
    }

    @Override
    public Long createUniqueDate(Long cycleId, Integer item, Date datetime, Integer status, String remark, Integer sourceModule, Long sourceModuleId,
        Long subSourceModuleId) {
        return this.createUniqueDate(artCycleService.get(cycleId), item, datetime, status, remark, sourceModule, sourceModuleId, subSourceModuleId);
    }

    @Override
    public Long createUniqueDate(ArtCycle cycle, Integer item, Date datetime, Integer status, String remark, Integer sourceModule,
        Long sourceModuleId, Long subSourceModuleId) {
        TodoAnaesthesia todo = this.getOneByDate(cycle.getId(), item, datetime, status, sourceModule, sourceModuleId, subSourceModuleId, true);
        if (todo != null) {
            if (ObjectUtil.notEqual(remark, todo.getRemark())) {
                todo.setRemark(remark);
                super.update(todo);
            }
            return todo.getId();
        }
        return this.create(cycle, item, datetime, status, remark, sourceModule, sourceModuleId, subSourceModuleId);
    }

    @Override
    public Long createUniqueTime(Long cycleId, Integer item, Date datetime, Integer status, String remark, Integer sourceModule, Long sourceModuleId,
        Long subSourceModuleId) {
        return this.createUniqueTime(artCycleService.get(cycleId), item, datetime, status, remark, sourceModule, sourceModuleId, subSourceModuleId);
    }

    @Override
    public Long createUniqueTime(ArtCycle cycle, Integer item, Date datetime, Integer status, String remark, Integer sourceModule,
        Long sourceModuleId, Long subSourceModuleId) {
        TodoAnaesthesia todo = this.getOne(cycle.getId(), item, datetime, status, sourceModule, sourceModuleId, subSourceModuleId, true);
        if (todo != null) {
            if (ObjectUtil.notEqual(remark, todo.getRemark())) {
                todo.setRemark(remark);
                super.update(todo);
            }
            return todo.getId();
        }
        return this.create(cycle, item, datetime, status, remark, sourceModule, sourceModuleId, subSourceModuleId);
    }

    // @Override
    // public Long createSurgeryAnaTodo(int item, long patientId, Date datetime, String remark, Integer sourceModule, Long
    // sourceModuleId) {
    // if (datetime == null) {
    // throw new BusinessException(ResultEnum.FAILED.getCode(), "执行时间不能为空!");
    // }
    //
    // PatientInfo patient = patientInfoService.get(patientId);
    // if (patient == null) {
    // throw new BusinessException(ResultEnum.FAILED.getCode(), "没有找到该病人的基本信息!");
    // }
    //
    // TodoAnaesthesia todo = new TodoAnaesthesia();
    // todo.setExecuteTime(datetime);
    // todo.setSourceModule(sourceModule);
    // todo.setSourceModuleId(sourceModuleId);
    // todo.setItem(item);
    //
    // return super.insert(todo);
    // }

    @Override
    public TodoAnaesthesia getOne(Long cycleId, Integer item, Date datetime, Integer status, Integer sourceModule, Long sourceModuleId,
        Long subSourceModuleId, boolean asc) {
        LambdaQueryWrapper<TodoAnaesthesia> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(item != null, TodoAnaesthesia::getItem, item).eq(datetime != null, TodoAnaesthesia::getExecuteTime, datetime)
            .eq(status != null, TodoAnaesthesia::getStatus, status).eq(TodoAnaesthesia::getCycleId, cycleId)
            .eq(sourceModule != null, TodoAnaesthesia::getSourceModule, sourceModule)
            .eq(ObjectUtil.isNotNull(sourceModuleId), TodoAnaesthesia::getSourceModuleId, sourceModuleId)
            .eq(ObjectUtil.isNotNull(subSourceModuleId), TodoAnaesthesia::getSubSourceModuleId, subSourceModuleId);

        if (asc) {
            wrapper.orderByAsc(TodoAnaesthesia::getExecuteTime);
        } else {
            wrapper.orderByDesc(TodoAnaesthesia::getExecuteTime);
        }

        List<TodoAnaesthesia> list = this.getList(wrapper);
        return CollUtil.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public TodoAnaesthesia getOneByDate(Long cycleId, Integer item, Date datetime, Integer status, Integer sourceModule, Long sourceModuleId,
        Long subSourceModuleId, boolean asc) {
        LambdaQueryWrapper<TodoAnaesthesia> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TodoAnaesthesia::getItem, item).ge(TodoAnaesthesia::getExecuteTime, DateUtil.beginOfDay(datetime))
            .le(TodoAnaesthesia::getExecuteTime, DateUtil.endOfDay(datetime)).eq(TodoAnaesthesia::getStatus, status)
            .eq(TodoAnaesthesia::getCycleId, cycleId).eq(sourceModule != null, TodoAnaesthesia::getSourceModule, sourceModule)
            .eq(ObjectUtil.isNotNull(sourceModuleId), TodoAnaesthesia::getSourceModuleId, sourceModuleId)
            .eq(ObjectUtil.isNotNull(subSourceModuleId), TodoAnaesthesia::getSubSourceModuleId, subSourceModuleId);
        if (asc) {
            wrapper.orderByAsc(TodoAnaesthesia::getExecuteTime);
        } else {
            wrapper.orderByDesc(TodoAnaesthesia::getExecuteTime);
        }

        List<TodoAnaesthesia> list = this.getList(wrapper);
        return CollUtil.isEmpty(list) ? null : list.get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(Long id) {
        TodoAnaesthesia todo = this.get(id);
        if (todo == null) {
            log.error("取消麻醉待办失败，未能找到相应的待办数据, id: {}", id);
        } else {
            if (TodoStatus.DEFAULT.equals(todo.getStatus())) {
                todo.setStatus(TodoStatus.CANCEL);
                this.update(todo);
            } else if (TodoStatus.CANCEL.equals(todo.getStatus())) {

            } else {
                throw new BusinessException(ResultEnum.FAILED.getCode(), "已完成的麻醉待办不能被取消或修改");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void planDelete(Long cycleId, Integer item, Date datetime, Long planId, Long subPlanId, boolean force) {
        List<TodoAnaesthesia> list = this.getPlanTodo(cycleId, item, datetime, planId, subPlanId);
        list = list.stream().filter(t -> !TodoStatus.CANCEL.equals(t.getStatus())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(list)) {
            for (TodoAnaesthesia todo : list) {
                if (TodoStatus.FINISH.equals(todo.getStatus()) || TodoStatus.CONFIRM.equals(todo.getStatus())) {
                    if (force) {
                        throw new BusinessException(ResultEnum.FAILED.getCode(), "麻醉待办处理失败，已完成的待办不能被取消或修改");
                    }
                } else {
                    todo.setStatus(KArtConstants.TodoStatus.CANCEL);
                    super.update(todo);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void planCancel(Long planId, boolean skip) {
        List<TodoAnaesthesia> list = this.getPlanTodo(null, null, null, planId, null);
        list = list.stream().filter(t -> !TodoStatus.CANCEL.equals(t.getStatus())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(list)) {
            for (TodoAnaesthesia todo : list) {
                if (TodoStatus.FINISH.equals(todo.getStatus())) {
                    if (!skip) {
                        throw new BusinessException(ResultEnum.FAILED.getCode(), "麻醉待办处理失败，已完成的待办不能被取消或修改");
                    }
                } else {
                    todo.setStatus(TodoStatus.CANCEL);
                    super.update(todo);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void suspendCycle(Long cycleId, Long planId) {
        List<TodoAnaesthesia> list = this.getPlanTodo(cycleId, null, null, planId, null);
        list = list.stream().filter(t -> !KArtConstants.TodoStatus.CANCEL.equals(t.getStatus())).collect(Collectors.toList());
        for (TodoAnaesthesia todo : list) {
            // 只要是未完成、已确认的，都可以阻断，后面都可以再启动
            if (KArtConstants.TodoStatus.DEFAULT.equals(todo.getStatus())) {
                todo.setStatus(KArtConstants.TodoStatus.CANCEL);
                super.update(todo);
            }
        }
    }

    private List<TodoAnaesthesia> getPlanTodo(Long cycleId, Integer item, Date datetime, Long planId, Long subPlanId) {
        QueryTodoVO queryVo = new QueryTodoVO();
        queryVo.setCycleId(cycleId);
        queryVo.setItem(item);
        queryVo.setExecuteTime(datetime);
        if (ObjectUtil.isNotNull(planId)) {
            queryVo.setSourceModule(KArtConstants.TodoSourceModule.ART_PLAN);
            queryVo.setSourceModuleId(planId);
            queryVo.setSubSourceModuleId(subPlanId);
        } else {
            queryVo.setSourceModule(KArtConstants.TodoSourceModule.OTHER);
        }
        return this.getList(queryVo, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restartCycle(Long cycleId, Long planId) {
        List<TodoAnaesthesia> list = this.getPlanTodo(cycleId, null, null, planId, null);
        for (TodoAnaesthesia todo : list) {
            // 对已经作废的，重新启用，变为未完成
            if (KArtConstants.TodoStatus.CANCEL.equals(todo.getStatus())) {
                // 要求当天或当天之后
                if (todo.getExecuteTime().after(DateUtil.endOfDay(DateUtil.yesterday()))) {
                    todo.setStatus(KArtConstants.TodoStatus.DEFAULT);
                    super.update(todo);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateState(Long id, int status) {
        this.updateState(id, status, StrUtil.EMPTY);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBurglaryStatus(Long id, int status) {
        TodoAnaesthesia todo = super.get(id);
        if (todo != null) {
            todo.setBurglaryStatus(status);
            super.update(todo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateState(Long id, int status, String remark) {
        /* 定义：remark=""，不更新remark， remark=null, 整体设置为null, 其他情况做append  */
        TodoAnaesthesia todo = super.get(id);
        if (todo != null) {
            todo.setStatus(status);
            // 完成时也代表出室
            todo.setBurglaryStatus(KArtConstants.TodoStatus.CONFIRM);
            if (remark == null) {
                todo.setRemark(null);
            } else if (StrUtil.isNotEmpty(remark)) {
                todo.setRemark(todo.getRemark().concat(KArtConstants.Symbols.SEMICOLON).concat(remark));
            }
            super.update(todo);
        }
    }

    @Override
    public List<TodoAnaesthesia> getList(QueryTodoVO vo, boolean asc) {
        LambdaQueryWrapper<TodoAnaesthesia> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtil.isNotNull(vo.getCycleId()), TodoAnaesthesia::getCycleId, vo.getCycleId())
            .eq(vo.getItem() != null, TodoAnaesthesia::getItem, vo.getItem())
            .in(CollUtil.isNotEmpty(vo.getItems()), TodoAnaesthesia::getItem, vo.getItems()).and(StrUtil.isNotBlank(vo.getPatientName()),
                w -> w.eq(TodoAnaesthesia::getPatientFemaleName, vo.getPatientName()).or().eq(TodoAnaesthesia::getPatientMaleName, vo.getPatientName()))
            .and(StrUtil.isNotBlank(vo.getPatientCardNo()), w -> w.eq(TodoAnaesthesia::getPatientFemaleCardNo, vo.getPatientCardNo()).or()
                .eq(TodoAnaesthesia::getPatientMaleCardNo, vo.getPatientCardNo())).and(StrUtil.isNotBlank(vo.getPatientIdentifyId()),
                w -> w.eq(TodoAnaesthesia::getPatientFemaleIdentifyId, vo.getPatientIdentifyId()).or()
                    .eq(TodoAnaesthesia::getPatientMaleIdentifyId, vo.getPatientIdentifyId())).and(ObjectUtil.isNotNull(vo.getPatientId()),
                w -> w.eq(TodoAnaesthesia::getPatientFemaleId, vo.getPatientId()).or().eq(TodoAnaesthesia::getPatientMaleId, vo.getPatientId()))
            .eq(ObjectUtil.isNotNull(vo.getArtDoctorId()), TodoAnaesthesia::getArtDoctorId, vo.getArtDoctorId())
            .eq(StrUtil.isNotBlank(vo.getArtDoctorName()), TodoAnaesthesia::getArtDoctorName, vo.getArtDoctorName())
            .eq(vo.getTreatmentPlan() != null, TodoAnaesthesia::getTreatmentPlan, vo.getTreatmentPlan())
            .eq(vo.getCohPlan() != null, TodoAnaesthesia::getCohPlan, vo.getCohPlan())
            .eq(vo.getStatus() != null, TodoAnaesthesia::getStatus, vo.getStatus())
            .eq(vo.getSourceModule() != null, TodoAnaesthesia::getSourceModule, vo.getSourceModule())
            .eq(ObjectUtil.isNotNull(vo.getSourceModuleId()), TodoAnaesthesia::getSourceModuleId, vo.getSourceModuleId())
            .eq(ObjectUtil.isNotNull(vo.getSubSourceModuleId()), TodoAnaesthesia::getSubSourceModuleId, vo.getSubSourceModuleId())
            .in(CollUtil.isNotEmpty(vo.getDoctors()), TodoAnaesthesia::getArtDoctorId, vo.getDoctors())
            .eq(ObjectUtil.isNotNull(vo.getTeamId()), TodoAnaesthesia::getTeamId, vo.getTeamId())
            .in(CollUtil.isNotEmpty(vo.getTeamIds()), TodoAnaesthesia::getTeamId, vo.getTeamIds());
        if (vo.getExecuteTime() != null) {
            wrapper.ge(TodoAnaesthesia::getExecuteTime, DateUtil.beginOfDay(vo.getExecuteTime()));
            wrapper.le(TodoAnaesthesia::getExecuteTime, DateUtil.endOfDay(vo.getExecuteTime()));
        }

        wrapper.orderByAsc(TodoAnaesthesia::getItem);
        if (asc) {
            wrapper.orderByAsc(TodoAnaesthesia::getExecuteTime);
        } else {
            wrapper.orderByDesc(TodoAnaesthesia::getExecuteTime);
        }

        return this.getList(wrapper);
    }

    @Override
    public IPage<TodoAnaesthesia> getList(PageParams page) {
        LambdaQueryWrapper<TodoAnaesthesia> wrapper = Wrappers.lambdaQuery(TodoAnaesthesia.class);
        Map<String, Object> params = page.getParams();
        wrapper.in(params.containsKey("items"), TodoAnaesthesia::getItem, params.get("items") == null ? null
                : StrUtil.split(params.get("items").toString(), CharUtil.COMMA).stream().mapToInt(Integer::valueOf).boxed().collect(Collectors.toList()))
            .ge(params.containsKey("beginExecuteTime"), TodoAnaesthesia::getExecuteTime,
                params.get("beginExecuteTime") == null ? null : DateUtil.beginOfDay(DateUtil.parseDate(params.get("beginExecuteTime").toString())))
            .le(params.containsKey("endExecuteTime"), TodoAnaesthesia::getExecuteTime,
                params.get("endExecuteTime") == null ? null : DateUtil.endOfDay(DateUtil.parseDate(params.get("endExecuteTime").toString())))
            .and(params.containsKey("patientName"), w -> w.eq(TodoAnaesthesia::getPatientFemaleName, params.get("patientName")).or()
                .eq(TodoAnaesthesia::getPatientMaleName, params.get("patientName"))).and(params.containsKey("patientCardNo"),
                w -> w.eq(TodoAnaesthesia::getPatientFemaleCardNo, params.get("patientCardNo")).or()
                    .eq(TodoAnaesthesia::getPatientMaleCardNo, params.get("patientCardNo"))).and(params.containsKey("patientIdentifyId"),
                w -> w.eq(TodoAnaesthesia::getPatientFemaleIdentifyId, params.get("patientIdentifyId")).or()
                    .eq(TodoAnaesthesia::getPatientMaleIdentifyId, params.get("patientIdentifyId"))).and(params.containsKey("patientId"),
                w -> w.eq(TodoAnaesthesia::getPatientFemaleId, params.get("patientId")).or()
                    .eq(TodoAnaesthesia::getPatientMaleId, params.get("patientId")))
            .eq(params.containsKey("doctorId"), TodoAnaesthesia::getArtDoctorId, params.get("doctorId"))
            .eq(params.containsKey("doctorName"), TodoAnaesthesia::getArtDoctorName, params.get("doctorName"))
            .eq(params.containsKey("treatmentPlan"), TodoAnaesthesia::getTreatmentPlan, params.get("treatmentPlan"))
            .eq(params.containsKey("cohPlan"), TodoAnaesthesia::getCohPlan, params.get("cohPlan"))
            .eq(params.containsKey("status"), TodoAnaesthesia::getStatus, params.get("status"))
            .in(params.containsKey("doctors"), TodoAnaesthesia::getArtDoctorId,
                params.get("doctors") == null ? null : JsonUtil.toList(params.get("doctors").toString(), Long.class))
            .eq(params.containsKey("teamId"), TodoAnaesthesia::getTeamId, params.get("teamId"))
            .in(params.containsKey("teamIds"), TodoAnaesthesia::getTeamId,
                params.get("teamIds") == null ? null : JsonUtil.toList(params.get("teamIds").toString(), Long.class));

        wrapper.orderByAsc(TodoAnaesthesia::getItem).orderByDesc(TodoAnaesthesia::getExecuteTime);
        return mapper.selectPage(convertPage(page), wrapper);
    }

    // @Override
    // @Transactional(rollbackFor = Exception.class)
    // public void restore(Long id) {
    // if (ObjectUtil.isNotNull(id)) {
    // TodoAnaesthesia entity = this.get(id);
    // if (entity != null) {
    // if (ObjectUtil.equals(entity.getStatus(), TodoStatus.FINISH)) {
    // entity.setStatus(TodoStatus.DEFAULT);
    // this.update(entity);
    // }
    //
    // }
    // }
    // }

    @Override
    public TodoStatVO doStat(Date beginDate, Date endDate) {
        Date begin = DateUtil.beginOfDay(beginDate);
        Date end = DateUtil.endOfDay(endDate);
        List<TodoStatDetailVO> details = mapper.doStat(begin, end);
        details.forEach(item -> item.setItemType(KArtConstants.TodoItemType.ANA));
        TodoStatVO vo = new TodoStatVO();
        vo.setDetails(details);
        vo.setBeginDate(beginDate);
        vo.setEndDate(endDate);
        return vo;
    }

    @Override
    public List<TodoGroup> getGroupList(QueryTodoVO vo) {
        // 获取列表，按时间升序
        List<TodoAnaesthesia> list = this.getList(vo, true);
        TreeMap<Integer, TodoGroup> resultMap = new TreeMap<>();
        for (TodoAnaesthesia entity : list) {
            Integer item = entity.getItem();
            if (!resultMap.containsKey(item)) {
                TodoGroup group = new TodoGroup();
                group.setItem(item);
                group.setEtGroup(new ArrayList<>());
                resultMap.put(item, group);
            }

            resultMap.get(item).getAnaGroup().add(entity);
        }
        return new ArrayList<>(resultMap.values());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long restorePlanTodo(Long cycleId, Integer item, Date datetime, Long planId, Long subPlanId) {
        TodoAnaesthesia todo = this.getOne(cycleId, item, datetime, null, KArtConstants.TodoSourceModule.ART_PLAN, planId, subPlanId, true);
        if (todo != null) {
            // 保证幂等，已作废则还原到未完成，否则不动
            Integer status = todo.getStatus();
            if (ObjectUtil.equal(status, TodoStatus.CANCEL)) {
                todo.setStatus(KArtConstants.TodoStatus.DEFAULT);
                super.update(todo);
            }
            return todo.getId();
        } else {
            // 恢复失败，直接抛出异常，这部分是不允许的，应该是代码问题
            throw new BusinessException(ResultEnum.FAILED.getCode(), "恢复待办异常，找不到待办");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setTreatment(EditTreatmentVO editTreatmentVO) {
        TodoAnaesthesia todo = mapper.selectById(editTreatmentVO.getId());
        if (null != todo) {
            BeanUtil.copyProperties(editTreatmentVO, todo);
            if (editTreatmentVO.getTreatmentSuggestion() == 2) {
                todo.setStatus(KArtConstants.TodoStatus.CANCEL);
            }
            todo.setRecorderTime(new Date());
            todo.setRecorderId(AuthUtil.getCurrentAuthInfo().getUserId());
            todo.setRecorderName(AuthUtil.getCurrentAuthInfo().getName());
            super.update(todo);
        }
    }

    @Override
    public IPage<TodoAnaesthesia> getPage(TodoPageVo page) {
        PageParams mPage = new PageParams();
        IPage<TodoAnaesthesia> iPage = new Page<>();
        mPage.setPage(page.getPage());
        mPage.setLimit(page.getSize());
        Date beginDate = null, endDate = null;
        try {
            beginDate = StrUtil.isEmpty(page.getBeginDate()) ? DateUtil.date() : DateUtil.parse(page.getBeginDate(), "yyyy-MM-dd");
            endDate = StrUtil.isEmpty(page.getEndDate()) ? DateUtil.date() : DateUtil.parse(page.getEndDate(), "yyyy-MM-dd");
        } catch (Exception e) {
            log.error("时间格式异常", e);
            throw new BusinessException(ResultEnum.FAILED.getCode(), "时间格式错误");
        }
        LambdaQueryWrapper<TodoAnaesthesia> wrapper = Wrappers.lambdaQuery(TodoAnaesthesia.class);
        wrapper.ge(TodoAnaesthesia::getExecuteTime, DateUtil.beginOfDay(beginDate)).le(TodoAnaesthesia::getExecuteTime, DateUtil.endOfDay(endDate))
            .in(StrUtil.isNotEmpty(page.getItems()), TodoAnaesthesia::getItem, StrUtil.isEmpty(page.getItems()) ? null
                : StrUtil.split(page.getItems(), CharUtil.COMMA).stream().mapToInt(Integer::valueOf).boxed().collect(Collectors.toList()))
            .orderByAsc(TodoAnaesthesia::getItem);
        wrapper.orderByAsc(TodoAnaesthesia::getItem).orderByDesc(TodoAnaesthesia::getExecuteTime);
        return mapper.selectPage(convertPage(mPage), wrapper);
    }

    @Override
    public TodoAnaesthesia getBySubPlan(int item, long planId, long subPlanId) {
        return mapper.selectOne(Wrappers.lambdaQuery(TodoAnaesthesia.class).eq(TodoAnaesthesia::getItem, item)
            .eq(TodoAnaesthesia::getSourceModule, KArtConstants.TodoSourceModule.ART_PLAN).eq(TodoAnaesthesia::getSourceModuleId, planId)
            .eq(TodoAnaesthesia::getSubSourceModuleId, subPlanId));
    }

    @Override
    public Long createNoneCycle(Long maleId, Long femaleId, Integer item, Date datetime, String remark, Integer sourceModule, Long sourceModuleId,
        Long subSourceModuleId) {

        TodoAnaesthesia todo = new TodoAnaesthesia();
        // 填充男病人信息
        if (maleId != null) {
            PatientInfo maleInfo = patientInfoService.get(maleId);
            if (maleInfo != null) {
                todo.setPatientMaleId(maleId);
                todo.setPatientMaleName(maleInfo.getName());
                todo.setPatientMaleCardNo(maleInfo.getCardNo());
                todo.setPatientMaleIdentifyId(maleInfo.getIdentifyId());
            }
        }
        // 填充女病人信息
        if (femaleId != null) {
            PatientInfo femaleInfo = patientInfoService.get(femaleId);

            todo.setPatientFemaleId(femaleId);
            todo.setPatientFemaleName(femaleInfo.getName());
            todo.setPatientFemaleCardNo(femaleInfo.getCardNo());
            todo.setPatientFemaleIdentifyId(femaleInfo.getIdentifyId());
        }
        // 填充公用信息
        todo.setItem(item);
        todo.setExecuteTime(datetime);
        todo.setSourceModule(sourceModule);
        todo.setSourceModuleId(sourceModuleId);
        todo.setSubSourceModuleId(subSourceModuleId);
        todo.setRemark(remark);
        todo.setStatus(KArtConstants.TodoStatus.DEFAULT);
        todo.setOrderNo(serialNoGenerator.generatorSerialNo(TodoCacheConstants.TODO_SURGERY_ORDER_CACHE_KEY, 3,
            TodoCacheConstants.TODO_SURGERY_ORDER_DATE_CACHE_KEY, "TodoSpecialSurgery"));
        return super.insert(todo);
    }

    @Override
    public void deleteByOtherModuleId(Long sourceModuleId) {
        List<TodoAnaesthesia> list = mapper.selectList(
            Wrappers.lambdaQuery(TodoAnaesthesia.class).eq(TodoAnaesthesia::getSourceModule, KArtConstants.TodoSourceModule.OTHER)
                .eq(TodoAnaesthesia::getSourceModuleId, sourceModuleId));
        if (CollUtil.isNotEmpty(list)) {
            // 这里防止出问题，只删第一条，理论上只有1条
            mapper.deleteById(list.get(0).getId());
        }
    }

    @Override
    public TodoAnaesthesia getFirstByModuleId(Long sourceModuleId) {
        return mapper.selectOne(Wrappers.lambdaQuery(TodoAnaesthesia.class).eq(TodoAnaesthesia::getSourceModule, KArtConstants.TodoSourceModule.OTHER)
            .eq(TodoAnaesthesia::getSourceModuleId, sourceModuleId).orderByAsc(TodoAnaesthesia::getExecuteTime).last(" limit 1"));
    }
}
