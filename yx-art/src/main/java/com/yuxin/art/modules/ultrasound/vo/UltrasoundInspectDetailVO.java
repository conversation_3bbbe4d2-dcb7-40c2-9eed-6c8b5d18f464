/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:UltrasoundInspectDetailVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.ultrasound.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * B超检查明细
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-19
 */
@Data
@NoArgsConstructor
@ApiModel(value = "UltrasoundInspectDetailVO", description = "B超检查明细")
public class UltrasoundInspectDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @ApiModelProperty(value = "ID,编辑时必填")
    public Long id;
    /**
     * B超监测主键ID
     */
    @ApiModelProperty(value = "B超监测ID")
    private Long ultrasoundId;
    /**
     * 位置 0：左 1：右
     */
    @ApiModelProperty(value = "位置. 0:左,1:右.", required = true)
    @NotNull(message = "位置不能为空")
    @Range(min = 0, max = 1, message = "无法识别的位置")
    private Integer position;
    /**
     * 长
     */
    @ApiModelProperty(value = "卵泡长(1-99).长和宽要么都填,要么都不填")
    @Min(value = 1, message = "卵泡长不能小于等于0")
    @Max(value = 99, message = "卵泡长不能大于99")
    private Integer length;
    /**
     * 宽
     */
    @ApiModelProperty(value = "卵泡宽(1-99).长和宽要么都填,要么都不填")
    @Min(value = 1, message = "卵泡宽不能小于等于0")
    @Max(value = 99, message = "卵泡宽不能大于99")
    private Integer width;
    /**
     * 平均值
     */
    @ApiModelProperty(value = "平均值(2,1).若长宽有值,则必填,否则非必填")
    @Digits(integer = 2, fraction = 1, message = "平均值只能包含2位整数,1位小数")
    @DecimalMin(value = "0.1", message = "平均值不能小于等于0")
    private BigDecimal average;
    /**
     * 序号
     */
    @ApiModelProperty(value = "序号(1-1000)", required = true)
    @NotNull(message = "序号不能为空")
    @Min(value = 1, message = "序号不能小于1")
    @Max(value = 1000, message = "序号不能大于1000")
    private Integer sort;

}
