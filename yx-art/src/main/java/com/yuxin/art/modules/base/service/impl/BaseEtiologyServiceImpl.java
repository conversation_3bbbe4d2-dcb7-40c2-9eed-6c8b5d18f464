/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:BaseEtiologyServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.base.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.yuxin.api.ResultEnum;
import com.yuxin.art.domain.base.BaseEtiology;
import com.yuxin.art.modules.base.mapper.BaseEtiologyMapper;
import com.yuxin.art.modules.base.service.BaseEtiologyService;
import com.yuxin.art.modules.base.vo.req.BaseEtiologyPageReqVO;
import com.yuxin.art.modules.base.vo.req.BaseEtiologySaveReqVO;
import com.yuxin.framework.exception.BusinessException;
import com.yuxin.framework.mvc.vo.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 病因类型资料 服务实现类
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Slf4j
@Service
public class BaseEtiologyServiceImpl implements BaseEtiologyService {
    @Autowired
    private BaseEtiologyMapper baseEtiologyMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseEtiology createEtiology(BaseEtiologySaveReqVO baseEtiologySaveReqVO) {
        // 校验唯一性
        validateNameUnique(null, baseEtiologySaveReqVO.getName());
        validateCodeUnique(null, baseEtiologySaveReqVO.getCode());

        // 创建实体
        BaseEtiology entity = new BaseEtiology();
        BeanUtil.copyProperties(baseEtiologySaveReqVO, entity);

        // 保存
        baseEtiologyMapper.insert(entity);
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEtiology(BaseEtiologySaveReqVO baseEtiologySaveReqVO) {
        // 校验唯一性
        validateNameUnique(baseEtiologySaveReqVO.getId(), baseEtiologySaveReqVO.getName());
        validateCodeUnique(baseEtiologySaveReqVO.getId(), baseEtiologySaveReqVO.getCode());

        // 更新实体
        BaseEtiology entity = new BaseEtiology();
        BeanUtil.copyProperties(baseEtiologySaveReqVO, entity);

        // 保存
        baseEtiologyMapper.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteEtiology(Long id) {
        // 检查是否存在
        BaseEtiology exist = baseEtiologyMapper.selectById(id);
        if (exist == null) {
            throw new BusinessException(ResultEnum.FAILED.getCode(), "病因类型资料不存在");
        }

        // 删除
        baseEtiologyMapper.deleteById(id);
    }

    @Override
    public PageResult<BaseEtiology> getEtiologyPage(BaseEtiologyPageReqVO req) {
        return baseEtiologyMapper.selectPage(req);
    }

    @Override
    public List<BaseEtiology> getEtiologyListByEnable() {
        return baseEtiologyMapper.selectListByEnable();
    }

    @Override
    public List<BaseEtiology> getList() {
        return baseEtiologyMapper.selectListAll();
    }

    /**
     * 校验名称是否重复
     */
    private void validateNameUnique(Long id, String name) {
        BaseEtiology exist = baseEtiologyMapper.selectByName(name);
        if (exist == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的数据
        if (id == null) {
            throw new BusinessException("病因名称不能重复");
        }
        if (!exist.getId().equals(id)) {
            throw new BusinessException("病因名称不能重复");
        }
    }

    /**
     * 校验编码是否重复
     */
    private void validateCodeUnique(Long id, String code) {
        BaseEtiology exist = baseEtiologyMapper.selectByCode(code);
        if (exist == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的数据
        if (id == null) {
            throw new BusinessException("病因编码不能重复");
        }
        if (!exist.getId().equals(id)) {
            throw new BusinessException("病因编码不能重复");
        }
    }
}
