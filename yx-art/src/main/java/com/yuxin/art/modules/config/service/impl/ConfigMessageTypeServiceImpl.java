/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ConfigMessageTypeServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.config.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuxin.art.domain.config.ConfigMessageType;
import com.yuxin.art.modules.config.mapper.ConfigMessageTypeMapper;
import com.yuxin.art.modules.config.service.ConfigMessageTypeService;
import com.yuxin.framework.mvc.mapper.BaseMapper;
import com.yuxin.framework.mvc.service.impl.BaseServiceImpl;
import com.yuxin.security.util.AuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * ServiceImpl
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
public class ConfigMessageTypeServiceImpl extends BaseServiceImpl<ConfigMessageType> implements ConfigMessageTypeService {
    @Autowired
    private ConfigMessageTypeMapper configMessageTypeMapper;

    @Override
    protected BaseMapper<ConfigMessageType> getMapper() {
        return configMessageTypeMapper;
    }

    @Override
    public ConfigMessageType get(Long id) {
        return configMessageTypeMapper.selectById(id);
    }

    @Override
    public List<ConfigMessageType> getList(Wrapper<ConfigMessageType> wrapper) {
        return super.getList(wrapper);
    }

    @Override
    public List<ConfigMessageType> getList() {
        return configMessageTypeMapper.selectList(Wrappers.lambdaQuery(ConfigMessageType.class).orderByDesc(ConfigMessageType::getSort));
    }

    @Override
    public Long insert(ConfigMessageType entity) {
        // entity.setId(KeyGenerate.generateId());
        entity.setId(null);
        entity.setRecorderId(AuthUtil.getCurrentAuthInfo().getUserId());
        entity.setRecorderName(AuthUtil.getCurrentAuthInfo().getName());
        entity.setRecorderTime(new Date());
        configMessageTypeMapper.insert(entity);
        return entity.getId();
    }

    @Override
    public void delete(Long id) {
        configMessageTypeMapper.deleteById(id);
    }

    @Override
    public void update(ConfigMessageType entity) {
        entity.setRecorderId(AuthUtil.getCurrentAuthInfo().getUserId());
        entity.setRecorderName(AuthUtil.getCurrentAuthInfo().getName());
        entity.setRecorderTime(new Date());
        configMessageTypeMapper.updateById(entity);
    }

}
