/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:BaseConsumableTypeCache.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.cache.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yuxin.art.domain.base.BaseConsumableType;
import com.yuxin.art.modules.base.service.BaseConsumableTypeService;
import com.yuxin.cache.util.RedisListUtil;
import com.yuxin.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 耗材类型缓存
 *
 * <AUTHOR>
 * @date 2020/6/6
 */
@Slf4j
@Component
public class BaseConsumableTypeCache {
    @Autowired
    private RedisListUtil redisListUtil;

    /** 加载缓存 */
    public void initCache() {
        if (!redisListUtil.hasKey(getCacheKey())) {
            log.info("初始化耗材类型缓存");
            reload();
        }
    }

    /**
     * 重新加载耗材类型数据
     */
    public void reload() {
        BaseConsumableTypeService baseConsumableTypeService = SpringUtil.getBean(BaseConsumableTypeService.class);
        List<BaseConsumableType> list = baseConsumableTypeService.getConsumableTypeList();
        redisListUtil.del(getCacheKey());
        if (CollectionUtil.isNotEmpty(list)) {
            log.info("正在重新加载耗材类型缓存,加载数量:{}", list.size());
            redisListUtil.lSetList(getCacheKey(), list);
        }
    }

    /**
     * 添加耗材类型缓存
     *
     * @param baseConsumableType
     */
    public void add(BaseConsumableType baseConsumableType) {
        log.info("添加耗材类型缓存");
        if (ObjectUtil.isNotNull(baseConsumableType)) {
            redisListUtil.lSet(getCacheKey(), baseConsumableType);
        }
    }

    /**
     * 缓存耗材类型前缀
     *
     * @return
     */
    public String getCacheKey() {
        return BaseCacheConstants.BASE_CONSUMABLES_TYPE;
    }
}
