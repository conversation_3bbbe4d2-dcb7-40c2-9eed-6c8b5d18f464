/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ConfigExamineAuditTemplateCache.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.cache.config;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.yuxin.art.domain.config.ConfigExamineAuditTemp;
import com.yuxin.art.modules.config.service.ConfigExamineAuditTemplateService;
import com.yuxin.cache.util.RedisListUtil;
import com.yuxin.constant.GlobalConstant;
import com.yuxin.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 检验单审核配置缓存
 *
 * <AUTHOR>
 * @date 2020/6/6
 */
@Slf4j
@Component
public class ConfigExamineAuditTemplateCache {
    @Autowired
    private RedisListUtil redisListUtil;

    /** 加载缓存 */
    public void initCache() {
        if (!redisListUtil.hasKey(getCacheKey())) {
            log.info("初始化可用检验单审核模板缓存缓存");
            reload();
        }
    }

    /**
     * 重新加载可用检验单审核模板数据
     */
    public void reload() {
        ConfigExamineAuditTemplateService configExamineAuditTemplateService = SpringUtil.getBean(ConfigExamineAuditTemplateService.class);
        List<ConfigExamineAuditTemp> list = configExamineAuditTemplateService.findListByStatus(GlobalConstant.YES);
        redisListUtil.del(getCacheKey());
        if (CollectionUtil.isNotEmpty(list)) {
            log.info("正在重新加载可用检验单审核模板缓存,加载数量:{}", list.size());
            redisListUtil.lSetList(getCacheKey(), list);
        }
    }

    /**
     * 获取可用检验单审核模板缓存
     *
     * @return
     */
    public List<ConfigExamineAuditTemp> getAvailableList() {
        log.info("获取可用检验单审核模板缓存");
        List<ConfigExamineAuditTemp> list = Lists.newArrayList();
        Object object = redisListUtil.lGet(getCacheKey(), 0, -1);
        if (ObjectUtil.isNotNull(object)) {
            list = (List<ConfigExamineAuditTemp>)object;
            Collections.sort(list, Comparator.comparing(ConfigExamineAuditTemp::getRecorderTime).reversed());
        }
        return list;
    }

    /**
     * 添加检验单审核模板到缓存
     *
     * @param configExamineAuditTemp
     */
    public void add(ConfigExamineAuditTemp configExamineAuditTemp) {
        if (ObjectUtil.isNotNull(configExamineAuditTemp)) {
            if (GlobalConstant.YES == configExamineAuditTemp.getStatus()) {
                log.info("添加检验单审核模板到缓存");
                redisListUtil.lSet(getCacheKey(), configExamineAuditTemp);
            }
        }
    }

    /**
     * 检验单审核前缀
     *
     * @return
     */
    public String getCacheKey() {
        return ConfigModuleConstants.CONFIG_EXAMINE_AUDIT_TEMPLATE;
    }
}
