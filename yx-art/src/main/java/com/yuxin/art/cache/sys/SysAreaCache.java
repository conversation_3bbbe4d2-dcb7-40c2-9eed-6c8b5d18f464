/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:SysAreaCache.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.cache.sys;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.yuxin.art.domain.sys.SysArea;
import com.yuxin.art.modules.sys.mapper.SysAreaMapper;
import com.yuxin.art.modules.sys.vo.rsp.SysCountryRspVO;
import com.yuxin.cache.util.RedisMapUtil;
import com.yuxin.constant.GlobalConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 区域缓存操作
 *
 * <AUTHOR>
 * @date 2020-05-27
 */
@Component
@Slf4j
public class SysAreaCache {
    @Autowired
    private RedisMapUtil redisMapUtil;
    @Autowired
    private SysAreaMapper sysAreaMapper;

    /**
     * 加载科室数据到缓存
     */
    public void load() {
        if (!redisMapUtil.hasKey(SysModuleConstants.SYS_DEPT_CACHE_KEY)) {
            reload();
        }
    }

    /**
     * 重新加载缓存
     */
    public void reload() {
        log.info("正在重新加载区域缓存");
        List<SysArea> list = sysAreaMapper.selectList(
            Wrappers.lambdaQuery(SysArea.class).eq(SysArea::getState, GlobalConstant.YES).orderByAsc(SysArea::getLevel).orderByAsc(SysArea::getSort));
        // 拿到省
        List<SysArea> province = list.stream().filter(item -> item.getLevel() == 1).collect(Collectors.toList());
        Map<Long, SysArea> provinceMap = Maps.uniqueIndex(province, vo -> vo.getId());
        // 拿到市
        List<SysArea> city = list.stream().filter(item -> item.getLevel() == 2).collect(Collectors.toList());
        Map<Long, SysArea> cityMap = Maps.uniqueIndex(city, vo -> vo.getId());
        // 拿到县
        List<SysArea> country = list.stream().filter(item -> item.getLevel() == 3).collect(Collectors.toList());
        Map<String, Object> map = new HashMap<>();
        country.forEach(item -> {
            SysCountryRspVO vo = BeanUtil.copyProperties(item, SysCountryRspVO.class);
            if (cityMap.containsKey(item.getParentId())) {
                vo.setCityId(item.getParentId());
                SysArea sysCity = cityMap.get(vo.getCityId());
                if (sysCity != null) {
                    vo.setCityName(sysCity.getName());
                    Long provinceId = sysCity.getParentId();
                    vo.setProvinceId(provinceId);
                    if (provinceMap.containsKey(provinceId)) {
                        SysArea sysProvince = provinceMap.get(provinceId);
                        if (sysProvince != null) {
                            vo.setProvinceName(sysProvince.getName());
                        }
                    }
                }
            }
            map.put(item.getId().toString(), vo);
        });

        redisMapUtil.del(SysModuleConstants.SYS_AREA_CACHE_KEY);
        redisMapUtil.hmset(SysModuleConstants.SYS_AREA_CACHE_KEY, map);
    }

    public SysCountryRspVO getByCountryId(Long countryId) {
        return (SysCountryRspVO)redisMapUtil.hget(SysModuleConstants.SYS_AREA_CACHE_KEY, countryId.toString());
    }
}
