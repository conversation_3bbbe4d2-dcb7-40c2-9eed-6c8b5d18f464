/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:HospitalExternalController.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.external.contoller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.yuxin.art.domain.upcoming.*;
import com.yuxin.art.external.vo.ExternalResult;
import com.yuxin.art.external.vo.IndexStatVo;
import com.yuxin.art.external.vo.TodoPageVo;
import com.yuxin.art.modules.upcoming.service.*;
import com.yuxin.art.modules.upcoming.vo.TodoStatDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 医院信息对外接口
 *
 * <AUTHOR>
 * @date 2021-07-26
 **/
@RestController
@RequestMapping(value = "/external/hospital", produces = MediaType.APPLICATION_JSON_VALUE)
@Validated
@Api(tags = "医院信息对外接口")
@ApiSort(1)
public class HospitalExternalController {

    @Autowired
    private CommonTodoService commonTodoService;
    @Autowired
    private TodoCohService todoCohService;
    @Autowired
    private TodoClinicalService todoClinicalService;
    @Autowired
    private TodoSpecialSurgeryService todoSpecialSurgeryService;
    @Autowired
    private TodoLabService todoLabService;
    @Autowired
    private TodoEtService todoEtService;

    /**
     * 根据日期查询待办信息
     *
     * @param indexStatVo
     * @return
     */
    @GetMapping("/getIndexStat")
    @ApiOperation(value = "日期查询待办信息")
    @ApiOperationSupport(order = 1)
    public ExternalResult<List<TodoStatDetailVO>> getIndexStat(@Validated IndexStatVo indexStatVo) {
        List<TodoStatDetailVO> indexStatInfo = commonTodoService.doHospitalIndexStat(indexStatVo);
        return ExternalResult.success(indexStatInfo);
    }

    /**
     * 分页查询Coh待办列表
     *
     * @param page
     * @return
     */
    @GetMapping("/getCohPage")
    @ApiOperation(value = "分页查询Coh待办列表")
    @ApiOperationSupport(order = 2)
    public ExternalResult<IPage<TodoCoh>> getCohPage(@Validated TodoPageVo page) {

        IPage<TodoCoh> list = todoCohService.getPage(page);
        return ExternalResult.success(list);
    }

    /**
     * 分页查询临床待办列表
     *
     * @param page
     * @return
     */
    @GetMapping("/getClinicalPage")
    @ApiOperation(value = "分页查询临床待办列表")
    @ApiOperationSupport(order = 3)
    public ExternalResult<IPage<TodoClinical>> getClinicalPage(@Validated TodoPageVo page) {
        IPage<TodoClinical> pageInfo = todoClinicalService.getPage(page);
        return ExternalResult.success(pageInfo);
    }

    /**
     * 分页查询特殊手术待办列表
     *
     * @param page
     * @return
     */
    @GetMapping("/getSpecialSurgeryPage")
    @ApiOperation(value = "分页查询特殊手术待办列表")
    @ApiOperationSupport(order = 4)
    public ExternalResult<IPage<TodoSpecialSurgery>> getSpecialSurgeryPage(@Validated TodoPageVo page) {
        IPage<TodoSpecialSurgery> pageInfo = todoSpecialSurgeryService.getPage(page);
        return ExternalResult.success(pageInfo);
    }

    /**
     * 分页查询实验室待办列表
     *
     * @param page
     * @return
     */
    @GetMapping("/getLabPage")
    @ApiOperation(value = "分页查询实验室待办列表")
    @ApiOperationSupport(order = 4)
    public ExternalResult<IPage<TodoLab>> getLabPage(@Validated TodoPageVo page) {
        IPage<TodoLab> pageInfo = todoLabService.getPage(page);
        return ExternalResult.success(pageInfo);
    }

    /**
     * 分页查询移植待办列表
     *
     * @param page
     * @return
     */
    @GetMapping("/getEtPage")
    @ApiOperation(value = "分页查询移植待办列表")
    @ApiOperationSupport(order = 5)
    public ExternalResult<IPage<TodoEt>> getEtPage(@Validated TodoPageVo page) {
        IPage<TodoEt> pageInfo = todoEtService.getPage(page);
        return ExternalResult.success(pageInfo);
    }
}
