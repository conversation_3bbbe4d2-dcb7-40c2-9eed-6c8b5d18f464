/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ArtInseminationVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.external.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 授精响应数据实体类
 *
 * <AUTHOR>
 * @date 2021-03-12
 **/
@Data
@NoArgsConstructor
@ApiModel(value = "ArtInseminationVO", description = "授精响应数据实体类")
public class ArtInseminationVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "周期主键ID")
    private Long cycleId;

    @ApiModelProperty(value = "女性患者ID")
    private String patientFemaleIdentifyId;

    @ApiModelProperty(value = "男性患者ID")
    private String patientMaleIdentifyId;

    @ApiModelProperty(value = " 受精方式")
    private String fertilizationMode;

    @ApiModelProperty(value = "授精时间")
    private Date inseminationTime;

    @ApiModelProperty(value = "总获卵数")
    private Integer totalOvumNum;

    @ApiModelProperty(value = "IVF数")
    private Integer ivfNum;

    @ApiModelProperty(value = "SIVF数")
    private Integer sIvfNum;

    @ApiModelProperty(value = "ICSI数")
    private Integer icsiNum;

    @ApiModelProperty(value = "LICSI数")
    private Integer lIcsiNum;

    @ApiModelProperty(value = "RICSI数")
    private Integer rIcsiNum;
}
