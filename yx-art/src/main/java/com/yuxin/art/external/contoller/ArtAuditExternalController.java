/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:ArtAuditExternalController.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.external.contoller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.yuxin.art.external.vo.ArtAuditVO;
import com.yuxin.art.external.vo.ExternalResult;
import com.yuxin.art.modules.audit.service.ArtWeeklyReviewDetailService;
import com.yuxin.art.modules.audit.service.ArtWeeklyReviewProcessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 进周审查对外接口
 *
 * <AUTHOR>
 * @date 2021-03-10
 **/
@RestController
@RequestMapping(value = "/external/audit", produces = MediaType.APPLICATION_JSON_VALUE)
@Validated
@Api(tags = "进周审查对外接口")
@ApiSort(1)
public class ArtAuditExternalController {

    @Autowired
    private ArtWeeklyReviewProcessService artWeeklyReviewProcessService;
    @Autowired
    private ArtWeeklyReviewDetailService artWeeklyReviewDetailService;

    /**
     * 根据获取周期ID或预约ID集合获取进周审查信息
     *
     * @param reviewUniqueIds
     * @return
     */
    @GetMapping("/getInfoByReviewUniqueIds")
    @ApiOperation(value = "根据获取周期ID或预约ID集合获取进周审查信息")
    @ApiImplicitParam(name = "reviewUniqueIds", value = "周期ID或预约ID集合", required = true, paramType = "query", dataType = "String")
    @ApiOperationSupport(order = 1)
    public ExternalResult<List<ArtAuditVO>> getInfoByUniqueId(@NotNull(message = "周期ID或预约ID不能为空") String reviewUniqueIds) {
        List<ArtAuditVO> artAuditVOS = artWeeklyReviewProcessService.getAuditVOByReviewUniqueIds(reviewUniqueIds);
        return ExternalResult.success(artAuditVOS);
    }
}
