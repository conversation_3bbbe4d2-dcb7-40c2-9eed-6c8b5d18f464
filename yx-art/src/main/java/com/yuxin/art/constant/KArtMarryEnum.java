/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:KArtMarryEnum.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.constant;

/**
 * <AUTHOR>
 * @date 2020-07-29
 */
public enum KArtMarryEnum {
    /**
     * 温附一：
     * 未婚
     * 已婚
     * 离婚
     * 再婚
     * 丧偶
     * 其他
     */
    SPINSTERHOOD("未婚", 0), MARRIED("已婚", 1), DIVORCE("离婚", 2), REMARRIAGE("再婚", 3), WIDOWED("丧偶", 4), OTHER("其他", 5);

    /**
     * 编码
     */
    private Integer code;

    /**
     * 值
     */
    private String value;

    KArtMarryEnum(String value, Integer code) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过code获取获取value
     *
     * @param code
     * @return
     */
    public static String getValue(Integer code) {
        for (KArtMarryEnum item : KArtMarryEnum.values()) {
            if (code.equals(item.code)) {
                return item.value;
            }
        }
        return null;
    }

    /**
     * 通过code获取获取value
     *
     * @param value
     * @return
     */
    public static Integer getCode(String value) {
        for (KArtMarryEnum item : KArtMarryEnum.values()) {
            if (value.equals(item.value)) {
                return item.code;
            }
        }
        return null;
    }

}
