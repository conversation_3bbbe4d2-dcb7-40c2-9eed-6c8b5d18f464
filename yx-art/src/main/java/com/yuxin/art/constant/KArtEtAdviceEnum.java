/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:KArtEtAdviceEnum.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.constant;

/**
 * 胚胎观察类型
 *
 * <AUTHOR>
 * @date 2020-07-29
 */
public enum KArtEtAdviceEnum {
    // 移植
    CONFIRM("移植", 0), // 不移植
    DENY("不移植", 1), // 推迟移植
    DELAY("推迟移植", 2);

    /**
     * 字典编码
     */
    private Integer code;

    /**
     * 字典值
     */
    private String value;

    KArtEtAdviceEnum(String value, Integer code) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过code获取获取value
     *
     * @param code
     * @return
     */
    public static String getValue(Integer code) {
        for (KArtEtAdviceEnum item : KArtEtAdviceEnum.values()) {
            if (code.equals(item.code)) {
                return item.value;
            }
        }
        return null;
    }

    /**
     * 通过code获取获取value
     *
     * @param value
     * @return
     */
    public static Integer getCode(String value) {
        for (KArtEtAdviceEnum item : KArtEtAdviceEnum.values()) {
            if (value.equals(item.value)) {
                return item.code;
            }
        }
        return null;
    }
}
