/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:UpdateRelevanceVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.relevance.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 更新引用
 *
 * update ${tableName} set ${tableKey} = ${tableKeyValue} where ${relevanceKey} = ${relevanceKeyValue}
 *
 * <AUTHOR>
 * @date 2020/9/20
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UpdateRelevanceVO", description = "更新引用")
public class UpdateRelevanceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据表
     */
    @ApiModelProperty(value = "数据表")
    private String tableName;

    /**
     * 更新字段-值
     */
    @ApiModelProperty(value = "更新字段-值")
    private List<UpdateFieldValueVO> values;

    /**
     *
     */
    @ApiModelProperty(value = "引用字段")
    private String relevanceKey;

    /**
     * 引用值
     */
    @ApiModelProperty(value = "引用值")
    private Long relevanceKeyValue;

}
