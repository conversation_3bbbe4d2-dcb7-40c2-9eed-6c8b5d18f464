/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:SsoAuthController.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.his.controller;

import com.yuxin.api.Result;
import com.yuxin.art.cache.sys.SysUserCache;
import com.yuxin.art.his.config.SsoProperties;
import com.yuxin.art.his.service.SsoAuthService;
import com.yuxin.art.modules.auth.vo.AuthUser;
import com.yuxin.constant.RequestHeaderConstant;
import com.yuxin.domain.AuthInfo;
import com.yuxin.framework.mvc.controller.BaseController;
import com.yuxin.security.util.AuthUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 单点登录控制类
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@Slf4j
@RestController
@RequestMapping(value = "/sso/auth", produces = MediaType.APPLICATION_JSON_VALUE)
@Validated
@Api(tags = "单点登录控制类")
public class SsoAuthController extends BaseController {

    @Autowired
    private SsoAuthService ssoAuthService;
    @Autowired
    private SsoProperties ssoPro;
    @Autowired
    private SysUserCache sysUserCache;

    /**
     * 接收token，同步校验token，成功后登录
     *
     * @param token
     * @return
     */
    @GetMapping("/receive-token")
    @ApiOperation(value = "接收token")
    @ApiImplicitParams({@ApiImplicitParam(name = "token", value = "TOKEN", required = true, paramType = "query", dataType = "String"),
        @ApiImplicitParam(name = "userId", value = "用户ID", required = true, paramType = "query", dataType = "Long")})
    public Result<AuthUser> receiveToken(@NotBlank(message = "TOKEN不能为空") String token, @NotNull(message = "用户ID不能为空") String userId,
        HttpServletResponse response) {
        AuthInfo authInfo = ssoAuthService.receiveToken(token, userId);
        response.addHeader(RequestHeaderConstant.TOKEN, authInfo.getToken());
        AuthUser authUser = (AuthUser)authInfo.getUserInfo();
        authUser.setSsoAuthUrl(ssoPro.getLoginUrl());
        return Result.success(authUser);
    }

    /**
     * 用于登录失败后拉取第三方登录地址
     *
     * @return
     */
    @GetMapping("/getSsoAuthUrl")
    @ApiOperation(value = "用于登录失败后拉取第三方登录地址")
    public Result<String> getSsoAuthUrl() {
        String url = ssoPro.getLoginUrl();
        return Result.success(url);
    }

    @GetMapping("/convertRYKID")
    @ApiOperation(value = "转换人员库ID并取得签名")
    @ApiImplicitParams({@ApiImplicitParam(name = "rykId", value = "rykId", required = true, paramType = "query", dataType = "Long")})
    public Result<String> convertRYKID(@NotNull(message = "用户ID不能为空") Long rykId) {
        String token = sysUserCache.getLoginToken(AuthUtil.getCurrentAuthInfo().getUserId());
        ssoAuthService.convertRYKID(rykId, token);

        return Result.success("");
    }

}
