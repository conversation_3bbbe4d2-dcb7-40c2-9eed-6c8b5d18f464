/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:HisPatientApi.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */

package com.yuxin.art.his.api;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yuxin.art.his.constant.HisConstants;
import com.yuxin.art.his.utils.ActiveUtil;
import com.yuxin.art.his.utils.HisHttpUtil;
import com.yuxin.art.his.utils.HisResultMock;
import com.yuxin.art.his.vo.HisResultVO;
import com.yuxin.art.his.vo.PatientContentVO;
import com.yuxin.art.his.vo.RequestVO;

/**
 * his病人信息相关接口
 *
 * <AUTHOR>
 * @date 2022/11/8
 */
public class HisPatientApi {

    /**
     * 根据卡类型和卡号查询his病人信息接口
     *
     * @param cardType
     * @param cardNo
     * @param name     卡类型为短卡号时可不传
     * @return
     */
    public static HisResultVO getPatientInfo(String cardType, String cardNo, String name) {
        if ("prod".equals(ActiveUtil.getActive())) {
            return HisHttpUtil.httpRequest(requestBody(cardType, cardNo, name));
        } else {
            return HisResultMock.mock(cardNo, HisConstants.ServiceCode.PATIENT);
        }
    }

    private static String requestBody(String cardType, String cardNo, String name) {
        RequestVO requestVo = new RequestVO();
        requestVo.setToken("");
        requestVo.setCode(HisConstants.ServiceCode.PATIENT);
        PatientContentVO contentVo = new PatientContentVO();
        if (cardType.equals(HisConstants.CARD_TYPE_LANG)) {
            contentVo.setCardNo("");
            contentVo.setXm("");
            contentVo.setKnsj(cardNo);
        } else {
            contentVo.setCardNo(cardNo);
            contentVo.setXm(name);
            contentVo.setKnsj("");
        }
        requestVo.setContent(JSON.toJSONString(Lists.newArrayList(contentVo)));
        return JSON.toJSONString(requestVo);
    }
}
