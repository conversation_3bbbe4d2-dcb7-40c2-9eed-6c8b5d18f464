/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:RequestContentVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.his.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/5/27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "RequestContentVO", description = "请求内容")
public class RequestContentVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 病人标示符
     */
    @JSONField(name = "identify_id")
    private String patientCaseNo;

    /**
     * 开始时间
     */
    private String kssj;

    /**
     * 结束时间
     */
    private String jssj;

    /**
     * 姓名
     */
    private String xm;
}
