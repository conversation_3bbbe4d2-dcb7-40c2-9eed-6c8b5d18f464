/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:PatientVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.his.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/5/23
 */
@Data
@NoArgsConstructor
@ApiModel(value = "PatientVO", description = "患者")
public class PatientVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @JsonProperty(value = "ID")
    private String hisId;
    /**
     * 就诊卡号
     */
    @JsonProperty(value = "CARD_NO")
    private String cardNo;
    /**
     * 病案号
     */
    @JsonProperty(value = "IDENTIFY_ID")
    private String patientCaseNo;
    /**
     * 姓名
     */
    @JsonProperty(value = "NAME")
    private String name;
    /**
     * 病人类型(优先级别) 0：普通 1：优先 2：加急
     */
    @JsonProperty(value = "PRIORITY")
    private Integer priority;
    /**
     * 证件号
     */
    @JsonProperty(value = "ID_CARD")
    private String idCard;
    /**
     * 证件类型：0.身份证、1.护照、2.香港身份证、3.澳门身份证、4.其他
     */
    @JsonProperty(value = "ID_CARD_TYPE")
    private Integer idCardType;
    /**
     * 性别  0：女 1：男
     */
    @JsonProperty(value = "GENDER")
    private Integer gender;
    /**
     * 出生日期
     */
    @JsonProperty(value = "BIRTHDAY")
    // @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy/MM/dd")
    private Date birthday;
    /**
     * 婚否 0:未婚.1:已婚.2:离婚.3：再婚.4：丧偶.5：其他
     */
    @JsonProperty(value = "IS_MARRY")
    private Integer isMarry;
    /**
     * 地区
     */
    @JsonProperty(value = "REGION")
    private String region;
    /**
     * 国家
     */
    @JsonProperty(value = "COUNTRY")
    private String country;
    /**
     * 户口地址
     */
    @JsonProperty(value = "REGISTERED_ADDRESS")
    private String registeredAddress;
    /**
     * 联系地址
     */
    @JsonProperty(value = "CONTACT_ADDRESS")
    private String contactAddress;
    /**
     * 电话
     */
    @JsonProperty(value = "PHONE")
    private String phone;
    /**
     * 民族
     */
    @JsonProperty(value = "NATION")
    private String nation;
    /**
     * 职业
     */
    @JsonProperty(value = "PROFESSION")
    private String profession;
    /**
     * ABO血型 A、B、AB、O
     */
    @JsonProperty(value = "BLOOD_TYPE")
    private String bloodType;
    /**
     * RH血型  RH-、RH+
     */
    @JsonProperty(value = "RH_BLOOD_TYPE")
    private String rhBloodType;
    /**
     * 首次就诊日期
     */
    @JsonProperty(value = "FIRST_VISIT_DAY")
    // @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy/MM/dd")
    private Date firstVisitDay;
    /**
     * 传染病标记  甲肝 乙肝 丙肝 梅毒 其他
     */
    @JsonProperty(value = "INFECTIOUS_DISEASE_MARKER")
    private String infectiousDiseaseMarker;
    /**
     * 末次月经
     */
    @JsonProperty(value = "LAST_MENSTRUATION")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy/MM/dd")
    private Date lastMenstruation;
    /**
     * 备注
     */
    @JsonProperty(value = "REMARK")
    private String remark;

    /**
     * 住院号
     */
    @JsonProperty(value = "ZYH")
    private String inpatientNo;

    /**
     * 床位号
     */
    @JsonProperty(value = "CWH")
    private String bedNo;
}
