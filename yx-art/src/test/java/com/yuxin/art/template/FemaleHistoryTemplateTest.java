/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yuxin-support
 * 模块名称：yuxin-word-spring-boot-starter
 * 文件名称:FemaleHistoryTemplateTest.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */

package com.yuxin.art.template;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.policy.DynamicTableRenderPolicy;
import org.apache.poi.util.LocaleUtil;
import org.apache.poi.xwpf.usermodel.*;
import org.junit.jupiter.api.Test;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 人类辅助生殖女方病历模板测试类
 * <p>
 * 提供女方病历模板的生成和渲染测试功能，包括：
 * <ul>
 *   <li>模板文件生成测试</li>
 *   <li>综合数据渲染测试（基本数据 + 增强检验数据）</li>
 *   <li>动态表格渲染策略（检验表格、卵泡监测表格）</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 */
@SpringBootTest
public class FemaleHistoryTemplateTest {

    @Autowired
    private Configure configure;

    /**
     * 测试生成女方病历模板文件
     */
    @Test
    public void testGenerateTemplate() throws Exception {
        // 设置中文环境
        LocaleUtil.setUserLocale(Locale.SIMPLIFIED_CHINESE);

        // 获取测试资源目录
        URL resourceUrl = getClass().getClassLoader().getResource("");
        if (resourceUrl == null) {
            throw new RuntimeException("无法获取测试资源目录");
        }

        Path resourcesPath;
        try {
            resourcesPath = Paths.get(resourceUrl.toURI());
        } catch (URISyntaxException e) {
            throw new RuntimeException("资源路径转换失败", e);
        }

        // 设置模板文件路径
        Path templatePath = resourcesPath.resolve("FemaleHistory.docx");

        System.out.println("生成模板文件: " + templatePath);

        // 生成模板文件
        FemaleHistoryTemplateGenerator generator = new FemaleHistoryTemplateGenerator();
        generator.generateTemplate(templatePath.toString());

        // 验证文件是否生成成功
        assert Files.exists(templatePath) : "模板文件生成失败";
        assert Files.size(templatePath) > 0 : "模板文件为空";

        System.out.println("模板文件生成成功: " + templatePath);
        System.out.println("文件大小: " + Files.size(templatePath) + " bytes");
    }

    /**
     * 综合测试：模板渲染和增强检验数据
     * <p>
     * 整合了基本模板渲染和增强检验数据渲染的功能，
     * 支持两种数据集的测试，并生成对应的输出文件
     * </p>
     */
    @Test
    public void testComprehensiveTemplateRender() throws Exception {
        System.out.println("=== 开始女方病历综合模板渲染测试 ===");

        // 设置中文环境
        LocaleUtil.setUserLocale(Locale.SIMPLIFIED_CHINESE);

        // 获取测试资源目录
        URL resourceUrl = getClass().getClassLoader().getResource("");
        if (resourceUrl == null) {
            throw new RuntimeException("无法获取测试资源目录");
        }

        Path resourcesPath;
        try {
            resourcesPath = Paths.get(resourceUrl.toURI());
        } catch (URISyntaxException e) {
            throw new RuntimeException("资源路径转换失败", e);
        }

        // 设置模板文件路径
        Path templatePath = resourcesPath.resolve("FemaleHistory.docx");

        // 如果模板文件不存在，先生成
        if (!Files.exists(templatePath)) {
            System.out.println("📝 生成女方病历模板文件...");
            FemaleHistoryTemplateGenerator generator = new FemaleHistoryTemplateGenerator();
            generator.generateTemplate(templatePath.toString());
            System.out.println("✓ 模板文件生成完成: " + templatePath);
        } else {
            System.out.println("📄 使用现有模板文件: " + templatePath);
        }

        // 创建动态表格配置
        ExamineTableRenderPolicy examineTablePolicy = new ExamineTableRenderPolicy();
        UltrasoundMonitorTableRenderPolicy ultrasoundTablePolicy = new UltrasoundMonitorTableRenderPolicy();
        Configure config =
            Configure.builder().bind("examineTargets", examineTablePolicy).bind("ultrasoundMonitorInfos", ultrasoundTablePolicy).build();

        // 综合测试数据渲染
        System.out.println("\n📊 综合测试数据渲染");
        Map<String, Object> comprehensiveData = createComprehensiveTestData();
        renderTemplate(templatePath, resourcesPath.resolve("FemaleHistory_comprehensive_rendered.docx"), comprehensiveData, config);

        // 打印综合数据统计信息
        printComprehensiveDataStats(comprehensiveData);

        System.out.println("\n=== 女方病历综合模板渲染测试完成 ===");
    }

    /**
     * 渲染模板文件
     * <p>
     * 使用指定的数据和配置渲染模板，生成最终的Word文档
     * </p>
     *
     * @param templatePath 模板文件路径
     * @param outputPath   输出文件路径
     * @param data         渲染数据
     * @param config       POI-TL配置
     * @throws Exception 渲染过程中的异常
     */
    private void renderTemplate(Path templatePath, Path outputPath, Map<String, Object> data, Configure config) throws Exception {
        System.out.println("  📝 渲染综合测试数据...");
        System.out.println("    - 模板: " + templatePath.getFileName());
        System.out.println("    - 输出: " + outputPath.getFileName());

        // 编译模板并渲染数据
        try (XWPFTemplate template = XWPFTemplate.compile(templatePath.toString(), config)) {
            template.render(data);
            template.writeToFile(outputPath.toString());
        }

        // 验证文件是否生成成功
        if (!Files.exists(outputPath)) {
            throw new RuntimeException("综合测试数据渲染文件生成失败");
        }
        if (Files.size(outputPath) == 0) {
            throw new RuntimeException("综合测试数据渲染文件为空");
        }

        System.out.println("  ✓ 综合测试数据渲染成功");
        System.out.println("    - 文件大小: " + Files.size(outputPath) + " bytes");
    }

    /**
     * 创建综合测试数据
     * <p>
     * 将基本数据和增强检验数据合并，生成完整的测试数据集
     * </p>
     */
    private Map<String, Object> createComprehensiveTestData() {
        // 从基本数据开始
        Map<String, Object> data = createTestData();

        // 添加增强的检验数据
        Map<String, Object> enhancedData = createEnhancedTestData();

        // 合并检验数据（使用增强的检验数据覆盖基本数据）
        if (enhancedData.containsKey("examineTargets")) {
            data.put("examineTargets", enhancedData.get("examineTargets"));
        }

        // 可以添加其他增强数据字段
        // 例如：如果增强数据中有其他字段，也可以合并进来

        return data;
    }

    /**
     * 打印综合数据统计信息
     */
    private void printComprehensiveDataStats(Map<String, Object> data) {
        System.out.println("\n📈 综合测试数据统计:");

        // 统计检验数据
        @SuppressWarnings("unchecked") List<Map<String, Object>> examineTargets = (List<Map<String, Object>>)data.get("examineTargets");

        if (examineTargets != null && !examineTargets.isEmpty()) {
            System.out.println("  📋 检验项目数量: " + examineTargets.size());

            // 统计每个检验项目的指标数量
            Map<String, Integer> projectStats = new HashMap<>();
            for (Map<String, Object> target : examineTargets) {
                String projectName = (String)target.get("projectName");
                if (projectName != null && !projectName.isEmpty()) {
                    projectStats.put(projectName, projectStats.getOrDefault(projectName, 0) + 1);
                }
            }

            int totalTargets = 0;
            for (Map.Entry<String, Integer> entry : projectStats.entrySet()) {
                System.out.println("    - " + entry.getKey() + ": " + entry.getValue() + " 个指标");
                totalTargets += entry.getValue();
            }
            System.out.println("  📊 检验指标总数: " + totalTargets);
        } else {
            System.out.println("  ⚠️ 没有检验数据");
        }

        // 统计卵泡监测数据
        @SuppressWarnings("unchecked") List<Map<String, Object>> ultrasoundMonitorInfos =
            (List<Map<String, Object>>)data.get("ultrasoundMonitorInfos");

        if (ultrasoundMonitorInfos != null && !ultrasoundMonitorInfos.isEmpty()) {
            System.out.println("  🔍 卵泡监测记录数量: " + ultrasoundMonitorInfos.size());
        }

        // 统计基本信息
        String patientName = (String)data.get("patientFemaleName");
        String patientAge = (String)data.get("femaleAge");
        if (patientName != null || patientAge != null) {
            System.out.println(
                "  👤 患者信息: " + (patientName != null ? patientName : "未知") + " (" + (patientAge != null ? patientAge + "岁" : "年龄未知") + ")");
        }

        System.out.println("  ✅ 数据类型: 基本数据 + 增强检验数据");
    }

    /**
     * 创建增强测试数据
     */
    private Map<String, Object> createEnhancedTestData() {
        Map<String, Object> data = new HashMap<>();

        // 页眉信息
        data.put("cycleNumber", "202500005");
        data.put("femaleAge", "29");
        data.put("femalePatientId", "250717001");
        data.put("maleAge", "32");
        data.put("malePatientId", "250717002");

        // 基本信息
        data.put("patientFemaleName", "张三");
        data.put("femaleBirthday", "1996-12-19");
        data.put("femaleNation", "汉族");
        data.put("femalePhone", "15741253632");
        data.put("femaleBloodType", "B");
        data.put("femaleIdType", "身份证");
        data.put("femaleIdCard", "253651199612192523");

        data.put("patientMaleName", "李四");
        data.put("maleBirthday", "1993-10-26");
        data.put("maleNation", "汉族");
        data.put("malePhone", "15325236523");
        data.put("maleBloodType", "AB");
        data.put("maleIdType", "身份证");
        data.put("maleIdCard", "******************");

        data.put("address", "sdoibjkndstkjbndsknfnlusdfnlhbkjnsdfkjbknks");

        // 使用扁平化的检验数据
        data.put("examineTargets", createFlatExamineTargets());

        // 卵泡监测数据
        data.put("ultrasoundMonitorInfos", createUltrasoundMonitorInfos());

        // 其他数据保持简单
        data.put("physicaRemark", "体格检查正常");
        data.put("unescapeArtChoiceInspectInfos", "辅助检查结果正常");
        data.put("treatmentAdvice", "继续观察，定期复查");
        data.put("doctorSign", "主治医师");
        data.put("mrSign", "住院医师");
        data.put("sysdate", "2024-07-22");

        return data;
    }

    /**
     * 创建测试数据
     */
    private Map<String, Object> createTestData() {
        Map<String, Object> data = new HashMap<>();

        // 页眉信息
        data.put("cycleNumber", "202500005");
        data.put("femaleAge", "29");
        data.put("femalePatientId", "250717001");
        data.put("maleAge", "32");
        data.put("malePatientId", "250717002");

        // 基本信息
        data.put("patientFemaleName", "张三");
        data.put("femaleBirthday", "1990-05-15");
        data.put("femaleNation", "汉族");
        data.put("femalePhone", "13800138000");
        data.put("femaleBloodType", "A型");
        data.put("femaleIdType", "身份证");
        data.put("femaleIdCard", "******************");

        data.put("patientMaleName", "李四");
        data.put("maleBirthday", "1988-03-20");
        data.put("maleNation", "汉族");
        data.put("malePhone", "13900139000");
        data.put("maleBloodType", "B型");
        data.put("maleIdType", "身份证");
        data.put("maleIdCard", "******************");

        data.put("address", "广东省广州市天河区珠江新城XXX路XXX号");

        // 女病史
        data.put("chiefComplaint", "结婚3年未孕，要求辅助生殖技术助孕。");
        data.put("historyPresentIllness", "患者结婚3年，夫妻同居，性生活正常，未避孕未孕。月经规律，无痛经。既往体健，否认手术史。");

        // 月经史
        data.put("mhMenarche", "13");
        data.put("mhMenstrualCycleMin", "28");
        data.put("mhMenstrualCycleMax", "30");
        data.put("mhPeriodlMin", "5");
        data.put("mhPeriodMax", "7");
        data.put("mhLastMenstrualPeriod", "2024-07-01");
        data.put("unescapeMhMenstrualColic", "无");
        data.put("mhMenstrualVolume", "中等量");

        // 个人史
        data.put("phSmoke", "0");
        data.put("phAlcohol", "0");
        data.put("phDrug", "0");
        data.put("phMentalStimulation", "无");
        data.put("phHabitualMedication", "无");
        data.put("phDrugAllergy", "无");
        data.put("phBirthDefect", "无");
        data.put("phJobEnvFactor", "无");

        // 家庭史
        data.put("fhGenetic", "无");
        data.put("fhSimilarDiseases", "无");

        // 既往病史
        data.put("pmhHepatitis", "无");
        data.put("pmhTuberculosis", "无");
        data.put("pmhKidney", "无");
        data.put("pmhCardiovascular", "无");
        data.put("pmhUrinarySystem", "无");
        data.put("pmhSexualRadiation", "无");
        data.put("pmhPelvicInfection", "无");
        data.put("pmhAppendicitis", "无");
        data.put("pmhSurgicalTreatment", "无");
        data.put("pmhThyroidDisease", "无");
        data.put("pmhRheumatismFree", "无");
        data.put("pmhThrombosis", "无");
        data.put("pmhSpecialMedication", "无");
        data.put("pmhRadiotherapy", "无");

        // 婚育史
        data.put("mrhIntermarry", "否");
        data.put("mrhRemarry", "否");
        data.put("mrhRemarryYears", "0");
        data.put("mrhLastPregnancyDate", "无");
        data.put("contraceptionMeasures", "无");
        data.put("mrhPregnantNumber", "0");
        data.put("mrhBiochemicalPregnancy", "0");
        data.put("mrhFertilityNumber", "0");
        data.put("mrhFullTermNumber", "0");
        data.put("mrhPretermBirthNumber", "0");
        data.put("mrhInducedLaborNumber", "0");
        data.put("mrhMedicalAbortionNumber", "0");
        data.put("mrhNaturalAbortionNumber", "0");
        data.put("mrhArtificialAbortionNumber", "0");
        data.put("mrhLeftExfetationNumber", "0");
        data.put("mrhRightExfetationNumber", "0");
        data.put("mrhExistingChildrenNumber", "0");
        data.put("abortion", "0");
        data.put("unescapeHistoryRemark", "无");

        // 体格检查 - 一般检查
        data.put("giHeight", "165");
        data.put("giWeight", "55");
        data.put("giBmi", "20.2");
        data.put("giBreath", "18次/分");
        data.put("giPulse", "72次/分");
        data.put("giTemperature", "36.5℃");
        data.put("giSystolicPressure", "120mmHg");
        data.put("giExpansionPressure", "80mmHg");
        data.put("giWaistline", "70cm");
        data.put("giHipline", "95cm");
        data.put("giWhr", "0.74");

        // 常规检查
        data.put("reNutrition", "良好");
        data.put("reSenses", "清楚");
        data.put("reGrowth", "正常");
        data.put("reHair", "正常");
        data.put("reSkinMucosa", "正常");
        data.put("reSuperficialLymphNodes", "未触及肿大");
        data.put("reBreast", "正常");
        data.put("reGalactorrhea", "无");
        data.put("reHeart", "正常");
        data.put("reLung", "正常");
        data.put("reLiver", "正常");
        data.put("reKidney", "正常");
        data.put("reSmell", "正常");
        data.put("reSpinalLimbs", "正常");
        data.put("reBlackAcanthosis", "无");
        data.put("reAcne", "无");

        // 专科检查
        data.put("seVulva", "正常");
        data.put("seVaginal", "正常");
        data.put("seCervixSurface", "光滑");
        data.put("seNanoCyst", "无");
        data.put("seCervicalHypertrophy", "无");
        data.put("seUterusPosition", "前位");
        data.put("seUterusSize", "正常大小");
        data.put("seUterineTexture", "中等");
        data.put("seUterineActivity", "好");
        data.put("seUterineTenderness", "无");
        data.put("seLeftAnnex", "正常");
        data.put("seRightAnnex", "正常");
        data.put("sePelvicMass", "无");
        data.put("physicaRemark", "无异常");

        // 检验数据（使用扁平化数据）
        data.put("examineTargets", createFlatExamineTargets());

        // 辅助检查
        data.put("unescapeArtChoiceInspectInfos", "B超检查：子宫大小正常，内膜厚度适中，双侧卵巢大小正常。");

        // 卵泡监测数据
        data.put("ultrasoundMonitorInfos", createUltrasoundMonitorInfos());

        // 病史小结
        data.put("unescapeArtSummaryInfos", "患者女性，30岁，结婚3年未孕，月经规律，体格检查无异常，建议进一步检查明确不孕原因。");

        // 诊断
        data.put("unescapeArtDiagnos", "原发性不孕症");

        // 指征
        data.put("femaleIndicationItemStr", "输卵管因素不孕");

        // 治疗方案
        data.put("unescapeArtTreatmentPlanInfo", "建议行体外受精-胚胎移植（IVF-ET）治疗。");

        // 治疗建议
        data.put("unescapeArtTreatmentRecommendationInfo", "1. 完善相关检查；2. 制定个体化促排卵方案；3. 定期监测卵泡发育。");

        // 签名和日期
        data.put("doctorSign", "张医生");
        data.put("mrSign", "李护士");
        data.put("sysdate", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        return data;
    }

    /**
     * 创建增强的检验数据示例
     * <p>
     * 包含多个检验项目，每个项目包含多个检验指标：
     * <ul>
     *   <li>血常规检查：白细胞计数、红细胞计数、血红蛋白</li>
     *   <li>生化检验：血糖、肝功能、肾功能</li>
     *   <li>激素检验：FSH、LH、E2</li>
     * </ul>
     * </p>
     */
    private List<Map<String, Object>> createEnhancedExamineTargets() {
        List<Map<String, Object>> examineTargets = new ArrayList<>();

        // 1. 血常规检查项目
        Map<String, Object> bloodTest = new HashMap<>();
        bloodTest.put("itemName", "血常规");
        bloodTest.put("examineDate", "2024-01-15");
        bloodTest.put("examineDoctor", "李医生");

        List<Map<String, Object>> bloodTargets = new ArrayList<>();

        // 白细胞计数
        Map<String, Object> wbc = new HashMap<>();
        wbc.put("targetName", "白细胞计数(WBC)");
        wbc.put("result", "6.5");
        wbc.put("unit", "×10^9/L");
        wbc.put("abnormal", "正常");
        wbc.put("normalReferenceValue", "3.5-9.5");
        wbc.put("minReferenceValue", "3.5");
        wbc.put("maxReferenceValue", "9.5");
        bloodTargets.add(wbc);

        // 红细胞计数
        Map<String, Object> rbc = new HashMap<>();
        rbc.put("targetName", "红细胞计数(RBC)");
        rbc.put("result", "4.2");
        rbc.put("unit", "×10^12/L");
        rbc.put("abnormal", "正常");
        rbc.put("normalReferenceValue", "3.8-5.1");
        rbc.put("minReferenceValue", "3.8");
        rbc.put("maxReferenceValue", "5.1");
        bloodTargets.add(rbc);

        // 血红蛋白
        Map<String, Object> hgb = new HashMap<>();
        hgb.put("targetName", "血红蛋白(HGB)");
        hgb.put("result", "125");
        hgb.put("unit", "g/L");
        hgb.put("abnormal", "正常");
        hgb.put("normalReferenceValue", "115-150");
        hgb.put("minReferenceValue", "115");
        hgb.put("maxReferenceValue", "150");
        bloodTargets.add(hgb);

        // 血小板计数
        Map<String, Object> plt = new HashMap<>();
        plt.put("targetName", "血小板计数(PLT)");
        plt.put("result", "280");
        plt.put("unit", "×10^9/L");
        plt.put("abnormal", "正常");
        plt.put("normalReferenceValue", "125-350");
        plt.put("minReferenceValue", "125");
        plt.put("maxReferenceValue", "350");
        bloodTargets.add(plt);

        // 中性粒细胞百分比
        Map<String, Object> neut = new HashMap<>();
        neut.put("targetName", "中性粒细胞百分比");
        neut.put("result", "65.2");
        neut.put("unit", "%");
        neut.put("abnormal", "正常");
        neut.put("normalReferenceValue", "50-70");
        neut.put("minReferenceValue", "50");
        neut.put("maxReferenceValue", "70");
        bloodTargets.add(neut);

        bloodTest.put("targets", bloodTargets);
        examineTargets.add(bloodTest);

        // 2. 生化检查项目
        Map<String, Object> biochemTest = new HashMap<>();
        biochemTest.put("itemName", "生化检查");
        biochemTest.put("examineDate", "2024-01-16");
        biochemTest.put("examineDoctor", "王医生");

        List<Map<String, Object>> biochemTargets = new ArrayList<>();

        // 血糖
        Map<String, Object> glucose = new HashMap<>();
        glucose.put("targetName", "空腹血糖(GLU)");
        glucose.put("result", "5.2");
        glucose.put("unit", "mmol/L");
        glucose.put("abnormal", "正常");
        glucose.put("normalReferenceValue", "3.9-6.1");
        glucose.put("minReferenceValue", "3.9");
        glucose.put("maxReferenceValue", "6.1");
        biochemTargets.add(glucose);

        // 总胆固醇
        Map<String, Object> chol = new HashMap<>();
        chol.put("targetName", "总胆固醇(TC)");
        chol.put("result", "4.8");
        chol.put("unit", "mmol/L");
        chol.put("abnormal", "正常");
        chol.put("normalReferenceValue", "3.1-5.7");
        chol.put("minReferenceValue", "3.1");
        chol.put("maxReferenceValue", "5.7");
        biochemTargets.add(chol);

        // 甘油三酯
        Map<String, Object> tg = new HashMap<>();
        tg.put("targetName", "甘油三酯(TG)");
        tg.put("result", "1.2");
        tg.put("unit", "mmol/L");
        tg.put("abnormal", "正常");
        tg.put("normalReferenceValue", "0.45-1.7");
        tg.put("minReferenceValue", "0.45");
        tg.put("maxReferenceValue", "1.7");
        biochemTargets.add(tg);

        // 肝功能-ALT
        Map<String, Object> alt = new HashMap<>();
        alt.put("targetName", "丙氨酸氨基转移酶(ALT)");
        alt.put("result", "28");
        alt.put("unit", "U/L");
        alt.put("abnormal", "正常");
        alt.put("normalReferenceValue", "7-40");
        alt.put("minReferenceValue", "7");
        alt.put("maxReferenceValue", "40");
        biochemTargets.add(alt);

        biochemTest.put("targets", biochemTargets);
        examineTargets.add(biochemTest);

        // 3. 激素检查项目
        Map<String, Object> hormoneTest = new HashMap<>();
        hormoneTest.put("itemName", "性激素六项");
        hormoneTest.put("examineDate", "2024-01-17");
        hormoneTest.put("examineDoctor", "张医生");

        List<Map<String, Object>> hormoneTargets = new ArrayList<>();

        // FSH
        Map<String, Object> fsh = new HashMap<>();
        fsh.put("targetName", "卵泡刺激素(FSH)");
        fsh.put("result", "6.8");
        fsh.put("unit", "mIU/mL");
        fsh.put("abnormal", "正常");
        fsh.put("normalReferenceValue", "3.5-12.5");
        fsh.put("minReferenceValue", "3.5");
        fsh.put("maxReferenceValue", "12.5");
        hormoneTargets.add(fsh);

        // LH
        Map<String, Object> lh = new HashMap<>();
        lh.put("targetName", "黄体生成素(LH)");
        lh.put("result", "4.2");
        lh.put("unit", "mIU/mL");
        lh.put("abnormal", "正常");
        lh.put("normalReferenceValue", "2.4-12.6");
        lh.put("minReferenceValue", "2.4");
        lh.put("maxReferenceValue", "12.6");
        hormoneTargets.add(lh);

        // E2
        Map<String, Object> e2 = new HashMap<>();
        e2.put("targetName", "雌二醇(E2)");
        e2.put("result", "45.8");
        e2.put("unit", "pg/mL");
        e2.put("abnormal", "正常");
        e2.put("normalReferenceValue", "12.5-166");
        e2.put("minReferenceValue", "12.5");
        e2.put("maxReferenceValue", "166");
        hormoneTargets.add(e2);

        // P
        Map<String, Object> p = new HashMap<>();
        p.put("targetName", "孕酮(P)");
        p.put("result", "0.8");
        p.put("unit", "ng/mL");
        p.put("abnormal", "正常");
        p.put("normalReferenceValue", "0.2-1.5");
        p.put("minReferenceValue", "0.2");
        p.put("maxReferenceValue", "1.5");
        hormoneTargets.add(p);

        // T
        Map<String, Object> t = new HashMap<>();
        t.put("targetName", "睾酮(T)");
        t.put("result", "0.35");
        t.put("unit", "ng/mL");
        t.put("abnormal", "正常");
        t.put("normalReferenceValue", "0.14-0.76");
        t.put("minReferenceValue", "0.14");
        t.put("maxReferenceValue", "0.76");
        hormoneTargets.add(t);

        // PRL
        Map<String, Object> prl = new HashMap<>();
        prl.put("targetName", "催乳素(PRL)");
        prl.put("result", "18.5");
        prl.put("unit", "ng/mL");
        prl.put("abnormal", "正常");
        prl.put("normalReferenceValue", "4.8-23.3");
        prl.put("minReferenceValue", "4.8");
        prl.put("maxReferenceValue", "23.3");
        hormoneTargets.add(prl);

        hormoneTest.put("targets", hormoneTargets);
        examineTargets.add(hormoneTest);

        return examineTargets;
    }

    /**
     * 卵泡监测表格动态渲染策略
     * <p>
     * 专门处理卵泡监测数据的动态表格渲染，支持：
     * <ul>
     *   <li>监测日期、监测结果的行渲染</li>
     *   <li>自动表格结构创建</li>
     *   <li>监测数据的格式化显示</li>
     * </ul>
     * </p>
     */
    public static class UltrasoundMonitorTableRenderPolicy extends DynamicTableRenderPolicy {
        @Override
        public void render(XWPFTable table, Object data) throws Exception {
            System.out.println("开始渲染卵泡监测表格，数据: " + data);

            if (!(data instanceof List)) {
                System.out.println("卵泡监测数据不是List类型，跳过渲染");
                return;
            }

            @SuppressWarnings("unchecked") List<Map<String, Object>> monitorInfos = (List<Map<String, Object>>)data;
            System.out.println("卵泡监测数据条数: " + monitorInfos.size());

            // 检查并确保有正确的标题行结构
            ensureHeaderRow(table);

            // 清除数据行（保留两行标题：卵泡监测标题行和列标题行）
            while (table.getRows().size() > 2) {
                table.removeRow(2);
            }

            System.out.println("表格初始行数: " + table.getRows().size());

            // 渲染数据行
            for (Map<String, Object> monitorInfo : monitorInfos) {
                XWPFTableRow row = table.createRow();

                // 确保有7个单元格
                while (row.getTableCells().size() < 7) {
                    row.createCell();
                }

                // 填充数据
                setCellText(row.getCell(0), getString(monitorInfo, "monitorTime"));
                setCellText(row.getCell(1), getString(monitorInfo, "cycleDay"));
                setCellText(row.getCell(2), getString(monitorInfo, "leftOvary"));
                setCellText(row.getCell(3), getString(monitorInfo, "rightOvary"));
                setCellText(row.getCell(4), getString(monitorInfo, "num"));
                setCellText(row.getCell(5), getString(monitorInfo, "intimaType"));
                setCellText(row.getCell(6), getString(monitorInfo, "monitorName"));

                // 设置数据单元格样式
                for (int i = 0; i < 7; i++) {
                    setDataCellStyle(row.getCell(i));
                }

                // 设置行高与专科检查表格一致
                row.setHeight(400); // 400 twips ≈ 7mm
            }

            System.out.println("卵泡监测表格渲染完成，最终行数: " + table.getRows().size());
        }

        /**
         * 确保表格有正确的标题行结构
         */
        private void ensureHeaderRow(XWPFTable table) {
            if (table.getRows().isEmpty()) {
                // 没有行，创建完整的标题结构
                createTitleRow(table);
                createHeaderRow(table);
            } else if (table.getRows().size() == 1) {
                // 只有一行，检查是否是"卵泡监测"标题行
                XWPFTableRow firstRow = table.getRow(0);
                String firstCellText = getCellText(firstRow.getCell(0));
                if ("卵泡监测".equals(firstCellText)) {
                    // 是"卵泡监测"标题行，添加列标题行
                    createHeaderRow(table);
                } else {
                    // 不是，重新创建完整结构
                    table.removeRow(0);
                    createTitleRow(table);
                    createHeaderRow(table);
                }
            } else if (table.getRows().size() >= 2) {
                // 检查前两行的结构
                XWPFTableRow firstRow = table.getRow(0);
                XWPFTableRow secondRow = table.getRow(1);
                String firstCellText = getCellText(firstRow.getCell(0));
                String secondCellText = getCellText(secondRow.getCell(0));

                if (!"卵泡监测".equals(firstCellText) || !"监测日期".equals(secondCellText)) {
                    // 结构不正确，重新创建
                    while (!table.getRows().isEmpty()) {
                        table.removeRow(0);
                    }
                    createTitleRow(table);
                    createHeaderRow(table);
                }
            }
        }

        /**
         * 创建"卵泡监测"标题行
         */
        private void createTitleRow(XWPFTable table) {
            XWPFTableRow titleRow = table.createRow();

            // 确保有7个单元格
            while (titleRow.getTableCells().size() < 7) {
                titleRow.createCell();
            }

            // 设置"卵泡监测"标题
            setCellText(titleRow.getCell(0), "卵泡监测");

            // 合并所有单元格
            for (int i = 1; i < 7; i++) {
                setCellText(titleRow.getCell(i), "");
            }

            // 设置标题行样式
            setHeaderCellStyle(titleRow.getCell(0));

            // 设置行高
            titleRow.setHeight(400); // 400 twips ≈ 7mm
        }

        /**
         * 创建列标题行
         */
        private void createHeaderRow(XWPFTable table) {
            XWPFTableRow headerRow = table.createRow();

            // 确保有7个单元格
            while (headerRow.getTableCells().size() < 7) {
                headerRow.createCell();
            }

            // 设置列标题
            setCellText(headerRow.getCell(0), "监测日期");
            setCellText(headerRow.getCell(1), "周期日");
            setCellText(headerRow.getCell(2), "左卵巢");
            setCellText(headerRow.getCell(3), "右卵巢");
            setCellText(headerRow.getCell(4), "卵泡数");
            setCellText(headerRow.getCell(5), "内膜");
            setCellText(headerRow.getCell(6), "监测医师");

            // 设置标题单元格样式
            for (int i = 0; i < 7; i++) {
                setHeaderCellStyle(headerRow.getCell(i));
            }

            // 设置行高与专科检查表格一致
            headerRow.setHeight(400); // 400 twips ≈ 7mm
        }

        /**
         * 设置标题单元格样式
         */
        private void setHeaderCellStyle(XWPFTableCell cell) {
            // 设置段落居中对齐
            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                paragraph.setAlignment(ParagraphAlignment.CENTER);
                for (XWPFRun run : paragraph.getRuns()) {
                    run.setBold(true);
                }
            }
            // 设置单元格垂直居中对齐
            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        }

        /**
         * 设置数据单元格样式
         */
        private void setDataCellStyle(XWPFTableCell cell) {
            // 设置段落左对齐
            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                paragraph.setAlignment(ParagraphAlignment.LEFT);
            }
            // 设置单元格垂直居中对齐
            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        }

        /**
         * 设置单元格文本
         */
        private void setCellText(XWPFTableCell cell, String text) {
            if (cell.getParagraphs().isEmpty()) {
                cell.addParagraph();
            }
            XWPFParagraph paragraph = cell.getParagraphs().get(0);
            if (paragraph.getRuns().isEmpty()) {
                paragraph.createRun();
            }
            XWPFRun run = paragraph.getRuns().get(0);
            run.setText(text != null ? text : "", 0);
        }

        /**
         * 获取单元格文本
         */
        private String getCellText(XWPFTableCell cell) {
            if (cell == null || cell.getParagraphs().isEmpty()) {
                return "";
            }
            return cell.getText().trim();
        }

        /**
         * 从Map中安全获取字符串值
         */
        private String getString(Map<String, Object> map, String key) {
            Object value = map.get(key);
            return value != null ? value.toString() : "";
        }
    }

    /**
     * 创建扁平化的检验数据
     * <p>
     * 按照检验项目分组，每个项目包含多个指标，用于动态表格渲染。
     * 数据结构适配ExamineTableRenderPolicy的渲染需求。
     * </p>
     */
    private List<Map<String, Object>> createFlatExamineTargets() {
        List<Map<String, Object>> flatTargets = new ArrayList<>();

        // 血常规检验项目
        Map<String, Object> target1 = new HashMap<>();
        target1.put("projectName", "血常规");
        target1.put("targetName", "白细胞计数");
        target1.put("result", "6.2");
        target1.put("unit", "×10^9/L");
        target1.put("abnormal", "正常");
        target1.put("normalReferenceValue", "3.5-9.5");
        target1.put("minReferenceValue", "3.5");
        target1.put("maxReferenceValue", "9.5");
        flatTargets.add(target1);

        Map<String, Object> target2 = new HashMap<>();
        target2.put("projectName", "");  // 同一项目，不显示项目名
        target2.put("targetName", "红细胞计数");
        target2.put("result", "4.5");
        target2.put("unit", "×10^12/L");
        target2.put("abnormal", "正常");
        target2.put("normalReferenceValue", "3.8-5.1");
        target2.put("minReferenceValue", "3.8");
        target2.put("maxReferenceValue", "5.1");
        flatTargets.add(target2);

        Map<String, Object> target3 = new HashMap<>();
        target3.put("projectName", "");  // 同一项目，不显示项目名
        target3.put("targetName", "血红蛋白");
        target3.put("result", "125");
        target3.put("unit", "g/L");
        target3.put("abnormal", "正常");
        target3.put("normalReferenceValue", "115-150");
        target3.put("minReferenceValue", "115");
        target3.put("maxReferenceValue", "150");
        flatTargets.add(target3);

        // 生化检验项目
        Map<String, Object> target4 = new HashMap<>();
        target4.put("projectName", "生化检验");
        target4.put("targetName", "血糖");
        target4.put("result", "5.2");
        target4.put("unit", "mmol/L");
        target4.put("abnormal", "正常");
        target4.put("normalReferenceValue", "3.9-6.1");
        target4.put("minReferenceValue", "3.9");
        target4.put("maxReferenceValue", "6.1");
        flatTargets.add(target4);

        Map<String, Object> target5 = new HashMap<>();
        target5.put("projectName", "");  // 同一项目，不显示项目名
        target5.put("targetName", "肌酐");
        target5.put("result", "65");
        target5.put("unit", "μmol/L");
        target5.put("abnormal", "正常");
        target5.put("normalReferenceValue", "45-84");
        target5.put("minReferenceValue", "45");
        target5.put("maxReferenceValue", "84");
        flatTargets.add(target5);

        // 激素检验项目
        Map<String, Object> target6 = new HashMap<>();
        target6.put("projectName", "激素检验");
        target6.put("targetName", "FSH");
        target6.put("result", "6.8");
        target6.put("unit", "mIU/mL");
        target6.put("abnormal", "正常");
        target6.put("normalReferenceValue", "3.5-12.5");
        target6.put("minReferenceValue", "3.5");
        target6.put("maxReferenceValue", "12.5");
        flatTargets.add(target6);

        Map<String, Object> target7 = new HashMap<>();
        target7.put("projectName", "");  // 同一项目，不显示项目名
        target7.put("targetName", "LH");
        target7.put("result", "4.2");
        target7.put("unit", "mIU/mL");
        target7.put("abnormal", "正常");
        target7.put("normalReferenceValue", "2.4-12.6");
        target7.put("minReferenceValue", "2.4");
        target7.put("maxReferenceValue", "12.6");
        flatTargets.add(target7);

        return flatTargets;
    }

    /**
     * 创建卵泡监测数据示例
     */
    private List<Map<String, Object>> createUltrasoundMonitorInfos() {
        List<Map<String, Object>> monitorInfos = new ArrayList<>();

        Map<String, Object> info1 = new HashMap<>();
        info1.put("monitorTime", "2024-07-15");
        info1.put("cycleDay", "第12天");
        info1.put("leftOvary", "15×12mm");
        info1.put("rightOvary", "16×13mm");
        info1.put("num", "2");
        info1.put("intimaType", "A型");
        info1.put("monitorName", "王医生");
        monitorInfos.add(info1);

        Map<String, Object> info2 = new HashMap<>();
        info2.put("monitorTime", "2024-07-17");
        info2.put("cycleDay", "第14天");
        info2.put("leftOvary", "18×15mm");
        info2.put("rightOvary", "19×16mm");
        info2.put("num", "2");
        info2.put("intimaType", "A型");
        info2.put("monitorName", "王医生");
        monitorInfos.add(info2);

        return monitorInfos;
    }

    /**
     * 检验表格动态渲染策略
     * <p>
     * 使用DynamicTableRenderPolicy来完全控制表格渲染，支持：
     * <ul>
     *   <li>项目名称合并单元格显示</li>
     *   <li>多个检验指标的行渲染</li>
     *   <li>异常结果的特殊标记</li>
     *   <li>自动表格结构创建和数据填充</li>
     * </ul>
     * </p>
     */
    public static class ExamineTableRenderPolicy extends DynamicTableRenderPolicy {

        @Override
        public void render(XWPFTable table, Object data) throws Exception {
            if (data == null) {
                return;
            }

            @SuppressWarnings("unchecked") List<Map<String, Object>> examineTargets = (List<Map<String, Object>>)data;

            System.out.println("开始渲染检验表格，数据条数: " + examineTargets.size());
            System.out.println("表格初始行数: " + table.getRows().size());

            // 检查并确保有正确的标题行
            ensureHeaderRow(table);

            // 清除数据行（保留两行标题：检验标题行和列标题行）
            while (table.getRows().size() > 2) {
                table.removeRow(2);
            }

            // 按检验项目分组数据
            Map<String, List<Map<String, Object>>> groupedData = groupByProject(examineTargets);

            // 渲染每个检验项目
            for (Map.Entry<String, List<Map<String, Object>>> entry : groupedData.entrySet()) {
                String projectName = entry.getKey();
                List<Map<String, Object>> projectTargets = entry.getValue();

                System.out.println("渲染检验项目: " + projectName + ", 指标数量: " + projectTargets.size());

                renderProjectData(table, projectName, projectTargets);
            }

            System.out.println("检验表格渲染完成，最终行数: " + table.getRows().size());
        }

        /**
         * 确保表格有正确的标题行结构
         */
        private void ensureHeaderRow(XWPFTable table) {
            if (table.getRows().isEmpty()) {
                // 没有行，创建完整的标题结构
                createTitleRow(table);
                createHeaderRow(table);
            } else if (table.getRows().size() == 1) {
                // 只有一行，检查是否是"检验"标题行
                XWPFTableRow firstRow = table.getRow(0);
                String firstCellText = getCellText(firstRow.getCell(0));
                if ("检验".equals(firstCellText)) {
                    // 是"检验"标题行，添加列标题行
                    createHeaderRow(table);
                } else {
                    // 不是，重新创建完整结构
                    table.removeRow(0);
                    createTitleRow(table);
                    createHeaderRow(table);
                }
            } else if (table.getRows().size() >= 2) {
                // 检查前两行的结构
                XWPFTableRow firstRow = table.getRow(0);
                XWPFTableRow secondRow = table.getRow(1);
                String firstCellText = getCellText(firstRow.getCell(0));
                String secondCellText = getCellText(secondRow.getCell(0));

                if (!"检验".equals(firstCellText) || !"检验项目".equals(secondCellText)) {
                    // 结构不正确，重新创建
                    while (!table.getRows().isEmpty()) {
                        table.removeRow(0);
                    }
                    createTitleRow(table);
                    createHeaderRow(table);
                }
            }
        }

        /**
         * 创建"检验"标题行
         */
        private void createTitleRow(XWPFTable table) {
            XWPFTableRow titleRow = table.createRow();

            // 确保有8个单元格
            while (titleRow.getTableCells().size() < 8) {
                titleRow.createCell();
            }

            // 设置"检验"标题
            setCellText(titleRow.getCell(0), "检验");

            // 合并所有单元格
            for (int i = 1; i < 8; i++) {
                setCellText(titleRow.getCell(i), "");
            }

            // 设置标题行样式
            setHeaderCellStyle(titleRow.getCell(0));

            // 设置行高
            titleRow.setHeight(400); // 400 twips ≈ 7mm
        }

        /**
         * 创建列标题行
         */
        private void createHeaderRow(XWPFTable table) {
            XWPFTableRow headerRow = table.createRow();

            // 确保有8个单元格
            while (headerRow.getTableCells().size() < 8) {
                headerRow.createCell();
            }

            // 设置标题内容
            setCellText(headerRow.getCell(0), "检验项目");
            setCellText(headerRow.getCell(1), "检验指标");
            setCellText(headerRow.getCell(2), "结果");
            setCellText(headerRow.getCell(3), "单位");
            setCellText(headerRow.getCell(4), "提示");
            setCellText(headerRow.getCell(5), "正常参考值");
            setCellText(headerRow.getCell(6), "最低参考值");
            setCellText(headerRow.getCell(7), "最高参考值");

            // 设置标题行样式（加粗、居中）
            for (int i = 0; i < 8; i++) {
                XWPFTableCell cell = headerRow.getCell(i);
                setHeaderCellStyle(cell);
            }

            // 设置行高 - 与专科检查表格保持一致
            headerRow.setHeight(400); // 400 twips ≈ 7mm
        }

        /**
         * 设置标题单元格样式
         */
        private void setHeaderCellStyle(XWPFTableCell cell) {
            // 设置段落居中对齐
            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                paragraph.setAlignment(ParagraphAlignment.CENTER);
                for (XWPFRun run : paragraph.getRuns()) {
                    run.setBold(true);
                }
            }
            // 设置单元格垂直居中对齐
            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        }

        /**
         * 按检验项目分组数据
         */
        private Map<String, List<Map<String, Object>>> groupByProject(List<Map<String, Object>> examineTargets) {
            Map<String, List<Map<String, Object>>> grouped = new LinkedHashMap<>();

            for (Map<String, Object> target : examineTargets) {
                String projectName = (String)target.get("projectName");
                if (projectName == null || projectName.trim().isEmpty()) {
                    // 如果项目名为空，归到上一个项目
                    if (!grouped.isEmpty()) {
                        String lastProject = null;
                        for (String key : grouped.keySet()) {
                            lastProject = key;
                        }
                        if (lastProject != null) {
                            grouped.get(lastProject).add(target);
                        }
                    }
                } else {
                    grouped.computeIfAbsent(projectName, k -> new ArrayList<>()).add(target);
                }
            }

            return grouped;
        }

        /**
         * 渲染单个检验项目的数据
         */
        private void renderProjectData(XWPFTable table, String projectName, List<Map<String, Object>> targets) {
            int startRowIndex = table.getRows().size();

            // 为每个指标创建一行
            for (int i = 0; i < targets.size(); i++) {
                Map<String, Object> target = targets.get(i);

                // 创建新行
                XWPFTableRow row = table.createRow();

                // 确保行有足够的单元格
                while (row.getTableCells().size() < 8) {
                    row.createCell();
                }

                // 填充数据
                if (i == 0) {
                    // 第一行显示项目名称
                    setCellText(row.getCell(0), projectName);
                } else {
                    // 其他行项目名称为空（稍后会被合并）
                    setCellText(row.getCell(0), "");
                }

                setCellText(row.getCell(1), (String)target.get("targetName"));
                setCellText(row.getCell(2), (String)target.get("result"));
                setCellText(row.getCell(3), (String)target.get("unit"));
                setCellText(row.getCell(4), (String)target.get("abnormal"));
                setCellText(row.getCell(5), (String)target.get("normalReferenceValue"));
                setCellText(row.getCell(6), (String)target.get("minReferenceValue"));
                setCellText(row.getCell(7), (String)target.get("maxReferenceValue"));

                // 设置行高与专科检查表格一致
                row.setHeight(400); // 400 twips ≈ 7mm

                // 设置数据行单元格样式（左对齐）
                for (int j = 0; j < 8; j++) {
                    setDataCellStyle(row.getCell(j));
                }
            }

            // 合并项目名称列的单元格（当同一项目有多个指标时）
            if (targets.size() > 1) {
                int endRowIndex = startRowIndex + targets.size() - 1;
                mergeProjectNameCells(table, startRowIndex, endRowIndex);
            }
        }

        /**
         * 设置单元格文本
         */
        private void setCellText(XWPFTableCell cell, String text) {
            if (text == null) {
                text = "";
            }

            // 清除现有内容
            cell.removeParagraph(0);

            // 添加新段落和文本
            XWPFParagraph paragraph = cell.addParagraph();
            XWPFRun run = paragraph.createRun();
            run.setText(text);
        }

        /**
         * 获取单元格文本内容
         */
        private String getCellText(XWPFTableCell cell) {
            if (cell == null) {
                return "";
            }
            return cell.getText().trim();
        }

        /**
         * 设置数据单元格样式
         */
        private void setDataCellStyle(XWPFTableCell cell) {
            // 设置段落左对齐
            for (XWPFParagraph paragraph : cell.getParagraphs()) {
                paragraph.setAlignment(ParagraphAlignment.LEFT);
            }
            // 设置单元格垂直居中对齐
            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        }

        /**
         * 合并项目名称列的单元格
         * <p>
         * 当同一个检验项目包含多个指标时，将项目名称列的单元格垂直合并，
         * 使项目名称只在第一行显示，提高表格的可读性。
         * </p>
         *
         * @param table   表格对象
         * @param fromRow 合并开始行索引
         * @param toRow   合并结束行索引
         */
        private void mergeProjectNameCells(XWPFTable table, int fromRow, int toRow) {
            final int PROJECT_NAME_COLUMN = 0; // 项目名称列固定为第0列

            try {
                for (int rowIndex = fromRow; rowIndex <= toRow; rowIndex++) {
                    XWPFTableCell cell = table.getRow(rowIndex).getCell(PROJECT_NAME_COLUMN);
                    if (rowIndex == fromRow) {
                        // 第一个单元格设置为合并开始
                        cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
                    } else {
                        // 其他单元格设置为合并继续
                        cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
                    }
                }
                System.out.println("合并单元格: 列" + PROJECT_NAME_COLUMN + ", 行" + fromRow + "-" + toRow);
            } catch (Exception e) {
                System.err.println("合并项目名称单元格时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }
}
