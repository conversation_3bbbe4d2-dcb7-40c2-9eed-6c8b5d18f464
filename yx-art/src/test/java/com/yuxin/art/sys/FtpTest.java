/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:FtpTest.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.sys;

//import net.sourceforge.pinyin4j.PinyinHelper;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Ftp测试
 *
 * <AUTHOR>
 * @date 2020-05-23
 **/
@SpringBootTest
@Slf4j
public class FtpTest {
    @Test
    public void init() {
        // FTPStorageService ftpStorageService = (FTPStorageService)StorageFactory.build();
        // File file = new File("D:\\a.txt");
        // ftpStorageService.upload(file);
        System.out.println(getPinYinHeadChar("卵巢test衰竭"));
    }

    public static String getPinYinHeadChar(String cityName) {
        String convert = "";
        for (int j = 0; j < cityName.length(); j++) {
            char word = cityName.charAt(j);
            if ((word >= 'a' && word <= 'z') || (word >= 'A' && word <= 'Z')) {
                convert += word;
            } else {
                String[] pinyinArray = null;
                if (pinyinArray != null) {
                    convert += pinyinArray[0].charAt(0);
                }
            }
        }
        return convert;
    }

    /*public static String parseFile(File docFile) {
        String fileName = docFile.getName();
        String docContent = null;
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(docFile);
            if (fileName.toLowerCase().endsWith(doc)) {
                HWPFDocument doc = new HWPFDocument(fis);
                docContent = doc.getDocumentText();
            } else if (fileName.toLowerCase().endsWith(docx)) {
                XWPFDocument xdoc = new XWPFDocument(fis);
                XWPFWordExtractor extractor = new XWPFWordExtractor(xdoc);
                docContent = extractor.getText();
            } else {
                log.info("文件名为：" + fileName + " 格式有误");
            }
        } catch (Exception e) {
            log.info("解析文件名为" + fileName + "发生异常：", e);
        }
        return docContent;// 返回文本内容
    }*/
}
