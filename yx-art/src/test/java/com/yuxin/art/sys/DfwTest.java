/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:DfwTest.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.sys;

import cn.hutool.core.date.DateUtil;
import com.yuxin.art.constant.KArtConstants;
import com.yuxin.art.constant.KArtFrozenThawConstants;
import com.yuxin.art.constant.KArtObConstants;
import com.yuxin.art.modules.art.embryo.mapper.ArtEmbryoMapper;
import com.yuxin.art.modules.art.embryoobservation.service.ArtEmbryoObService;
import com.yuxin.art.modules.fz.service.FtThawEmbryoService;
import com.yuxin.art.modules.upcoming.service.CommonTodoService;
import com.yuxin.json.util.JsonUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020-09-03
 **/
@SpringBootTest
@Slf4j
public class DfwTest {
    @Resource
    private StringRedisTemplate strRedis;
    @Autowired
    private CommonTodoService commonTodoService;
    @Autowired
    private ArtEmbryoObService obService;
    @Autowired
    private FtThawEmbryoService ftThawEmbryoService;
    @Autowired
    private ArtEmbryoMapper artEmbryoMapper;

    public static void main(String[] args) {
        // 场景，循环查询数据
        // for (int i = 0; i < 100000; i++) {
        // Param param = new Param();
        // param.setP1(i);
        // param.setP2(i + "");
        // // 做查询操作
        // }
        //
        // // Param就是参数
        // Param param = new Param();
        // for (int i = 0; i < 100000; i++) {
        // param.setP1(i);
        // param.setP2(i + "");
        // // 做查询操作
        // }
    }

    @Data
    static class Param {
        // 参数1
        private Integer p1;
        // 参数2
        private String p2;
    }

    // @Test
    public void printConstants() {
        // 只能拿到注解，获取不到注释
        Class<?>[] clazz = new Class[] {KArtConstants.class, KArtFrozenThawConstants.class, KArtObConstants.class};
        for (Class<?> clz : clazz) {
            System.out.println("当前类：" + clz.getName());
            Class<?>[] innerClazz = clz.getDeclaredClasses();
            for (Class<?> innerClz : innerClazz) {
                System.out.println(innerClz.getSimpleName());
            }

        }
    }

    @Test
    public void testStatTodo() {
        log.error(JsonUtil.toJson(commonTodoService.doStat(DateUtil.parseDate("2020-09-11"), 0)));
        log.error(JsonUtil.toJson(commonTodoService.doStat(DateUtil.parseDate("2020-09-11"), 1)));
    }

    @Test
    public void testBatchCancel() {
        // commonTodoService.batchCancel(1331074494470688768L);
    }

    @Test
    public void testGetD3Score() {
        // log.error("早胚评分：{}", obService.getMidScore(1380053031600656384L));

        log.error("数据：{}", ftThawEmbryoService.getWaitThawEmbryoListByPairBondId(1380588406530248704L));
    }

    @Test
    public void testCoculture() {
        // final List<ArtEmbryo> coculture = artEmbryoMapper.getCoculture(1359658003384635392L);
        // log.info(JSON.toJSONString(coculture, true));

        // final List<EmbryoCocultureVO> cocultureInfo =
        // obService.getCocultureInfo(ListUtil.of(1359658003384635392L, 1359658003502075904L, 1359658003711791104L));
        // log.info(JsonUtil.toJson(cocultureInfo));
    }
}
