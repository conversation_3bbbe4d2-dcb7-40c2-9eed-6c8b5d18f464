/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称：yx-art
 * 文件名称:DataChangeTraceServiceTest.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.trace;

import com.google.common.collect.Maps;
import com.yuxin.art.modules.trace.dto.DataChangeTraceDTO;
import com.yuxin.art.modules.trace.enums.BizModuleEnum;
import com.yuxin.art.modules.trace.service.DataChangeTraceService;
import com.yuxin.art.trace.constant.TraceQueueConstants;
import com.yuxin.mq.util.RabbitUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据变更留痕服务测试
 * <p>
 * 测试数据变更留痕服务的完整功能，包括：
 * 1. 数据比较逻辑测试
 * 2. 重复数据过滤测试
 * 3. 简化方法调用测试
 * 4. 批量操作测试
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@SpringBootTest(classes = com.yuxin.art.YxArtApplication.class)
public class DataChangeTraceServiceTest {

    @Autowired
    private DataChangeTraceService dataChangeTraceService;

    @Autowired(required = false)
    private RabbitUtil rabbitUtil;

    /**
     * 测试数据创建记录 - 使用队列
     */
    @Test
    public void testRecordCreate() {
        log.info("=== 测试数据创建记录 ===");

        try {
            if (rabbitUtil == null) {
                log.warn("RabbitUtil未注入，跳过测试");
                return;
            }

            // 准备测试数据
            Long bizId = System.currentTimeMillis(); // 使用时间戳确保唯一性
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            Map<String, Object> testData = new HashMap<>();
            testData.put("patientName", "张三");
            testData.put("age", 28);
            testData.put("diagnosis", "不孕症");
            testData.put("createTime", new java.util.Date());

            // 测试队列调用创建记录
            log.info("准备发送消息到队列...");
            log.info("队列配置：Exchange={}, RouteKey={}, Queue={}", TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY,
                TraceQueueConstants.QUEUE);

            DataChangeTraceDTO createDTO = DataChangeTraceDTO.createForCreate(bizId, bizModule, testData, 1L, "测试用户", "192.168.1.100");
            log.info("创建的DTO：bizId={}, bizModule={}, operationType={}", createDTO.getBizId(), createDTO.getBizModule(),
                createDTO.getOperationType());
            dataChangeTraceService.recordDataChange(createDTO);

            //rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, createDTO, Maps.newHashMap());
            //log.info("✅ 消息已发送到队列");

            // 等待消费者处理
            //log.info("等待消费者处理消息...");
            // Thread.sleep(10000); // 等待10秒

            //log.info("✅ 队列调用创建记录测试完成");
            //log.info("请检查：1. 控制台是否有消费者日志  2. ES中是否有数据");

        } catch (Exception e) {
            log.error("数据创建记录测试失败", e);
        }

        log.info("=== 数据创建记录测试完成 ===");
    }

    /**
     * 测试数据更新记录（重复数据过滤）
     */
    @Test
    public void testRecordUpdateWithDuplicateFilter() {
        log.info("=== 测试数据更新记录（重复数据过滤） ===");

        try {
            if (rabbitUtil == null) {
                log.warn("RabbitUtil未注入，跳过测试");
                return;
            }

            // 准备测试数据
            Long bizId = 1002L;
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            Map<String, Object> originalData = new HashMap<>();
            originalData.put("patientName", "李四");
            originalData.put("age", 30);
            originalData.put("diagnosis", "多囊卵巢综合征");
            originalData.put("updateTime", new java.util.Date());

            // 第一次更新 - 应该记录
            log.info("第一次更新测试...");
            DataChangeTraceDTO updateDTO1 = DataChangeTraceDTO.createForUpdate(bizId, bizModule, originalData, 1L, "测试用户", "192.168.1.100");
            //rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, updateDTO1, Maps.newHashMap());
            dataChangeTraceService.recordDataChange(updateDTO1);
            // 等待一秒
            Thread.sleep(1000);

            // 第二次更新相同数据 - 应该被过滤
            log.info("第二次更新相同数据测试...");
            DataChangeTraceDTO updateDTO2 = DataChangeTraceDTO.createForUpdate(bizId, bizModule, originalData, 1L, "测试用户", "192.168.1.100");
            //rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, updateDTO2, Maps.newHashMap());
            dataChangeTraceService.recordDataChange(updateDTO2);
            // 第三次更新不同数据 - 应该记录
            log.info("第三次更新不同数据测试...");
            Map<String, Object> updatedData = new HashMap<>(originalData);
            updatedData.put("age", 31);
            updatedData.put("updateTime", new java.util.Date());
            DataChangeTraceDTO updateDTO3 = DataChangeTraceDTO.createForUpdate(bizId, bizModule, updatedData, 1L, "测试用户", "192.168.1.100");
            //rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, updateDTO3, Maps.newHashMap());
            dataChangeTraceService.recordDataChange(updateDTO3);
            log.info("数据更新记录（重复数据过滤）测试通过");

        } catch (Exception e) {
            log.error("数据更新记录（重复数据过滤）测试失败", e);
        }

        log.info("=== 数据更新记录（重复数据过滤）测试完成 ===");
    }

    /**
     * 测试数据删除记录
     */
    @Test
    public void testRecordDelete() {
        log.info("=== 测试数据删除记录 ===");

        try {
            if (rabbitUtil == null) {
                log.warn("RabbitUtil未注入，跳过测试");
                return;
            }

            // 准备测试数据
            Long bizId = 1003L;
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            Map<String, Object> testData = new HashMap<>();
            testData.put("patientName", "王五");
            testData.put("age", 25);
            testData.put("diagnosis", "输卵管阻塞");
            testData.put("deleteTime", new java.util.Date());

            // 测试队列调用删除记录
            DataChangeTraceDTO deleteDTO = DataChangeTraceDTO.createForDelete(bizId, bizModule, testData, 1L, "测试用户", "192.168.1.100");
            //rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, deleteDTO, Maps.newHashMap());
            dataChangeTraceService.recordDataChange(deleteDTO);
            log.info("队列调用删除记录测试完成");

            log.info("数据删除记录测试通过");

        } catch (Exception e) {
            log.error("数据删除记录测试失败", e);
        }

        log.info("=== 数据删除记录测试完成 ===");
    }

    /**
     * 测试批量数据变更记录
     */
    @Test
    public void testRecordBatchDataChange() {
        log.info("=== 测试批量数据变更记录 ===");

        try {
            if (rabbitUtil == null) {
                log.warn("RabbitUtil未注入，跳过测试");
                return;
            }

            // 准备批量测试数据
            Long[] bizIds = {2001L, 2002L, 2003L};
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            Object[] dataList = new Object[3];
            for (int i = 0; i < 3; i++) {
                Map<String, Object> data = new HashMap<>();
                data.put("patientName", "批量患者" + (i + 1));
                data.put("age", 25 + i);
                data.put("diagnosis", "批量诊断" + (i + 1));
                data.put("batchTime", new java.util.Date());
                dataList[i] = data;
            }

            // 测试批量创建 - 使用队列调用
            for (int i = 0; i < bizIds.length; i++) {
                DataChangeTraceDTO createDTO = DataChangeTraceDTO.createForCreate(bizIds[i], bizModule, dataList[i], 1L, "测试用户", "192.168.1.100");
                rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, createDTO, Maps.newHashMap());
            }
            log.info("批量创建记录已发送到队列");

            // 等待一秒
            Thread.sleep(1000);

            // 测试批量更新（部分数据相同，应该被过滤）
            Object[] updatedDataList = new Object[3];
            for (int i = 0; i < 3; i++) {
                Map<String, Object> data = new HashMap<>();
                data.put("patientName", "批量患者" + (i + 1));
                data.put("age", 25 + i + (i % 2)); // 只有部分数据发生变化
                data.put("diagnosis", "批量诊断" + (i + 1));
                data.put("batchTime", new java.util.Date());
                updatedDataList[i] = data;
            }

            // 测试批量更新 - 使用队列调用
            for (int i = 0; i < bizIds.length; i++) {
                DataChangeTraceDTO updateDTO =
                    DataChangeTraceDTO.createForUpdate(bizIds[i], bizModule, updatedDataList[i], 1L, "测试用户", "192.168.1.100");
                rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, updateDTO, Maps.newHashMap());
            }
            log.info("批量更新记录已发送到队列");

            log.info("批量数据变更记录测试通过");

        } catch (Exception e) {
            log.error("批量数据变更记录测试失败", e);
        }

        log.info("=== 批量数据变更记录测试完成 ===");
    }

    /**
     * 测试数据比较逻辑
     */
    @Test
    public void testDataComparisonLogic() {
        log.info("=== 测试数据比较逻辑 ===");

        try {
            if (rabbitUtil == null) {
                log.warn("RabbitUtil未注入，跳过测试");
                return;
            }

            Long bizId = 3001L;
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            // 测试1：完全相同的数据
            Map<String, Object> data1 = new HashMap<>();
            data1.put("field1", "value1");
            data1.put("field2", 123);
            data1.put("field3", true);

            log.info("测试完全相同的数据...");
            DataChangeTraceDTO dto1 = DataChangeTraceDTO.createForUpdate(bizId, bizModule, data1, 1L, "测试用户", "192.168.1.100");
            rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, dto1, Maps.newHashMap());

            // 测试2：部分字段不同的数据
            Map<String, Object> data2 = new HashMap<>(data1);
            data2.put("field2", 456);

            log.info("测试部分字段不同的数据...");
            DataChangeTraceDTO dto2 = DataChangeTraceDTO.createForUpdate(bizId, bizModule, data2, 1L, "测试用户", "192.168.1.100");
            rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, dto2, Maps.newHashMap());

            // 测试3：新增字段的数据
            Map<String, Object> data3 = new HashMap<>(data2);
            data3.put("field4", "newValue");

            log.info("测试新增字段的数据...");
            DataChangeTraceDTO dto3 = DataChangeTraceDTO.createForUpdate(bizId, bizModule, data3, 1L, "测试用户", "192.168.1.100");
            rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, dto3, Maps.newHashMap());

            // 测试4：删除字段的数据
            Map<String, Object> data4 = new HashMap<>(data3);
            data4.remove("field3");

            log.info("测试删除字段的数据...");
            DataChangeTraceDTO dto4 = DataChangeTraceDTO.createForUpdate(bizId, bizModule, data4, 1L, "测试用户", "192.168.1.100");
            rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, dto4, Maps.newHashMap());

            log.info("数据比较逻辑测试通过");

        } catch (Exception e) {
            log.error("数据比较逻辑测试失败", e);
        }

        log.info("=== 数据比较逻辑测试完成 ===");
    }

    /**
     * 测试不同业务模块
     */
    @Test
    public void testDifferentBusinessModules() {
        log.info("=== 测试不同业务模块 ===");

        try {
            if (rabbitUtil == null) {
                log.warn("RabbitUtil未注入，跳过测试");
                return;
            }

            // 测试不同的业务模块
            BizModuleEnum[] modules =
                {BizModuleEnum.FEMALE_HISTORY, BizModuleEnum.MALE_HISTORY, BizModuleEnum.EMBRYO_TRANSFER, BizModuleEnum.EGG_SURGERY};

            for (int i = 0; i < modules.length; i++) {
                Long bizId = 4000L + i;
                BizModuleEnum module = modules[i];

                Map<String, Object> testData = new HashMap<>();
                testData.put("moduleTest", module.name());
                testData.put("bizId", bizId);
                testData.put("testTime", new java.util.Date());

                log.info("测试业务模块：{}", module.getDisplayName());
                DataChangeTraceDTO createDTO = DataChangeTraceDTO.createForCreate(bizId, module, testData, 1L, "测试用户", "192.168.1.100");
                rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, createDTO, Maps.newHashMap());
            }

            log.info("不同业务模块测试通过");

        } catch (Exception e) {
            log.error("不同业务模块测试失败", e);
        }

        log.info("=== 不同业务模块测试完成 ===");
    }
}
