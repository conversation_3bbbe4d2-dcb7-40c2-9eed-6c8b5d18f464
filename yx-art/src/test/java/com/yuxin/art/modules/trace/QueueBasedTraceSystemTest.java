/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:QueueBasedTraceSystemTest.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.trace;

import com.yuxin.art.modules.trace.consumer.DataChangeTraceConsumer;
import com.yuxin.art.modules.trace.dto.DataChangeTraceDTO;
import com.yuxin.art.modules.trace.enums.BizModuleEnum;
import com.yuxin.art.modules.trace.service.DataChangeTraceService;
import com.yuxin.art.trace.constant.TraceQueueConstants;
import com.yuxin.mq.util.RabbitUtil;
import com.yuxin.search.entity.ElasticEntity;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

/**
 * 基于队列的数据留痕系统测试
 * <p>
 * 测试新的队列架构，包括：
 * 1. 业务方只需提供原始数据并调用队列
 * 2. 所有数据处理逻辑在队列消费端进行
 * 3. 数据比较和重复过滤在消费端执行
 * 4. 完整的异步处理流程
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@SpringBootTest
public class QueueBasedTraceSystemTest {

    @Autowired(required = false)
    private DataChangeTraceService dataChangeTraceService;

    @Autowired(required = false)
    private DataChangeTraceConsumer dataChangeTraceConsumer;

    @Autowired(required = false)
    private RabbitUtil rabbitUtil;

    /**
     * 测试业务方简单调用（发送原始数据到队列）
     */
    @Test
    public void testBusinessSimpleCall() {
        log.info("=== 测试业务方简单调用 ===");

        try {
            if (rabbitUtil == null) {
                log.warn("RabbitUtil未注入，跳过测试");
                return;
            }

            // 准备测试数据
            Long bizId = 5001L;
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            Map<String, Object> testData = new HashMap<>();
            testData.put("patientName", "队列测试患者");
            testData.put("age", 32);
            testData.put("diagnosis", "队列测试诊断");
            testData.put("createTime", new java.util.Date());

            // 业务方直接调用队列
            log.info("业务方发送原始数据到队列...");
            DataChangeTraceDTO createDTO = DataChangeTraceDTO.createForCreate(bizId, bizModule, testData, 1L, "队列测试用户", "192.168.1.200");
            rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, createDTO, Maps.newHashMap());

            log.info("原始数据已发送到队列，所有处理逻辑将在消费端进行");

            // 模拟等待队列处理
            Thread.sleep(2000);

            log.info("业务方简单调用测试完成");

        } catch (Exception e) {
            log.error("业务方简单调用测试失败", e);
        }

        log.info("=== 业务方简单调用测试完成 ===");
    }

    /**
     * 测试队列消费端数据处理
     */
    @Test
    public void testQueueConsumerDataProcessing() {
        log.info("=== 测试队列消费端数据处理 ===");

        try {
            if (dataChangeTraceConsumer == null) {
                log.warn("DataChangeTraceConsumer未注入，跳过测试");
                return;
            }

            // 模拟队列消息 - 使用DataChangeTraceDTO
            DataChangeTraceDTO mockDTO = createMockDataChangeTraceDTO();

            log.info("模拟队列消费端处理消息...");
            dataChangeTraceConsumer.consumeDataChangeTrace(mockDTO);

            log.info("队列消费端数据处理测试完成");

        } catch (Exception e) {
            log.error("队列消费端数据处理测试失败", e);
        }

        log.info("=== 队列消费端数据处理测试完成 ===");
    }

    /**
     * 测试重复数据在消费端的过滤
     */
    @Test
    public void testDuplicateDataFilteringInConsumer() {
        log.info("=== 测试重复数据在消费端的过滤 ===");

        try {
            if (rabbitUtil == null) {
                log.warn("RabbitUtil未注入，跳过测试");
                return;
            }

            Long bizId = 5002L;
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            Map<String, Object> testData = new HashMap<>();
            testData.put("patientName", "重复数据测试");
            testData.put("age", 28);
            testData.put("diagnosis", "重复数据诊断");
            testData.put("testTime", new java.util.Date());

            // 第一次发送数据
            log.info("第一次发送数据到队列...");
            DataChangeTraceDTO createDTO = DataChangeTraceDTO.createForCreate(bizId, bizModule, testData, 1L, "测试用户", "*************");
            rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, createDTO, Maps.newHashMap());

            // 等待处理
            Thread.sleep(1000);

            // 第二次发送相同数据（应该在消费端被过滤）
            log.info("第二次发送相同数据到队列...");
            DataChangeTraceDTO updateDTO1 = DataChangeTraceDTO.createForUpdate(bizId, bizModule, testData, 1L, "测试用户", "*************");
            rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, updateDTO1, Maps.newHashMap());

            // 等待处理
            Thread.sleep(1000);

            // 第三次发送不同数据（应该在消费端被处理）
            Map<String, Object> updatedData = new HashMap<>(testData);
            updatedData.put("age", 29);
            updatedData.put("testTime", new java.util.Date());

            log.info("第三次发送不同数据到队列...");
            DataChangeTraceDTO updateDTO2 = DataChangeTraceDTO.createForUpdate(bizId, bizModule, updatedData, 1L, "测试用户", "*************");
            rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, updateDTO2, Maps.newHashMap());

            // 等待处理
            Thread.sleep(1000);

            log.info("重复数据在消费端的过滤测试完成");

        } catch (Exception e) {
            log.error("重复数据在消费端的过滤测试失败", e);
        }

        log.info("=== 重复数据在消费端的过滤测试完成 ===");
    }

    /**
     * 测试批量数据的队列处理
     */
    @Test
    public void testBatchDataQueueProcessing() {
        log.info("=== 测试批量数据的队列处理 ===");

        try {
            if (rabbitUtil == null) {
                log.warn("RabbitUtil未注入，跳过测试");
                return;
            }

            // 准备批量测试数据
            Long[] bizIds = {6001L, 6002L, 6003L, 6004L, 6005L};
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            Object[] dataList = new Object[5];
            for (int i = 0; i < 5; i++) {
                Map<String, Object> data = new HashMap<>();
                data.put("patientName", "批量队列患者" + (i + 1));
                data.put("age", 25 + i);
                data.put("diagnosis", "批量队列诊断" + (i + 1));
                data.put("batchTime", new java.util.Date());
                dataList[i] = data;
            }

            log.info("发送批量数据到队列...");
            // 批量发送到队列
            for (int i = 0; i < bizIds.length; i++) {
                DataChangeTraceDTO createDTO = DataChangeTraceDTO.createForCreate(bizIds[i], bizModule, dataList[i], 1L, "批量测试用户", "*************");
                rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, createDTO, Maps.newHashMap());
            }

            // 等待队列处理
            Thread.sleep(3000);

            log.info("批量数据的队列处理测试完成");

        } catch (Exception e) {
            log.error("批量数据的队列处理测试失败", e);
        }

        log.info("=== 批量数据的队列处理测试完成 ===");
    }

    /**
     * 测试不同操作类型的队列处理
     */
    @Test
    public void testDifferentOperationTypesInQueue() {
        log.info("=== 测试不同操作类型的队列处理 ===");

        try {
            if (rabbitUtil == null) {
                log.warn("RabbitUtil未注入，跳过测试");
                return;
            }

            Long bizId = 7001L;
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            Map<String, Object> testData = new HashMap<>();
            testData.put("patientName", "操作类型测试");
            testData.put("age", 30);
            testData.put("diagnosis", "操作类型诊断");

            // 测试CREATE操作
            log.info("测试CREATE操作...");
            DataChangeTraceDTO createDTO = DataChangeTraceDTO.createForCreate(bizId, bizModule, testData, 1L, "测试用户", "*************");
            rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, createDTO, Maps.newHashMap());
            Thread.sleep(1000);

            // 测试UPDATE操作
            log.info("测试UPDATE操作...");
            Map<String, Object> updatedData = new HashMap<>(testData);
            updatedData.put("age", 31);
            DataChangeTraceDTO updateDTO = DataChangeTraceDTO.createForUpdate(bizId, bizModule, updatedData, 1L, "测试用户", "*************");
            rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, updateDTO, Maps.newHashMap());
            Thread.sleep(1000);

            // 测试DELETE操作（应该总是被处理，不被过滤）
            log.info("测试DELETE操作...");
            DataChangeTraceDTO deleteDTO = DataChangeTraceDTO.createForDelete(bizId, bizModule, testData, 1L, "测试用户", "*************");
            rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, deleteDTO, Maps.newHashMap());
            Thread.sleep(1000);

            log.info("不同操作类型的队列处理测试完成");

        } catch (Exception e) {
            log.error("不同操作类型的队列处理测试失败", e);
        }

        log.info("=== 不同操作类型的队列处理测试完成 ===");
    }

    /**
     * 测试队列架构的性能优势
     */
    @Test
    public void testQueueArchitecturePerformance() {
        log.info("=== 测试队列架构的性能优势 ===");

        try {
            if (rabbitUtil == null) {
                log.warn("RabbitUtil未注入，跳过测试");
                return;
            }

            int testCount = 10;
            long startTime = System.currentTimeMillis();

            for (int i = 0; i < testCount; i++) {
                Long bizId = 8000L + i;
                BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

                Map<String, Object> testData = new HashMap<>();
                testData.put("patientName", "性能测试患者" + i);
                testData.put("age", 25 + (i % 10));
                testData.put("diagnosis", "性能测试诊断" + i);
                testData.put("performanceTest", true);
                testData.put("testTime", new java.util.Date());

                // 业务方调用应该很快返回（异步处理）
                DataChangeTraceDTO createDTO = DataChangeTraceDTO.createForCreate(bizId, bizModule, testData, 1L, "性能测试用户", "*************");
                rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, createDTO, Maps.newHashMap());
            }

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            log.info("发送{}条数据到队列耗时：{}ms，平均每条：{}ms", testCount, duration, duration / testCount);
            log.info("业务方调用快速返回，数据处理在队列中异步进行");

            // 等待队列处理完成
            Thread.sleep(5000);

            log.info("队列架构的性能优势测试完成");

        } catch (Exception e) {
            log.error("队列架构的性能优势测试失败", e);
        }

        log.info("=== 队列架构的性能优势测试完成 ===");
    }

    /**
     * 创建模拟的DataChangeTraceDTO
     */
    private DataChangeTraceDTO createMockDataChangeTraceDTO() {
        Map<String, Object> bizData = new HashMap<>();
        bizData.put("patientName", "模拟队列患者");
        bizData.put("age", 35);
        bizData.put("diagnosis", "模拟队列诊断");
        bizData.put("mockTest", true);

        return DataChangeTraceDTO.createForCreate(
                9001L,
                BizModuleEnum.FEMALE_HISTORY,
                bizData,
                1L,
                "模拟测试用户",
                "*************"
        );
    }

    /**
     * 创建模拟的ElasticEntity
     */
    private ElasticEntity createMockElasticEntity() {
        ElasticEntity entity = new ElasticEntity();
        entity.setId("test-trace-id-" + System.currentTimeMillis());
        entity.setIndexName("trace_index");

        // 模拟DataChangeTrace数据
        Map<String, Object> traceData = new HashMap<>();
        traceData.put("id", entity.getId());
        traceData.put("bizId", 9001L);
        traceData.put("bizTableName", "art_female_history");
        traceData.put("bizModule", "FEMALE_HISTORY");
        traceData.put("operationType", "CREATE");
        traceData.put("operationTime", new java.util.Date());
        traceData.put("createTime", new java.util.Date());
        traceData.put("operatorId", 1L);
        traceData.put("operatorName", "模拟测试用户");
        traceData.put("operationClientIp", "*************");

        // 模拟业务数据
        Map<String, Object> bizData = new HashMap<>();
        bizData.put("patientName", "模拟队列患者");
        bizData.put("age", 35);
        bizData.put("diagnosis", "模拟队列诊断");
        bizData.put("mockTest", true);

        traceData.put("bizData", com.yuxin.art.modules.trace.util.TraceDataUtils.convertToJsonString(bizData));

        entity.setData(traceData);
        return entity;
    }
}
