/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:WordDocumentServiceTest.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.trace;

import com.yuxin.art.modules.trace.enums.BizModuleEnum;
import com.yuxin.art.modules.trace.service.WordDocumentService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * Word文档生成服务测试
 * <p>
 * 测试Word文档生成功能，包括：
 * 1. 带批注的Word文档生成
 * 2. 不带批注的Word文档生成
 * 3. 模板文件检查
 * 4. 业务模块支持检查
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@SpringBootTest(classes = com.yuxin.art.YxArtApplication.class)
@TestPropertySource(properties = {
    "spring.rabbitmq.host=************",
    "spring.rabbitmq.port=5672",
    "spring.rabbitmq.username=yx-art-dev",
    "spring.rabbitmq.password=yx-art-dev",
    "spring.rabbitmq.virtual-host=yx-art-dev"
})
public class WordDocumentServiceTest {

    @Autowired(required = false)
    private WordDocumentService wordDocumentService;

    /**
     * 测试生成带批注的Word文档
     */
    @Test
    public void testGenerateWordWithComments() {
        log.info("=== 测试生成带批注的Word文档 ===");

        try {
            if (wordDocumentService == null) {
                log.warn("WordDocumentService未注入，跳过测试");
                return;
            }

            // 测试数据
            Long bizId = 1001L;
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            log.info("开始生成带批注的Word文档：bizId={}, bizModule={}", bizId, bizModule);

            // 生成Word文档
            byte[] wordBytes = wordDocumentService.generateWordWithComments(bizId, bizModule);

            if (wordBytes != null && wordBytes.length > 0) {
                log.info("✅ Word文档生成成功：size={} bytes", wordBytes.length);

                // 保存到临时文件进行验证
                String fileName = String.format("test_word_with_comments_%s_%s.docx", bizModule.name(), bizId);
                saveToTempFile(wordBytes, fileName);

            } else {
                log.error("❌ Word文档生成失败：返回空内容");
            }

        } catch (Exception e) {
            log.error("❌ 生成带批注的Word文档测试失败", e);
        }

        log.info("=== 带批注Word文档生成测试完成 ===");
    }

    /**
     * 测试生成不带批注的Word文档
     */
    @Test
    public void testGenerateWordWithoutComments() {
        log.info("=== 测试生成不带批注的Word文档 ===");

        try {
            if (wordDocumentService == null) {
                log.warn("WordDocumentService未注入，跳过测试");
                return;
            }

            // 测试数据
            Long bizId = 1002L;
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            log.info("开始生成不带批注的Word文档：bizId={}, bizModule={}", bizId, bizModule);

            // 生成Word文档
            byte[] wordBytes = wordDocumentService.generateWordWithoutComments(bizId, bizModule);

            if (wordBytes != null && wordBytes.length > 0) {
                log.info("✅ Word文档生成成功：size={} bytes", wordBytes.length);

                // 保存到临时文件进行验证
                String fileName = String.format("test_word_without_comments_%s_%s.docx", bizModule.name(), bizId);
                saveToTempFile(wordBytes, fileName);

            } else {
                log.error("❌ Word文档生成失败：返回空内容");
            }

        } catch (Exception e) {
            log.error("❌ 生成不带批注的Word文档测试失败", e);
        }

        log.info("=== 不带批注Word文档生成测试完成 ===");
    }

    /**
     * 测试模板文件检查
     */
    @Test
    public void testTemplateFileCheck() {
        log.info("=== 测试模板文件检查 ===");

        try {
            if (wordDocumentService == null) {
                log.warn("WordDocumentService未注入，跳过测试");
                return;
            }

            // 测试所有业务模块
            for (BizModuleEnum bizModule : BizModuleEnum.values()) {
                log.info("检查业务模块：{}", bizModule.getDisplayName());

                // 检查是否支持
                boolean isSupported = wordDocumentService.isModuleSupported(bizModule);
                log.info("  - 是否支持：{}", isSupported);

                // 获取模板路径
                String templatePath = wordDocumentService.getTemplatePath(bizModule);
                log.info("  - 模板路径：{}", templatePath);

                // 检查模板是否存在
                boolean templateExists = wordDocumentService.isTemplateExists(bizModule);
                log.info("  - 模板存在：{}", templateExists);

                if (isSupported && !templateExists) {
                    log.warn("⚠️ 业务模块{}支持但模板文件不存在", bizModule.getDisplayName());
                }
            }

        } catch (Exception e) {
            log.error("❌ 模板文件检查测试失败", e);
        }

        log.info("=== 模板文件检查测试完成 ===");
    }

    /**
     * 测试预览URL生成
     */
    @Test
    public void testGeneratePreviewUrl() {
        log.info("=== 测试预览URL生成 ===");

        try {
            if (wordDocumentService == null) {
                log.warn("WordDocumentService未注入，跳过测试");
                return;
            }

            // 测试数据
            Long bizId = 1003L;
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            // 生成带批注的预览URL
            String urlWithComments = wordDocumentService.generatePreviewUrl(bizId, bizModule, true);
            log.info("带批注预览URL：{}", urlWithComments);

            // 生成不带批注的预览URL
            String urlWithoutComments = wordDocumentService.generatePreviewUrl(bizId, bizModule, false);
            log.info("不带批注预览URL：{}", urlWithoutComments);

            if (urlWithComments != null && urlWithoutComments != null) {
                log.info("✅ 预览URL生成成功");
            } else {
                log.error("❌ 预览URL生成失败");
            }

        } catch (Exception e) {
            log.error("❌ 预览URL生成测试失败", e);
        }

        log.info("=== 预览URL生成测试完成 ===");
    }

    /**
     * 测试多种业务模块的Word生成
     */
    @Test
    public void testMultipleModules() {
        log.info("=== 测试多种业务模块的Word生成 ===");

        try {
            if (wordDocumentService == null) {
                log.warn("WordDocumentService未注入，跳过测试");
                return;
            }

            // 测试不同的业务模块
            BizModuleEnum[] testModules = {
                BizModuleEnum.FEMALE_HISTORY,
                BizModuleEnum.MALE_HISTORY
            };

            for (BizModuleEnum bizModule : testModules) {
                log.info("测试业务模块：{}", bizModule.getDisplayName());

                try {
                    Long bizId = System.currentTimeMillis() + bizModule.ordinal();

                    // 生成带批注的文档
                    byte[] wordWithComments = wordDocumentService.generateWordWithComments(bizId, bizModule);
                    if (wordWithComments != null) {
                        log.info("  ✅ 带批注文档生成成功：{} bytes", wordWithComments.length);
                    }

                    // 生成不带批注的文档
                    byte[] wordWithoutComments = wordDocumentService.generateWordWithoutComments(bizId, bizModule);
                    if (wordWithoutComments != null) {
                        log.info("  ✅ 不带批注文档生成成功：{} bytes", wordWithoutComments.length);
                    }

                } catch (Exception e) {
                    log.error("  ❌ 业务模块{}测试失败：{}", bizModule.getDisplayName(), e.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("❌ 多种业务模块测试失败", e);
        }

        log.info("=== 多种业务模块测试完成 ===");
    }

    /**
     * 保存字节数组到临时文件
     */
    private void saveToTempFile(byte[] data, String fileName) {
        try {
            String tempDir = System.getProperty("java.io.tmpdir");
            File tempFile = new File(tempDir, fileName);

            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(data);
                log.info("文件已保存到：{}", tempFile.getAbsolutePath());
            }

        } catch (IOException e) {
            log.error("保存临时文件失败：{}", e.getMessage());
        }
    }
}
