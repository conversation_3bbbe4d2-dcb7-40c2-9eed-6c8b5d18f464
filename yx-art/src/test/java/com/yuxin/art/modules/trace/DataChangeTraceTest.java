/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称：yx-art
 * 文件名称:DataChangeTraceTest.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.trace;

import com.yuxin.art.domain.art.medicalhistory.ArtFemaleHistory;
import com.yuxin.art.modules.art.medicalhistory.service.ArtFemaleHistoryService;
import com.yuxin.art.modules.trace.dto.DataChangeTraceDTO;
import com.yuxin.art.modules.trace.enums.BizModuleEnum;
import com.yuxin.art.modules.trace.service.DataChangeTraceService;
import com.yuxin.art.modules.trace.util.TraceDataUtils;
import com.yuxin.art.trace.constant.TraceQueueConstants;
import com.yuxin.mq.util.RabbitUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;
import java.util.Map;

/**
 * 数据变更留痕功能测试类
 * <p>
 * 用于验证数据留痕功能的正确性，包括创建、更新、删除操作的留痕记录。
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@SpringBootTest
public class DataChangeTraceTest {

    @Autowired(required = false)
    private DataChangeTraceService dataChangeTraceService;

    @Autowired(required = false)
    private ArtFemaleHistoryService artFemaleHistoryService;

    @Autowired(required = false)
    private RabbitUtil rabbitUtil;

    /**
     * 测试数据工具类功能
     */
    @Test
    public void testTraceDataUtils() {
        log.info("======> 测试数据工具类功能 [开始]");

        // 创建测试数据
        ArtFemaleHistory testData = createTestFemaleHistory();

        // 测试数据转换
        String jsonString = TraceDataUtils.convertToJsonString(testData);
        log.info("转换后的JSON字符串长度: {}", jsonString != null ? jsonString.length() : 0);
        log.info("JSON字符串内容: {}", jsonString);

        // 测试数据解析
        if (jsonString != null) {
            Map<String, Object> parsedData = TraceDataUtils.parseFromJsonString(jsonString);
            log.info("解析后的数据: {}", parsedData);
        }

        // 测试数据比较
        ArtFemaleHistory oldData = createTestFemaleHistory();
        ArtFemaleHistory newData = createTestFemaleHistory();
        newData.setChiefComplaint("修改后的主诉");
        newData.setHistoryPresentIllness("修改后的现病史");

        String changes = TraceDataUtils.compareDataChanges(oldData, newData);
        log.info("数据变更比较结果: {}", changes);

        log.info("======> 测试数据工具类功能 [完成]");
    }

    /**
     * 测试留痕服务功能
     */
    @Test
    public void testDataChangeTraceService() {
        log.info("======> 测试留痕服务功能 [开始]");

        // 创建测试数据
        ArtFemaleHistory testData = createTestFemaleHistory();

        // 检查依赖注入
        if (rabbitUtil == null) {
            log.warn("RabbitUtil未注入，跳过测试");
            return;
        }

        // 测试创建留痕 - 使用队列调用
        log.info("测试创建留痕...");
        DataChangeTraceDTO createDTO = DataChangeTraceDTO.createForCreate(1001L, BizModuleEnum.FEMALE_HISTORY, testData, 1L, "测试用户", "192.168.1.100");
        rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, createDTO, Maps.newHashMap());

        // 测试更新留痕 - 使用队列调用
        log.info("测试更新留痕...");
        ArtFemaleHistory oldData = createTestFemaleHistory();
        ArtFemaleHistory newData = createTestFemaleHistory();
        newData.setChiefComplaint("更新后的主诉");
        newData.setHistoryPresentIllness("更新后的现病史");

        DataChangeTraceDTO updateDTO = DataChangeTraceDTO.createForUpdate(1001L, BizModuleEnum.FEMALE_HISTORY, newData, 1L, "测试用户", "192.168.1.100");
        rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, updateDTO, Maps.newHashMap());

        // 测试删除留痕 - 使用队列调用
        log.info("测试删除留痕...");
        DataChangeTraceDTO deleteDTO = DataChangeTraceDTO.createForDelete(1001L, BizModuleEnum.FEMALE_HISTORY, testData, 1L, "测试用户", "192.168.1.100");
        rabbitUtil.sendNormalMessage(TraceQueueConstants.EXCHANGE, TraceQueueConstants.ROUTEKEY, deleteDTO, Maps.newHashMap());

        // 等待异步处理
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        log.info("======> 测试留痕服务功能 [完成]");
    }

    /**
     * 测试业务集成功能（需要数据库环境）
     */
    @Test
    public void testBusinessIntegration() {
        log.info("======> 测试业务集成功能 [开始]");

        try {
            // 创建测试数据
            ArtFemaleHistory testData = createTestFemaleHistory();
            testData.setCycleId(9999L); // 使用测试周期ID
            testData.setPatientId(9999L); // 使用测试患者ID

            // 测试保存操作（会触发留痕）
            log.info("测试保存操作...");
            Long savedId = artFemaleHistoryService.save(testData);
            log.info("保存成功，ID: {}", savedId);

            // 测试更新操作（会触发留痕）
            if (savedId != null) {
                log.info("测试更新操作...");
                testData.setId(savedId);
                testData.setChiefComplaint("更新后的主诉");
                testData.setHistoryPresentIllness("更新后的现病史");
                artFemaleHistoryService.save(testData);
                log.info("更新成功");
            }

            // 等待异步处理
            Thread.sleep(3000);

        } catch (Exception e) {
            log.error("业务集成测试失败", e);
        }

        log.info("======> 测试业务集成功能 [完成]");
    }

    /**
     * 创建测试用的女方病史数据
     */
    private ArtFemaleHistory createTestFemaleHistory() {
        ArtFemaleHistory history = new ArtFemaleHistory();

        // 基本信息
        history.setCycleId(9999L);
        history.setPatientId(9999L);

        // 主诉和现病史
        history.setChiefComplaint("测试主诉内容");
        history.setHistoryPresentIllness("测试现病史内容");

        // 月经史
        history.setMhMenarche(14);
        history.setMhMenstrualCycleMin(28);
        history.setMhMenstrualCycleMax(30);
        history.setMhPeriodlMin(5);
        history.setMhPeriodMax(7);

        // 个人史
        history.setPhSmoke(0);
        history.setPhAlcohol(0);
        history.setPhDrug(0);

        // 家庭史
        history.setFhGenetic(0);  // 0表示无
        history.setFhSimilarDiseases(0);  // 0表示无

        // 设置记录信息
        history.setRecorderId(1L);
        history.setRecorderName("测试医生");
        history.setRecorderDate(new Date());

        return history;
    }
}
