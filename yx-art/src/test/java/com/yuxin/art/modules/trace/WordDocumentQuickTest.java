/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称：yx-art
 * 文件名称:WordDocumentQuickTest.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.trace;

import com.yuxin.art.modules.trace.enums.BizModuleEnum;
import com.yuxin.art.modules.trace.service.WordDocumentService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Word文档生成快速测试
 * <p>
 * 快速验证Word文档生成功能是否正常工作
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@SpringBootTest
public class WordDocumentQuickTest {

    @Autowired
    private WordDocumentService wordDocumentService;

    /**
     * 快速测试 - 检查服务是否正常注入
     */
    @Test
    public void testServiceInjection() {
        log.info("=== 快速测试：服务注入检查 ===");
        
        if (wordDocumentService != null) {
            log.info("✅ WordDocumentService 注入成功");
        } else {
            log.error("❌ WordDocumentService 注入失败");
        }
        
        log.info("=== 服务注入检查完成 ===");
    }

    /**
     * 快速测试 - 检查模板文件路径
     */
    @Test
    public void testTemplatePaths() {
        log.info("=== 快速测试：模板文件路径检查 ===");
        
        try {
            // 检查女方病史模板
            String femaleHistoryPath = wordDocumentService.getTemplatePath(BizModuleEnum.FEMALE_HISTORY);
            log.info("女方病史模板路径：{}", femaleHistoryPath);
            
            boolean femaleHistoryExists = wordDocumentService.isTemplateExists(BizModuleEnum.FEMALE_HISTORY);
            log.info("女方病史模板存在：{}", femaleHistoryExists);
            
            // 检查男方病史模板
            String maleHistoryPath = wordDocumentService.getTemplatePath(BizModuleEnum.MALE_HISTORY);
            log.info("男方病史模板路径：{}", maleHistoryPath);
            
            boolean maleHistoryExists = wordDocumentService.isTemplateExists(BizModuleEnum.MALE_HISTORY);
            log.info("男方病史模板存在：{}", maleHistoryExists);
            
            if (femaleHistoryExists) {
                log.info("✅ 女方病史模板检查通过");
            } else {
                log.warn("⚠️ 女方病史模板不存在");
            }
            
            if (maleHistoryExists) {
                log.info("✅ 男方病史模板检查通过");
            } else {
                log.warn("⚠️ 男方病史模板不存在");
            }
            
        } catch (Exception e) {
            log.error("❌ 模板路径检查失败", e);
        }
        
        log.info("=== 模板文件路径检查完成 ===");
    }

    /**
     * 快速测试 - 检查模块支持情况
     */
    @Test
    public void testModuleSupport() {
        log.info("=== 快速测试：模块支持情况检查 ===");
        
        try {
            boolean femaleHistorySupported = wordDocumentService.isModuleSupported(BizModuleEnum.FEMALE_HISTORY);
            log.info("女方病史模块支持：{}", femaleHistorySupported);
            
            boolean maleHistorySupported = wordDocumentService.isModuleSupported(BizModuleEnum.MALE_HISTORY);
            log.info("男方病史模块支持：{}", maleHistorySupported);
            
            if (femaleHistorySupported && maleHistorySupported) {
                log.info("✅ 核心模块支持检查通过");
            } else {
                log.warn("⚠️ 部分核心模块不支持");
            }
            
        } catch (Exception e) {
            log.error("❌ 模块支持检查失败", e);
        }
        
        log.info("=== 模块支持情况检查完成 ===");
    }

    /**
     * 快速测试 - 尝试生成Word文档（使用模拟数据）
     */
    @Test
    public void testWordGeneration() {
        log.info("=== 快速测试：Word文档生成 ===");
        
        try {
            // 使用一个可能存在的业务ID进行测试
            Long testBizId = 1L;
            BizModuleEnum testModule = BizModuleEnum.FEMALE_HISTORY;
            
            log.info("测试参数：bizId={}, module={}", testBizId, testModule);
            
            // 检查模块支持
            if (!wordDocumentService.isModuleSupported(testModule)) {
                log.warn("⚠️ 模块不支持，跳过Word生成测试");
                return;
            }
            
            // 检查模板存在
            if (!wordDocumentService.isTemplateExists(testModule)) {
                log.warn("⚠️ 模板不存在，跳过Word生成测试");
                return;
            }
            
            // 尝试生成Word文档
            log.info("开始生成Word文档...");
            byte[] wordBytes = wordDocumentService.generateWordWithoutComments(testBizId, testModule);
            
            if (wordBytes != null && wordBytes.length > 0) {
                log.info("✅ Word文档生成成功：size={} bytes", wordBytes.length);
            } else {
                log.warn("⚠️ Word文档生成返回空内容");
            }
            
        } catch (Exception e) {
            log.error("❌ Word文档生成测试失败：{}", e.getMessage());
            // 这里不抛出异常，因为可能是因为测试数据不存在
        }
        
        log.info("=== Word文档生成测试完成 ===");
    }

    /**
     * 快速测试 - 预览URL生成
     */
    @Test
    public void testPreviewUrlGeneration() {
        log.info("=== 快速测试：预览URL生成 ===");
        
        try {
            Long testBizId = 1L;
            BizModuleEnum testModule = BizModuleEnum.FEMALE_HISTORY;
            
            // 生成预览URL
            String previewUrl = wordDocumentService.generatePreviewUrl(testBizId, testModule, false);
            log.info("预览URL：{}", previewUrl);
            
            if (previewUrl != null && !previewUrl.isEmpty()) {
                log.info("✅ 预览URL生成成功");
            } else {
                log.warn("⚠️ 预览URL生成失败");
            }
            
        } catch (Exception e) {
            log.error("❌ 预览URL生成测试失败", e);
        }
        
        log.info("=== 预览URL生成测试完成 ===");
    }

    /**
     * 综合快速测试
     */
    @Test
    public void testAll() {
        log.info("=== 综合快速测试开始 ===");
        
        testServiceInjection();
        testTemplatePaths();
        testModuleSupport();
        testWordGeneration();
        testPreviewUrlGeneration();
        
        log.info("=== 综合快速测试完成 ===");
    }
}
