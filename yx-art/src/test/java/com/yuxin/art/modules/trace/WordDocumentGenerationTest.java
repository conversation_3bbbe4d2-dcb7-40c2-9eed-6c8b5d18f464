/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称：yx-art
 * 文件名称:WordDocumentGenerationTest.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.trace;

import com.yuxin.art.modules.trace.enums.BizModuleEnum;
import com.yuxin.art.modules.trace.service.WordDocumentService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.FileOutputStream;
import java.io.IOException;

/**
 * Word文档生成功能测试
 * <p>
 * 测试基于FemaleHistory.docx模板文件生成Word文档的功能
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@SpringBootTest
public class WordDocumentGenerationTest {

    @Autowired
    private WordDocumentService wordDocumentService;

    /**
     * 测试生成女方病史Word文档（带批注）
     */
    @Test
    public void testGenerateFemaleHistoryWithComments() {
        log.info("=== 测试生成女方病史Word文档（带批注） ===");

        try {
            Long bizId = 1001L;
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            log.info("开始生成Word文档：bizId={}, bizModule={}", bizId, bizModule);

            // 检查模块支持
            boolean isSupported = wordDocumentService.isModuleSupported(bizModule);
            log.info("模块支持检查：{}", isSupported);

            if (!isSupported) {
                log.warn("业务模块不支持，跳过测试");
                return;
            }

            // 检查模板文件
            String templatePath = wordDocumentService.getTemplatePath(bizModule);
            log.info("模板文件路径：{}", templatePath);

            boolean templateExists = wordDocumentService.isTemplateExists(bizModule);
            log.info("模板文件存在：{}", templateExists);

            if (!templateExists) {
                log.warn("模板文件不存在，跳过测试");
                return;
            }

            // 生成Word文档
            byte[] wordBytes = wordDocumentService.generateWordWithComments(bizId, bizModule);

            if (wordBytes != null && wordBytes.length > 0) {
                log.info("✅ Word文档生成成功：size={} bytes", wordBytes.length);

                // 保存到临时文件
                saveToFile(wordBytes, "female_history_with_comments_" + bizId + ".docx");
            } else {
                log.error("❌ Word文档生成失败：返回空内容");
            }

        } catch (Exception e) {
            log.error("❌ 测试失败", e);
        }

        log.info("=== 女方病史Word文档生成测试完成 ===");
    }

    /**
     * 测试生成女方病史Word文档（不带批注）
     */
    @Test
    public void testGenerateFemaleHistoryWithoutComments() {
        log.info("=== 测试生成女方病史Word文档（不带批注） ===");

        try {
            Long bizId = 1001L;
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            log.info("开始生成Word文档：bizId={}, bizModule={}", bizId, bizModule);

            // 生成Word文档
            byte[] wordBytes = wordDocumentService.generateWordWithoutComments(bizId, bizModule);

            if (wordBytes != null && wordBytes.length > 0) {
                log.info("✅ Word文档生成成功：size={} bytes", wordBytes.length);

                // 保存到临时文件
                saveToFile(wordBytes, "female_history_without_comments_" + bizId + ".docx");
            } else {
                log.error("❌ Word文档生成失败：返回空内容");
            }

        } catch (Exception e) {
            log.error("❌ 测试失败", e);
        }

        log.info("=== 女方病史Word文档生成测试完成 ===");
    }

    /**
     * 测试生成男方病史Word文档
     */
    @Test
    public void testGenerateMaleHistory() {
        log.info("=== 测试生成男方病史Word文档 ===");

        try {
            Long bizId = 1002L;
            BizModuleEnum bizModule = BizModuleEnum.MALE_HISTORY;

            log.info("开始生成Word文档：bizId={}, bizModule={}", bizId, bizModule);

            // 检查模块支持
            boolean isSupported = wordDocumentService.isModuleSupported(bizModule);
            log.info("模块支持检查：{}", isSupported);

            if (!isSupported) {
                log.warn("业务模块不支持，跳过测试");
                return;
            }

            // 生成Word文档
            byte[] wordBytes = wordDocumentService.generateWordWithComments(bizId, bizModule);

            if (wordBytes != null && wordBytes.length > 0) {
                log.info("✅ Word文档生成成功：size={} bytes", wordBytes.length);

                // 保存到临时文件
                saveToFile(wordBytes, "male_history_" + bizId + ".docx");
            } else {
                log.error("❌ Word文档生成失败：返回空内容");
            }

        } catch (Exception e) {
            log.error("❌ 测试失败", e);
        }

        log.info("=== 男方病史Word文档生成测试完成 ===");
    }

    /**
     * 测试预览URL生成
     */
    @Test
    public void testGeneratePreviewUrl() {
        log.info("=== 测试预览URL生成 ===");

        try {
            Long bizId = 1001L;
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            // 生成带批注的预览URL
            String previewUrlWithComments = wordDocumentService.generatePreviewUrl(bizId, bizModule, true);
            log.info("带批注预览URL：{}", previewUrlWithComments);

            // 生成不带批注的预览URL
            String previewUrlWithoutComments = wordDocumentService.generatePreviewUrl(bizId, bizModule, false);
            log.info("不带批注预览URL：{}", previewUrlWithoutComments);

            log.info("✅ 预览URL生成成功");

        } catch (Exception e) {
            log.error("❌ 预览URL生成测试失败", e);
        }

        log.info("=== 预览URL生成测试完成 ===");
    }

    /**
     * 测试所有业务模块的支持情况
     */
    @Test
    public void testAllModulesSupport() {
        log.info("=== 测试所有业务模块的支持情况 ===");

        for (BizModuleEnum bizModule : BizModuleEnum.values()) {
            try {
                log.info("检查模块：{} ({})", bizModule.name(), bizModule.getDisplayName());

                // 检查模块支持
                boolean isSupported = wordDocumentService.isModuleSupported(bizModule);
                log.info("  - 是否支持：{}", isSupported);

                // 获取模板路径
                String templatePath = wordDocumentService.getTemplatePath(bizModule);
                log.info("  - 模板路径：{}", templatePath);

                // 检查模板存在
                boolean templateExists = wordDocumentService.isTemplateExists(bizModule);
                log.info("  - 模板存在：{}", templateExists);

                if (isSupported && templateExists) {
                    log.info("  ✅ 模块{}完全支持", bizModule.getDisplayName());
                } else if (isSupported) {
                    log.warn("  ⚠️ 模块{}支持但模板不存在", bizModule.getDisplayName());
                } else {
                    log.info("  ℹ️ 模块{}暂不支持", bizModule.getDisplayName());
                }

            } catch (Exception e) {
                log.error("  ❌ 模块{}检查失败：{}", bizModule.getDisplayName(), e.getMessage());
            }

            log.info(""); // 空行分隔
        }

        log.info("=== 所有业务模块支持情况检查完成 ===");
    }

    /**
     * 性能测试
     */
    @Test
    public void testPerformance() {
        log.info("=== Word文档生成性能测试 ===");

        try {
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;
            int testCount = 5;
            long totalTime = 0;

            for (int i = 0; i < testCount; i++) {
                Long bizId = 1000L + i;

                long startTime = System.currentTimeMillis();

                // 生成Word文档
                byte[] wordBytes = wordDocumentService.generateWordWithComments(bizId, bizModule);

                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                totalTime += duration;

                log.info("第{}次生成：bizId={}, 耗时={}ms, 大小={}bytes",
                        i + 1, bizId, duration, wordBytes != null ? wordBytes.length : 0);
            }

            double avgTime = (double) totalTime / testCount;
            log.info("✅ 性能测试完成：平均耗时={:.2f}ms", avgTime);

        } catch (Exception e) {
            log.error("❌ 性能测试失败", e);
        }

        log.info("=== Word文档生成性能测试完成 ===");
    }

    /**
     * 保存字节数组到文件
     */
    private void saveToFile(byte[] data, String fileName) {
        try {
            String tempDir = System.getProperty("java.io.tmpdir");
            String filePath = tempDir + "/" + fileName;

            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                fos.write(data);
                fos.flush();
            }

            log.info("文件已保存到：{}", filePath);

        } catch (IOException e) {
            log.error("保存文件失败：{}", fileName, e);
        }
    }
}
