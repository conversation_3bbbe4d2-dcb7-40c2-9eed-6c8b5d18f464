/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:MockWordGenerationTest.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.trace;

import com.yuxin.art.modules.trace.enums.BizModuleEnum;
import com.yuxin.art.modules.trace.service.impl.WordDocumentServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 模拟Word生成测试
 * <p>
 * 测试Word文档生成的核心逻辑，模拟真实的Word生成过程
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class MockWordGenerationTest {

    /**
     * 测试Word文档生成的完整流程
     */
    @Test
    public void testCompleteWordGenerationFlow() {
        log.info("=== 测试Word文档生成完整流程 ===");

        try {
            // 模拟业务数据
            Long bizId = 1001L;
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            log.info("开始模拟Word文档生成：bizId={}, bizModule={}", bizId, bizModule);

            // 1. 模拟获取业务数据
            String businessData = mockGetBusinessData(bizId, bizModule);
            log.info("✅ 模拟获取业务数据成功：{}", businessData.substring(0, Math.min(100, businessData.length())) + "...");

            // 2. 模拟获取变更历史
            String changeHistory = mockGetChangeHistory(bizId, bizModule);
            log.info("✅ 模拟获取变更历史成功：{} 条记录", changeHistory.split("\n").length);

            // 3. 模拟生成带批注的Word文档
            byte[] wordWithComments = mockGenerateWordWithComments(businessData, changeHistory);
            log.info("✅ 模拟生成带批注Word文档成功：{} bytes", wordWithComments.length);

            // 4. 模拟生成不带批注的Word文档
            byte[] wordWithoutComments = mockGenerateWordWithoutComments(businessData);
            log.info("✅ 模拟生成不带批注Word文档成功：{} bytes", wordWithoutComments.length);

            // 5. 保存测试文件
            saveToTempFile(wordWithComments, "mock_word_with_comments.txt");
            saveToTempFile(wordWithoutComments, "mock_word_without_comments.txt");

            // 6. 模拟预览URL生成
            String previewUrl = mockGeneratePreviewUrl(bizId, bizModule, true);
            log.info("✅ 模拟预览URL生成成功：{}", previewUrl);

            log.info("🎉 Word文档生成完整流程测试成功！");

        } catch (Exception e) {
            log.error("❌ Word文档生成完整流程测试失败", e);
        }

        log.info("=== Word文档生成完整流程测试完成 ===");
    }

    /**
     * 测试基于变更记录的Word生成
     */
    @Test
    public void testWordGenerationWithChangeHistory() {
        log.info("=== 测试基于变更记录的Word生成 ===");

        try {
            Long bizId = 2001L;
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            // 模拟多次变更的数据
            String[] changeRecords = {
                "2024-01-01 10:00:00 - 创建记录 - 用户A - 初始数据录入",
                "2024-01-02 14:30:00 - 更新记录 - 用户B - 修改患者姓名：张三 -> 张三丰",
                "2024-01-03 09:15:00 - 更新记录 - 用户C - 修改年龄：28 -> 29",
                "2024-01-04 16:45:00 - 更新记录 - 用户A - 修改诊断：不孕症 -> 多囊卵巢综合征",
                "2024-01-05 11:20:00 - 更新记录 - 用户B - 添加备注信息"
            };

            log.info("模拟变更记录：{} 条", changeRecords.length);

            // 生成包含详细变更历史的Word文档
            StringBuilder wordContent = new StringBuilder();
            wordContent.append("=== 女方病史文档（含变更记录）===\n\n");
            wordContent.append("业务ID：").append(bizId).append("\n");
            wordContent.append("业务模块：").append(bizModule.getDisplayName()).append("\n");
            wordContent.append("生成时间：").append(new java.util.Date()).append("\n\n");

            // 当前数据
            wordContent.append("=== 当前数据 ===\n");
            wordContent.append("患者姓名：张三丰\n");
            wordContent.append("年龄：29岁\n");
            wordContent.append("诊断：多囊卵巢综合征\n");
            wordContent.append("备注：已添加详细病史信息\n\n");

            // 变更历史
            wordContent.append("=== 变更历史 ===\n");
            for (int i = 0; i < changeRecords.length; i++) {
                wordContent.append(String.format("%d. %s\n", i + 1, changeRecords[i]));
            }

            // 字段变更详情
            wordContent.append("\n=== 字段变更详情 ===\n");
            wordContent.append("【患者姓名】\n");
            wordContent.append("  - 初始值：张三\n");
            wordContent.append("  - 变更时间：2024-01-02 14:30:00\n");
            wordContent.append("  - 变更人：用户B\n");
            wordContent.append("  - 当前值：张三丰\n\n");

            wordContent.append("【年龄】\n");
            wordContent.append("  - 初始值：28\n");
            wordContent.append("  - 变更时间：2024-01-03 09:15:00\n");
            wordContent.append("  - 变更人：用户C\n");
            wordContent.append("  - 当前值：29\n\n");

            wordContent.append("【诊断】\n");
            wordContent.append("  - 初始值：不孕症\n");
            wordContent.append("  - 变更时间：2024-01-04 16:45:00\n");
            wordContent.append("  - 变更人：用户A\n");
            wordContent.append("  - 当前值：多囊卵巢综合征\n\n");

            byte[] wordBytes = wordContent.toString().getBytes(StandardCharsets.UTF_8);
            
            log.info("✅ 生成包含变更记录的Word文档：{} bytes", wordBytes.length);
            saveToTempFile(wordBytes, "word_with_change_history.txt");

            log.info("🎉 基于变更记录的Word生成测试成功！");

        } catch (Exception e) {
            log.error("❌ 基于变更记录的Word生成测试失败", e);
        }

        log.info("=== 基于变更记录的Word生成测试完成 ===");
    }

    /**
     * 测试Word生成性能
     */
    @Test
    public void testWordGenerationPerformance() {
        log.info("=== 测试Word生成性能 ===");

        try {
            int testCount = 10;
            long totalTime = 0;

            for (int i = 0; i < testCount; i++) {
                long startTime = System.currentTimeMillis();

                // 模拟Word生成过程
                Long bizId = 3000L + i;
                String businessData = mockGetBusinessData(bizId, BizModuleEnum.FEMALE_HISTORY);
                String changeHistory = mockGetChangeHistory(bizId, BizModuleEnum.FEMALE_HISTORY);
                byte[] wordBytes = mockGenerateWordWithComments(businessData, changeHistory);

                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                totalTime += duration;

                log.info("第{}次生成：bizId={}, 耗时={}ms, 大小={}bytes", 
                        i + 1, bizId, duration, wordBytes.length);
            }

            double avgTime = (double) totalTime / testCount;
            log.info("✅ 性能测试完成：");
            log.info("  - 总测试次数：{}", testCount);
            log.info("  - 总耗时：{}ms", totalTime);
            log.info("  - 平均耗时：{:.2f}ms", avgTime);
            log.info("  - 每秒处理能力：{:.2f} 文档/秒", 1000.0 / avgTime);

        } catch (Exception e) {
            log.error("❌ Word生成性能测试失败", e);
        }

        log.info("=== Word生成性能测试完成 ===");
    }

    /**
     * 模拟获取业务数据
     */
    private String mockGetBusinessData(Long bizId, BizModuleEnum bizModule) {
        StringBuilder data = new StringBuilder();
        data.append("业务ID：").append(bizId).append("\n");
        data.append("业务模块：").append(bizModule.getDisplayName()).append("\n");
        data.append("患者姓名：张三\n");
        data.append("年龄：28岁\n");
        data.append("性别：女\n");
        data.append("诊断：不孕症\n");
        data.append("主诉：结婚2年未孕\n");
        data.append("现病史：患者结婚2年，性生活正常，未采取避孕措施，未孕。\n");
        data.append("既往史：无特殊\n");
        data.append("家族史：无遗传病史\n");
        data.append("月经史：月经规律，周期28-30天\n");
        data.append("婚育史：已婚2年，未育\n");
        return data.toString();
    }

    /**
     * 模拟获取变更历史
     */
    private String mockGetChangeHistory(Long bizId, BizModuleEnum bizModule) {
        StringBuilder history = new StringBuilder();
        history.append("变更记录1：2024-01-01 创建记录\n");
        history.append("变更记录2：2024-01-02 更新患者信息\n");
        history.append("变更记录3：2024-01-03 添加检查结果\n");
        history.append("变更记录4：2024-01-04 更新诊断信息\n");
        history.append("变更记录5：2024-01-05 完善病史资料\n");
        return history.toString();
    }

    /**
     * 模拟生成带批注的Word文档
     */
    private byte[] mockGenerateWordWithComments(String businessData, String changeHistory) {
        StringBuilder content = new StringBuilder();
        content.append("=== 带批注的Word文档 ===\n\n");
        content.append("【业务数据】\n");
        content.append(businessData).append("\n");
        content.append("【变更历史批注】\n");
        content.append(changeHistory).append("\n");
        content.append("【批注说明】\n");
        content.append("- 每个字段都包含完整的变更历史\n");
        content.append("- 批注显示变更时间、变更人、变更内容\n");
        content.append("- 支持追溯所有历史版本\n");
        content.append("\n生成时间：").append(new java.util.Date());
        
        return content.toString().getBytes(StandardCharsets.UTF_8);
    }

    /**
     * 模拟生成不带批注的Word文档
     */
    private byte[] mockGenerateWordWithoutComments(String businessData) {
        StringBuilder content = new StringBuilder();
        content.append("=== 标准Word文档 ===\n\n");
        content.append(businessData).append("\n");
        content.append("\n生成时间：").append(new java.util.Date());
        
        return content.toString().getBytes(StandardCharsets.UTF_8);
    }

    /**
     * 模拟生成预览URL
     */
    private String mockGeneratePreviewUrl(Long bizId, BizModuleEnum bizModule, boolean withComments) {
        String baseUrl = "http://localhost:8080/preview";
        String commentParam = withComments ? "true" : "false";
        return String.format("%s/word?bizId=%d&bizModule=%s&withComments=%s", 
                baseUrl, bizId, bizModule.name(), commentParam);
    }

    /**
     * 保存字节数组到临时文件
     */
    private void saveToTempFile(byte[] data, String fileName) {
        try {
            String tempDir = System.getProperty("java.io.tmpdir");
            File tempFile = new File(tempDir, fileName);

            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(data);
                log.info("文件已保存到：{}", tempFile.getAbsolutePath());
            }

        } catch (IOException e) {
            log.error("保存临时文件失败：{}", e.getMessage());
        }
    }
}
