/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art
 * 文件名称:SimpleWordTest.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.modules.trace;

import com.yuxin.art.modules.trace.enums.BizModuleEnum;
import com.yuxin.art.modules.trace.service.impl.WordDocumentServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 简化的Word生成测试
 * <p>
 * 直接测试WordDocumentServiceImpl的功能，不依赖Spring容器
 * </p>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class SimpleWordTest {

    /**
     * 测试Word文档生成基本功能
     */
    @Test
    public void testBasicWordGeneration() {
        log.info("=== 测试Word文档生成基本功能 ===");

        try {
            // 创建WordDocumentServiceImpl实例
            WordDocumentServiceImpl wordService = new WordDocumentServiceImpl();

            // 测试数据
            Long bizId = 1001L;
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            log.info("开始测试Word文档生成：bizId={}, bizModule={}", bizId, bizModule);

            // 测试模板路径获取
            String templatePath = wordService.getTemplatePath(bizModule);
            log.info("模板路径：{}", templatePath);

            // 测试模块支持检查
            boolean isSupported = wordService.isModuleSupported(bizModule);
            log.info("模块支持：{}", isSupported);

            // 测试模板文件存在检查
            boolean templateExists = wordService.isTemplateExists(bizModule);
            log.info("模板存在：{}", templateExists);

            // 测试预览URL生成
            String previewUrl = wordService.generatePreviewUrl(bizId, bizModule, true);
            log.info("预览URL：{}", previewUrl);

            // 测试Word文档生成（带批注）
            log.info("开始生成带批注的Word文档...");
            byte[] wordWithComments = wordService.generateWordWithComments(bizId, bizModule);

            if (wordWithComments != null && wordWithComments.length > 0) {
                log.info("✅ 带批注Word文档生成成功：size={} bytes", wordWithComments.length);

                // 保存到临时文件
                saveToTempFile(wordWithComments, "test_word_with_comments.txt");
            } else {
                log.error("❌ 带批注Word文档生成失败");
            }

            // 测试Word文档生成（不带批注）
            log.info("开始生成不带批注的Word文档...");
            byte[] wordWithoutComments = wordService.generateWordWithoutComments(bizId, bizModule);

            if (wordWithoutComments != null && wordWithoutComments.length > 0) {
                log.info("✅ 不带批注Word文档生成成功：size={} bytes", wordWithoutComments.length);

                // 保存到临时文件
                saveToTempFile(wordWithoutComments, "test_word_without_comments.txt");
            } else {
                log.error("❌ 不带批注Word文档生成失败");
            }

        } catch (Exception e) {
            log.error("❌ Word文档生成测试失败", e);
        }

        log.info("=== Word文档生成基本功能测试完成 ===");
    }

    /**
     * 测试所有业务模块的支持情况
     */
    @Test
    public void testAllModulesSupport() {
        log.info("=== 测试所有业务模块的支持情况 ===");

        try {
            WordDocumentServiceImpl wordService = new WordDocumentServiceImpl();

            // 测试所有业务模块
            for (BizModuleEnum bizModule : BizModuleEnum.values()) {
                log.info("检查业务模块：{} ({})", bizModule.name(), bizModule.getDisplayName());

                try {
                    // 检查模块支持
                    boolean isSupported = wordService.isModuleSupported(bizModule);
                    log.info("  - 是否支持：{}", isSupported);

                    // 获取模板路径
                    String templatePath = wordService.getTemplatePath(bizModule);
                    log.info("  - 模板路径：{}", templatePath);

                    // 检查模板存在
                    boolean templateExists = wordService.isTemplateExists(bizModule);
                    log.info("  - 模板存在：{}", templateExists);

                    // 生成预览URL
                    String previewUrl = wordService.generatePreviewUrl(1001L, bizModule, true);
                    log.info("  - 预览URL：{}", previewUrl);

                    if (isSupported) {
                        log.info("  ✅ 模块{}支持Word生成", bizModule.getDisplayName());
                    } else {
                        log.warn("  ⚠️ 模块{}不支持Word生成", bizModule.getDisplayName());
                    }

                } catch (Exception e) {
                    log.error("  ❌ 模块{}检查失败：{}", bizModule.getDisplayName(), e.getMessage());
                }

                log.info(""); // 空行分隔
            }

        } catch (Exception e) {
            log.error("❌ 业务模块支持测试失败", e);
        }

        log.info("=== 所有业务模块支持测试完成 ===");
    }

    /**
     * 测试Word生成性能
     */
    @Test
    public void testWordGenerationPerformance() {
        log.info("=== 测试Word生成性能 ===");

        try {
            WordDocumentServiceImpl wordService = new WordDocumentServiceImpl();

            int testCount = 5;
            BizModuleEnum bizModule = BizModuleEnum.FEMALE_HISTORY;

            long totalTime = 0;

            for (int i = 0; i < testCount; i++) {
                Long bizId = 1000L + i;

                long startTime = System.currentTimeMillis();

                // 生成Word文档
                byte[] wordBytes = wordService.generateWordWithComments(bizId, bizModule);

                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                totalTime += duration;

                log.info("第{}次生成：bizId={}, 耗时={}ms, 大小={}bytes", 
                        i + 1, bizId, duration, wordBytes != null ? wordBytes.length : 0);
            }

            double avgTime = (double) totalTime / testCount;
            log.info("✅ 性能测试完成：平均耗时={:.2f}ms", avgTime);

        } catch (Exception e) {
            log.error("❌ Word生成性能测试失败", e);
        }

        log.info("=== Word生成性能测试完成 ===");
    }

    /**
     * 保存字节数组到临时文件
     */
    private void saveToTempFile(byte[] data, String fileName) {
        try {
            String tempDir = System.getProperty("java.io.tmpdir");
            File tempFile = new File(tempDir, fileName);

            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                fos.write(data);
                log.info("文件已保存到：{}", tempFile.getAbsolutePath());
            }

        } catch (IOException e) {
            log.error("保存临时文件失败：{}", e.getMessage());
        }
    }
}
