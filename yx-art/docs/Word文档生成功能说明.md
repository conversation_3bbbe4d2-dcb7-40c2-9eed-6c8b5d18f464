# Word文档生成功能说明

## 功能概述

基于FemaleHistory.docx模板文件，根据业务数据ID和业务模块生成Word文件的功能。支持带批注和不带批注两种模式。

## 核心功能

### 1. Word文档生成
- **带批注模式**：生成包含数据变更记录批注的Word文档
- **不带批注模式**：生成纯业务数据的Word文档

### 2. 支持的业务模块
- 女方病史（FEMALE_HISTORY）
- 男方病史（MALE_HISTORY）
- 可扩展支持其他业务模块

### 3. 模板管理
- 基于现有的FemaleHistory.docx模板
- 支持配置化的模板路径管理
- 自动模板存在性检查

## API接口

### 1. 生成带批注的Word文档
```http
POST /api/word/generate-with-comments
```

**参数：**
- `bizId` (Long): 业务数据ID
- `bizModule` (BizModuleEnum): 业务模块

**响应：**
- 直接下载Word文档文件

### 2. 生成不带批注的Word文档
```http
POST /api/word/generate-without-comments
```

**参数：**
- `bizId` (Long): 业务数据ID
- `bizModule` (BizModuleEnum): 业务模块

**响应：**
- 直接下载Word文档文件

### 3. 生成预览URL
```http
GET /api/word/preview-url
```

**参数：**
- `bizId` (Long): 业务数据ID
- `bizModule` (BizModuleEnum): 业务模块
- `withComments` (Boolean): 是否包含批注，默认false

**响应：**
```json
{
  "code": 200,
  "msg": "success",
  "data": "http://localhost:8080/preview/document_123.docx"
}
```

### 4. 检查模块支持
```http
GET /api/word/check-support
```

**参数：**
- `bizModule` (BizModuleEnum): 业务模块

**响应：**
```json
{
  "code": 200,
  "msg": "success",
  "data": true
}
```

### 5. 获取模板路径
```http
GET /api/word/template-path
```

**参数：**
- `bizModule` (BizModuleEnum): 业务模块

**响应：**
```json
{
  "code": 200,
  "msg": "success",
  "data": "classpath:templates/FemaleHistory.docx"
}
```

## 配置说明

### application.yml配置
```yaml
yuxin:
  word:
    template:
      base-path: "classpath:templates"
      female-history: "FemaleHistory.docx"
      male-history: "MaleHistory.docx"
    preview:
      base-url: "http://localhost:8080/preview"
      temp-path: "/tmp/word-preview"
      retention-minutes: 60
    comment:
      enabled: true
      author: "数据留痕系统"
      font-family: "宋体"
      font-size: 9
      color: "0066CC"
```

### 配置项说明
- `template.base-path`: 模板文件基础路径
- `template.female-history`: 女方病史模板文件名
- `template.male-history`: 男方病史模板文件名
- `preview.base-url`: 预览服务基础URL
- `preview.temp-path`: 临时文件存储路径
- `preview.retention-minutes`: 临时文件保留时间（分钟）
- `comment.enabled`: 是否启用批注功能
- `comment.author`: 批注作者
- `comment.font-family`: 批注字体
- `comment.font-size`: 批注字体大小
- `comment.color`: 批注颜色

## 使用示例

### 1. Java代码调用
```java
@Autowired
private WordDocumentService wordDocumentService;

// 生成带批注的Word文档
byte[] wordBytes = wordDocumentService.generateWordWithComments(1001L, BizModuleEnum.FEMALE_HISTORY);

// 生成不带批注的Word文档
byte[] wordBytes = wordDocumentService.generateWordWithoutComments(1001L, BizModuleEnum.FEMALE_HISTORY);

// 检查模块支持
boolean isSupported = wordDocumentService.isModuleSupported(BizModuleEnum.FEMALE_HISTORY);

// 检查模板存在
boolean templateExists = wordDocumentService.isTemplateExists(BizModuleEnum.FEMALE_HISTORY);
```

### 2. 前端调用示例
```javascript
// 生成并下载Word文档
function downloadWordDocument(bizId, bizModule, withComments = false) {
    const url = withComments 
        ? '/api/word/generate-with-comments'
        : '/api/word/generate-without-comments';
    
    const params = new URLSearchParams({
        bizId: bizId,
        bizModule: bizModule
    });
    
    window.open(`${url}?${params}`, '_blank');
}

// 获取预览URL
async function getPreviewUrl(bizId, bizModule, withComments = false) {
    const response = await fetch(`/api/word/preview-url?bizId=${bizId}&bizModule=${bizModule}&withComments=${withComments}`);
    const result = await response.json();
    return result.data;
}
```

## 测试说明

### 1. 快速测试
运行 `WordDocumentQuickTest` 类进行快速功能验证：
```bash
mvn test -Dtest=WordDocumentQuickTest
```

### 2. 完整测试
运行 `WordDocumentGenerationTest` 类进行完整功能测试：
```bash
mvn test -Dtest=WordDocumentGenerationTest
```

### 3. 测试内容
- 服务注入检查
- 模板文件路径检查
- 模块支持情况检查
- Word文档生成测试
- 预览URL生成测试
- 性能测试

## 依赖说明

### Maven依赖
```xml
<!-- Apache POI for Word document processing -->
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi</artifactId>
    <version>5.2.3</version>
</dependency>
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-ooxml</artifactId>
    <version>5.2.3</version>
</dependency>
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-scratchpad</artifactId>
    <version>5.2.3</version>
</dependency>
```

## 扩展说明

### 1. 添加新的业务模块支持
1. 在 `BizModuleEnum` 中添加新的枚举值
2. 在配置文件中添加对应的模板文件配置
3. 在 `WordDocumentServiceImpl.getTemplatePath()` 方法中添加对应的case
4. 在 `WordDocumentServiceImpl.getSupportedModules()` 方法中添加新模块

### 2. 自定义批注格式
修改 `WordDocumentServiceImpl.addComments()` 方法来自定义批注的显示格式和样式。

### 3. 模板变量扩展
在 `fillPlaceholders()` 方法中扩展占位符的处理逻辑，支持更复杂的数据绑定。

## 注意事项

1. **模板文件格式**：确保Word模板文件使用 `.docx` 格式
2. **占位符格式**：模板中的占位符使用 `{{fieldName}}` 格式
3. **字段映射**：确保模板中的占位符与业务实体的字段名称一致
4. **文件大小**：生成的Word文档大小取决于模板复杂度和数据量
5. **性能考虑**：大量并发生成时建议使用异步处理
6. **错误处理**：业务数据不存在时会抛出异常，需要适当处理

## 故障排除

### 1. 模板文件不存在
- 检查模板文件是否在正确的路径下
- 检查配置文件中的路径配置是否正确

### 2. 业务数据获取失败
- 检查业务数据ID是否存在
- 检查对应的Service是否正常工作

### 3. Word文档生成失败
- 检查POI依赖是否正确引入
- 检查模板文件是否损坏
- 查看详细的错误日志

### 4. 批注显示异常
- 检查数据变更记录是否存在
- 检查批注配置是否正确
