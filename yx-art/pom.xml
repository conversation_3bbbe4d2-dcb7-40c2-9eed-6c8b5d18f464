<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
  ~ 项目名称:yx-art-project
  ~ 模块名称：yx-art
  ~ 文件名称:pom.xml
  ~ 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yuxin</groupId>
        <artifactId>yx-art-project</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>yx-art</artifactId>
    <description>辅助生殖系统</description>

    <repositories>
        <repository>
            <id>proxy</id>
            <name>public</name>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
            <url>https://packages.aliyun.com/636484d5801ae2503bb95caf/maven/proxy</url>
        </repository>
    </repositories>

    <dependencies>
        <!-- yuxin-framework -->
        <dependency>
            <groupId>com.yuxin</groupId>
            <artifactId>yuxin-framework</artifactId>
        </dependency>

        <!-- yuxin-bean -->
        <dependency>
            <groupId>com.yuxin</groupId>
            <artifactId>yx-art-domain</artifactId>
            <version>${revision}</version>
        </dependency>

        <!--yuxin-ocr -->
        <dependency>
            <groupId>com.yuxin</groupId>
            <artifactId>yuxin-ocr-spring-boot-starter</artifactId>
        </dependency>

        <!-- yuxin-oss -->
        <dependency>
            <groupId>com.yuxin</groupId>
            <artifactId>yuxin-oss-spring-boot-starter</artifactId>
        </dependency>
        <!-- yuxin-jackson -->
        <dependency>
            <groupId>com.yuxin</groupId>
            <artifactId>yuxin-json-spring-boot-starter</artifactId>
        </dependency>
        <!-- yuxin-rabbitmq -->
        <dependency>
            <groupId>com.yuxin</groupId>
            <artifactId>yuxin-mq-spring-boot-starter</artifactId>
        </dependency>
        <!-- yuxin-redis -->
        <dependency>
            <groupId>com.yuxin</groupId>
            <artifactId>yuxin-cache-spring-boot-starter</artifactId>
        </dependency>
        <!-- yuxin-es -->
        <dependency>
            <groupId>com.yuxin</groupId>
            <artifactId>yuxin-search-spring-boot-starter</artifactId>
        </dependency>
        <!-- yuxin-canal -->
        <dependency>
            <groupId>com.yuxin</groupId>
            <artifactId>yuxin-datasync-spring-boot-starter</artifactId>
        </dependency>
        <!-- yuxin-security -->
        <dependency>
            <groupId>com.yuxin</groupId>
            <artifactId>yuxin-security-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yuxin</groupId>
            <artifactId>yuxin-word-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.28</version>
        </dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>
        <dependency>
            <groupId>org.xhtmlrenderer</groupId>
            <artifactId>flying-saucer-pdf</artifactId>
            <version>9.1.6</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>


        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.yuxin</groupId>
            <artifactId>jave-ffmpegjave</artifactId>
            <version>1.0.2</version>
        </dependency>
        <!-- mysql数据库驱动 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.23</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.10.2</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.32</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>
