/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-stats
 * 文件名称:ColumnStatsResultVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.stats.display.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 统计结果
 *
 * <AUTHOR>
 * @date 2020-12-05
 */
@Data
public class ColumnStatsResultVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 列名
     */
    private String columnName;

    /**
     * 取值
     */
    private List<CellStatsResultVO> cells;
}
