/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-stats
 * 文件名称:QueryOrder.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.stats.common.vo;

import com.yuxin.constant.GlobalConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询排序
 *
 * <AUTHOR>
 * @date 2020-12-31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryOrder {
    /**
     * 排序字段
     */
    private String field;

    /**
     * 顺序
     * 0: 升序 ASC
     * 1: 降序 DESC
     */
    private Integer order;

    public static QueryOrder asc(String field) {
        return new QueryOrder(field, GlobalConstant.NO);
    }

    public static QueryOrder desc(String field) {
        return new QueryOrder(field, GlobalConstant.YES);
    }
}
