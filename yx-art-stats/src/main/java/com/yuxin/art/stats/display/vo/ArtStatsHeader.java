/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-stats
 * 文件名称:ArtStatsHeader.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.stats.display.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用于导出统计里面，导出excel需要对应的数据头
 *
 * <AUTHOR>
 * @date 2022-01-06
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ArtStatsHeader {
    private String name;

    private String field;

    @JSONField(serialize = false, deserialize = false)
    private Integer order;
}
