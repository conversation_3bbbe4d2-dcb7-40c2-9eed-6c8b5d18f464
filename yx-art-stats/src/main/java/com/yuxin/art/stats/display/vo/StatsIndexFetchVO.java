/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-stats
 * 文件名称:StatsIndexFetchVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.stats.display.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 统计方法
 *
 * <AUTHOR>
 * @date 2020-12-05
 */
@Data
@NoArgsConstructor
public class StatsIndexFetchVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 统计或展示字段
     */
    private String field;
    /**
     * 统计方法
     */
    private Integer method;

    /**
     * 范围分组
     */
    private List<RangeVO> ranges;

    public StatsIndexFetchVO(String field, Integer method) {
        this.field = field;
        this.method = method;
    }
}
