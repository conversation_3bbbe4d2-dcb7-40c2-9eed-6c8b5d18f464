/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-stats
 * 文件名称:QcStatsParamsVO.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.stats.display.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 自定义统计查询参数
 *
 * <AUTHOR>
 * @date 2020-12-05
 */
@Data
public class QcStatsParamsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 指标 index，多个用英文逗号隔开
     */
    @ApiModelProperty(value = "指标，多个英文逗号隔开", required = true)
    @NotEmpty(message = "指标不能为空")
    private List<QcIndexVO> indices;

    /**
     * 过滤条件
     */
    @ApiModelProperty(value = "过滤条件", required = true)
    @NotEmpty(message = "过滤条件不能为空")
    private List<QcConditionVO> conditions;

    /**
     * 分组情况
     */
    @ApiModelProperty(value = "分组情况", required = true)
    @NotEmpty(message = "分组不能为空")
    private List<QcGroupVO> groups;

    /**
     * 缓存key
     */
    @ApiModelProperty(value = "缓存key")
    private String cacheKey;

    /**
     * 缓存key
     */
    @ApiModelProperty(value = "使用的条件模板ID")
    private Long conditionTemplateId;

}
