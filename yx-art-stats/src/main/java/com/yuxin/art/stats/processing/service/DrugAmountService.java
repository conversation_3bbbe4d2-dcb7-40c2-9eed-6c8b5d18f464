/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-stats
 * 文件名称:DrugAmountService.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */

package com.yuxin.art.stats.processing.service;

import com.yuxin.art.domain.medication.ArtMedicationDetail;
import com.yuxin.art.stats.processing.entity.ArtStats;

import java.util.List;

public interface DrugAmountService {
    /**
     * 用药相关统计
     *
     * @param artStats
     */
    void getMedicationsAmount(ArtStats artStats);

    /**
     * 根据类型获取用药列表
     *
     * @param id
     * @param prescriptionType 0: 治疗前 1:普通用药 2:促排用药 3:扳机用药 4:黄体转化 5:黄体支持
     * @param asc
     * @return
     */
    List<ArtMedicationDetail> getMedicationList(Long id, Integer prescriptionType, Boolean asc);
}
