/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-stats
 * 文件名称:EsConstant.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.stats.common.constants;

/**
 * ES通用常量定义
 *
 * <AUTHOR>
 * @date 2022-03-22
 */

public interface EsConstant {
    interface RespJsonPath {
        /**
         * 可直接使用，计算数据条数，一般配合from = 0, size = 0使用，不拉取数据，只要数据行数，类似count(1)
         */
        String HIT_TOTAL_VALUE = "$.hits.total.value";
        String AGG_COUNT = "$.aggregations.value_count\\#{}.value";
        /**
         * 简单统计，第二个参数可用count、sum、avg、min、max
         * 另外有一个复合统计，可以统计方差的
         */
        String AGG_STATS = "$.aggregations.stats\\#{}.{}";
    }

    interface StatsItem {
        /**
         * 个数
         */
        String COUNT = "count";
        /**
         * 批数据的最小值
         */
        String MIN = "min";
        /**
         * 批数据的最大值
         */
        String MAX = "max";
        /**
         * 批数据的平均值，即 sum  / count
         */
        String AVG = "avg";
        /**
         * 批数据的合计值
         */
        String SUM = "sum";
    }

    /**
     * # 如果位为0，则补位为空
     * 0 如果没有这一位，则补0
     */
    interface NumberFormat {
        // # 原样输出，无填充
        String SAMPLE = "#";
        // #.00 两位小数，没有小数位则补0
        String DECIMAL = "#.00";
        // #.##% 两位小数
        String PERCENT = "#.##%";
    }
}
