/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-stats
 * 文件名称:ArtQcStatsController.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.stats.display.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.yuxin.api.Result;
import com.yuxin.art.stats.display.service.QcStatsService;
import com.yuxin.art.stats.display.vo.ClSupportVO;
import com.yuxin.art.stats.display.vo.QcStatsParamVO;
import com.yuxin.art.stats.display.vo.QcStatsResultVO;
import com.yuxin.art.stats.display.vo.StatsQcIndexTemplateVO;
import com.yuxin.art.stats.templates.service.DifferService;
import com.yuxin.art.stats.templates.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 质控统计展示 Controller
 *
 * <AUTHOR>
 * @since 2020-10-13
 */
@RestController
@RequestMapping(value = "/qcStats", produces = MediaType.APPLICATION_JSON_VALUE)
@Validated
@Api(tags = "质控统计模块")
@ApiSort(1)
public class ArtQcStatsController {
    @Autowired
    private QcStatsService qcStatsService;
    @Autowired
    private DifferService differService;

    /**
     * 获取黄体支持模板
     *
     * @return
     */
    @GetMapping("/getClSupportList")
    @ApiOperation(value = "获取黄体支持模板")
    @ApiOperationSupport(order = 1)
    public Result<List<ClSupportVO>> getClSupportList() {
        return Result.success(qcStatsService.getClSupports());
    }

    /**
     * 获取统计指标
     *
     * @return
     */
    @GetMapping("/getQcIndices")
    @ApiOperation(value = "获取统计指标")
    @ApiOperationSupport(order = 2)
    @ApiImplicitParam(name = "templateId", value = "模板ID", required = true, paramType = "query", dataType = "Long")
    public Result<StatsQcIndexTemplateVO> getQcIndices(Long templateId) {
        return Result.success(qcStatsService.getQcIndices(templateId));
    }

    /**
     * 执行质控统计
     */
    @PostMapping(value = "/doQcStats", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "执行质控统计", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 3)
    public Result<QcStatsResultVO> doQcStats(@RequestBody @Validated QcStatsParamVO params) {
        return Result.success(qcStatsService.stats(params));
    }

    /**
     * 比对人群条件
     */
    @PostMapping(value = "/differCondition", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "比对人群条件", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 7)
    public Result<DifferConditionVO> differCondition(@RequestBody @Validated DifferConditionQueryVO params) {
        return Result.success(differService.differCondition(params));
    }

    /**
     * 比对分组
     */
    @PostMapping(value = "/differGroup", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "比对分组", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 8)
    public Result<DifferGroupVO> differGroup(@RequestBody @Validated DifferGroupQueryVO params) {
        return Result.success(differService.differGroup(params));
    }

    /**
     * 比对指标
     */
    @PostMapping(value = "/differIndex", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(value = "比对指标", consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperationSupport(order = 9)
    public Result<DifferIndexVO> differIndex(@RequestBody @Validated DifferIndexQueryVO params) {
        return Result.success(differService.differIndex(params));
    }
}
