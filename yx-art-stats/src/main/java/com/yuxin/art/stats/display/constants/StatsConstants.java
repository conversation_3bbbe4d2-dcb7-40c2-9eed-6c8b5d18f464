/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-stats
 * 文件名称:StatsConstants.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */

package com.yuxin.art.stats.display.constants;

import cn.hutool.core.collection.CollUtil;

import java.util.Set;
import java.util.regex.Pattern;

/**
 * @ClassName StatsConstants
 * @Description 统计常量
 * <AUTHOR>
 * @Date 2020-12-05 15:18
 * @Version 1.0
 */
public class StatsConstants {
    /**
     * 默认的格式
     */
    public static final String DEFAULT_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

    /**
     * redis哈希结构存储指标key
     */
    public static final String REDIS_INDEX_KEY = "stats:index";

    /**
     * redis哈希结构存储配置量key
     */
    public static final String REGULAR_VALUE_KEY = "stats:regularValue";

    /**
     * redis哈希结构存储指标key
     */
    public static final String REDIS_QC_INDEX_KEY = "stats:qcIndex";

    /**
     * 统一定义规则数据Mysql取值：value
     */
    public static final String VALUE_KEY = "value";

    /**
     * 统计索引名称
     */
    public static final String STATS_INDEX_NAME = "art_stats";

    /**
     * 首列，指标名
     */
    public static final String INDEX_COLUMN_KEY = "name";

    /**
     * 合计列，列名（单列时没有）
     */
    public static final String SUM_COLUMN_KEY = "gSum";

    public static final long DEFAULT_PARENT = 0L;

    /**
     * 指标项正则
     */
    public final static Pattern INDEX_PATTERN = Pattern.compile("#(.+?)#");

    /**
     * JS计算除法计算，分子!=0, 分母=0时结果
     */
    public final static String INFINITY = "Infinity";

    /**
     * JS计算除法计算，分子、分母都是0时结果
     */
    public final static String NAN = "NaN";

    /**
     * 最多检索10万条数据（两年内，10W条数据）
     */
    public final static int MAX_SIZE = 100_000;

    public static final String STATS_RESULT_CACHE_KEY = "stats:cache:";
    public static final String QC_STATS_RESULT_CACHE_KEY = "qcStats:cache:";
    public static final String CACHE_PARAM_KEY = "param";
    public static final String CACHE_RESULT_KEY = "result";

    // 过期6小时
    public static final int CACHE_EXPIRED_TIME = 6;

    /**
     * 自定义计算累积活产率数据所需要的字段
     * 考虑到日期是放开的，数据量最大为10W条（超过就不考虑分页了，只统计前10W条）
     * 这个时候数据量如果返回整个文档过大，所以进行筛选，只返回需要的数据即可。
     * 另外，还要单独为这个统计建相应VO，否则仍使用artStats，申请空间会大很多
     */
    public final static String[] CUSTOM_CUMULATIVE_FIELDS =
        {"id", "pairBondId", "ovumNum", "treatmentPlan", "liveNum", "gestationNum", "stockEmbryoNum"};

    /**
     * 统计时，统计数据量
     */
    public final static Long PRECISION_THRESHOLD = 100_000_000_000L;

    public interface IndexLevel {
        // 级别：查询获取
        int LV1 = 0;
        // 级别：运算获取(如果同样是运算级别，被依赖的需要先进行处理)
        int LV2 = 1;
        // 级别：显示使用
        int LV3 = 2;
    }

    public interface Operator {
        // 0: 等于
        int EQ = 0;
        // 1: 不等于 （暂不实现）
        int NE = 1;
        // 2: in列表，要求value是一个数组
        int IN = 2;
        // 3: not in 列表 （暂不实现）
        int NOT_IN = 3;
        // 4: 范围 大于
        int GT = 4;
        // 5: 范围 大于等于
        int GTE = 5;
        // 6: 范围 小于
        int LT = 6;
        // 7: 范围 小于等于
        int LTE = 7;
        // 8: 为空，即not exist
        int NULL = 8;
        // 9: 不为空，即exist
        int NOT_NULL = 9;
        // 10: 范围 [a, b)，包括from，不包括to
        // 等价于 GTE a and LT b
        int RANGE = 10;
        // 自定义组合情况
        int CUSTOM = -1;
    }

    /**
     * 条件类型，用于质控统计
     * 需要转化为Operator
     */
    public interface ConditionType {
        /**
         * 等值
         */
        int EQ = 0;
        /**
         * 在列表
         */
        int IN = 1;
        /**
         * 范围
         * 对应需要转换为：GTE, LT, GTE && LT ，3种，根据里面的填值来。
         */
        int RANGE = 2;
        /**
         * 自定义处理（移植组合条件、解冻组合条件）
         */
        int CUSTOM = 3;
    }

    /**
     * 条件值，对应到Java的类型
     */
    public interface ValueType {
        /**
         * 整型，int数组也算是int型
         */
        int INT = 0;
        /**
         * 浮点，使用双精度
         */
        int DOUBLE = 1;
        /**
         * 日期类型
         */
        int DATE = 2;
        /**
         * long类型，一般指的都是ID
         */
        int LONG = 3;
    }

    public interface StatMethod {
        // 计算数量
        int COUNT = 0;
        // 初步统计
        int STATS = 1;
        // 唯一 (预留)
        int CARDINALITY = 2;
        // 自定义计算
        int CUSTOM = 3;
        // 高级统计，含方差、标准差等
        int STATS_EX = 4;
        // 分组
        int TERMS = 5;
        // 多重分组
        int RANGE = 6;
    }

    public interface ConditionValueRule {
        // 默认，直接取值
        int DEFAULT = 0;
        // 固定取值
        int REGULAR = 1;
        // 动态通过URL取值
        int JSON = 2;
    }

    public interface RegularValue {
        // 长效GnRhA小剂量临界值，即 amount <= min，则为小剂量
        // 分类：long-gnrha-dose 编码：min
        String LONG_GNRHA_DOSE_MIN = "0";
        // 长效GnRhA全量剂量临界值，即 amount >= max，则为全量 min < amount < max，这中间的部分，记为半量
        // 分类：long-gnrha-dose 编码：max
        String LONG_GNRHA_DOSE_MAX = "1";

        String AMH_KEY = "amh";

        String HCG_KEY = "hcg";
    }

    public interface Common {
        // 不启用
        int NO = 0;
        // 启用
        int YES = 1;
    }

    public interface CustomField {
        // 首次FET临床妊娠周期数
        int FIRST_FET_CLINICAL_PREGNANCY_COUNT = 318;
        // 首次FET活产周期数
        int FIRST_FET_LIVE_BIRTH_COUNT = 319;
        // 首次移植临床妊娠周期数
        int FIRST_ET_CLINICAL_PREGNANCY_COUNT = 320;
        // 首次移植活产周期数
        int FIRST_ET_LIVE_BIRTH_COUNT = 321;
        // 获得临床妊娠平均FET周期数
        int CLINICAL_PREGNANCY_AVG_FET_COUNT = 322;
        // 获得活产平均FET周期数
        int LIVE_BIRTH_AVG_FET_COUNT = 323;
        // 获得临床妊娠平均移植周期数
        int CLINICAL_PREGNANCY_AVG_ET_COUNT = 324;
        // 获得活产平均移植周期数
        int LIVE_BIRTH_AVG_ET_COUNT = 325;
        // 首次妊娠周期有剩余冷冻胚胎周期数
        int FIRST_CLINICAL_PREGNANCY_HAS_FROZEN_LEFT_COUNT = 326;
        // _平均剩余胚胎数
        int FIRST_CLINICAL_PREGNANCY_HAS_FROZEN_LEFT_AVG_NUM = 327;
        // 未妊娠周期有剩余冷冻胚胎周期数
        int NOT_CLINICAL_PREGNANCY_HAS_FROZEN_LEFT_COUNT = 328;
        // _平均剩余胚胎数
        int NOT_CLINICAL_PREGNANCY_HAS_FROZEN_LEFT_AVG_NUM = 329;
        // 未活产周期有剩余冷冻胚胎周期数
        int NO_LIVE_BIRTH_HAS_FROZEN_LEFT_COUNT = 330;
        // _平均剩余胚胎数
        int NO_LIVE_BIRTH_HAS_FROZEN_LEFT_AVG_NUM = 331;
        // 累积临床妊娠率（起始周期）
        int CUMULATIVE_CLINICAL_PREGNANCY_RATE = 332;
        // 累积临床妊娠率（起始周期不包括未妊娠有冷冻胚胎周期）
        int CUMULATIVE_CLINICAL_PREGNANCY_RATE_WITHOUT_NOT_PREGNANCY_FROZEN = 333;
        // 累积临床妊娠率（取卵周期）
        int CUMULATIVE_CLINICAL_PREGNANCY_RATE_OPU = 334;
        // 累积临床妊娠率（取卵周期不包括未妊娠有冷冻胚胎周期）
        int CUMULATIVE_CLINICAL_PREGNANCY_RATE_OPU_WITHOUT_NOT_PREGNANCY_FROZEN = 335;
        // 累积活产率（起始周期）
        int CUMULATIVE_LIVE_BIRTH_RATE = 336;
        // 累积活产率（起始周期：分母不包括未活产有冷冻胚胎周期）
        int CUMULATIVE_LIVE_BIRTH_RATE_WITHOUT_NO_LIVE_FROZEN = 337;
        // 累积活产率（取卵周期）
        int CUMULATIVE_LIVE_BIRTH_RATE_OPU = 338;
        // 累积活产率（取卵周期：分母不包括未活产有冷冻胚胎周期）
        int CUMULATIVE_LIVE_BIRTH_RATE_OPU_WITHOUT_NO_LIVE_FROZEN = 339;
    }

    public interface ComposeCondition {
        // 1：D2，2：D3，3：D4，4：D5，5：D6，6：D7
        String etDay = "etDay";
        // 0：卵裂胚，1：桑葚胚，2：囊胚
        String etTerm = "etTerm";
        // 0：单胚胎移植，1：双胚胎移植，2：大于双胚胎移植
        String etNum = "etNum";
        // 0：0PN，1：1PN，2：2PN，3：大于2PN
        String etPn = "etPn";
        // 0：非优质 1：优质（囊胚取优囊情况，卵裂胚、桑葚胚均取自优胚情况）
        String etHq = "etHq";
        // 1：D2，2：D3，3：D4，4：D5，5：D6，6：D7
        String thawDay = "thawDay";
        // 0：0PN，1：1PN，2：2PN，3：大于2PN
        String thawPn = "thawPn";
        // 0：卵裂胚，1：桑葚胚，2：囊胚
        String thawTerm = "thawTerm";
        // 0：非优质 1：优质（囊胚取优囊情况，卵裂胚、桑葚胚均取自优胚情况）
        String thawHq = "thawHq";

    }

    /**
     * 树形条件需特殊处理的字段
     * [
     * [1000, 1],
     * [2000, 2]
     * ]
     * 二维数组，遍历子数组，过去掉里面>=10000的数据
     */
    public static final Set<String> TREE_CONDITION_KEYS = CollUtil.newHashSet("cohPlanEx", "spermSource");

    /**
     * 辅助用药特定的key
     */
    public static final String ADJUVANT_KEY = "adjuvant";

    /**
     * 模板统计的父菜单ID
     */
    public static final long COMPOSE_PARENT_ID = 3000L;
}
