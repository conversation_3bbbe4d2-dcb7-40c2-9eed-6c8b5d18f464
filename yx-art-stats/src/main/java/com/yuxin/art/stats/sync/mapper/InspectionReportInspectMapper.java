/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-stats
 * 文件名称:InspectionReportInspectMapper.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.stats.sync.mapper;

import com.yuxin.art.domain.inspection.InspectionReportInspect;
import com.yuxin.framework.mvc.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2021-12-01
 */

@Mapper
public interface InspectionReportInspectMapper extends BaseMapper<InspectionReportInspect> {
}
