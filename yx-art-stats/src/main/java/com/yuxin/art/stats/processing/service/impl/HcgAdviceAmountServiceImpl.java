/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-stats
 * 文件名称:HcgAdviceAmountServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.stats.processing.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.yuxin.art.domain.doctororder.ArtHcgAdvice;
import com.yuxin.art.stats.common.utils.EsUtils;
import com.yuxin.art.stats.common.vo.QueryOrder;
import com.yuxin.art.stats.processing.constants.BaseQuery;
import com.yuxin.art.stats.processing.entity.ArtStats;
import com.yuxin.art.stats.processing.service.HcgAdviceAmountService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * hcg统计
 *
 * <AUTHOR>
 * @date 2021/1/6
 */
@Service
@Slf4j
public class HcgAdviceAmountServiceImpl implements HcgAdviceAmountService {
    @Override
    @SneakyThrows
    public void getHcgAmount(ArtStats artStats) {
        // 扳机次数
        int hcgTimes = 0;
        List<Date> hcgTime = new ArrayList<>();
        List<ArtHcgAdvice> list =
            EsUtils.getList(ArtHcgAdvice.class, BaseQuery.BY_CYCLE_ID, ListUtil.of(QueryOrder.asc("hcgTime")), ListUtil.of(artStats.getId()));
        if (CollectionUtil.isNotEmpty(list)) {
            // 扳机次数
            hcgTimes = list.size();
            hcgTime.addAll(list.stream().map(ArtHcgAdvice::getHcgTime).collect(Collectors.toList()));
            artStats.setHcgDoctorId(list.get(0).getRecorderId());
        }

        artStats.setHcgTimes(hcgTimes);
        artStats.setHcgTime(hcgTime);
    }
}
