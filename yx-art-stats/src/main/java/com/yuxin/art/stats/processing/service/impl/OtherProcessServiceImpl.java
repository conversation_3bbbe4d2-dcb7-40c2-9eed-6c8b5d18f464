/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-stats
 * 文件名称:OtherProcessServiceImpl.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.stats.processing.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yuxin.art.domain.art.ai.ArtArtificialInsemination;
import com.yuxin.art.domain.art.cycle.ArtCycle;
import com.yuxin.art.domain.art.embryo.ArtEmbryo;
import com.yuxin.art.domain.art.ivf.*;
import com.yuxin.art.domain.art.sperm.ArtSpermProcessingPostoperation;
import com.yuxin.art.domain.art.sperm.ArtSpermProcessingPreoperative;
import com.yuxin.art.domain.fz.*;
import com.yuxin.art.domain.inspection.InspectionReport;
import com.yuxin.art.domain.inspection.InspectionReportExamine;
import com.yuxin.art.domain.medication.ArtMedicationDetail;
import com.yuxin.art.domain.surgery.SurgeryFetalReduction;
import com.yuxin.art.domain.surgery.SurgeryFetalReductionDetail;
import com.yuxin.art.domain.surgery.SurgeryFetalReductionEmbryoDetail;
import com.yuxin.art.domain.surgery.SurgerySpecialOrder;
import com.yuxin.art.domain.ultrasound.*;
import com.yuxin.art.stats.common.utils.EsUtils;
import com.yuxin.art.stats.common.vo.QueryOrder;
import com.yuxin.art.stats.common.vo.QueryParam;
import com.yuxin.art.stats.display.constants.StatsConstants;
import com.yuxin.art.stats.processing.constants.BaseQuery;
import com.yuxin.art.stats.processing.constants.IvfQuery;
import com.yuxin.art.stats.processing.constants.OtherQuery;
import com.yuxin.art.stats.processing.entity.ArtStats;
import com.yuxin.art.stats.processing.service.DrugAmountService;
import com.yuxin.art.stats.processing.service.ProcessService;
import com.yuxin.art.stats.processing.utils.ProcessUtils;
import com.yuxin.constant.GlobalConstant;
import com.yuxin.entity.BaseEntity;
import com.yuxin.json.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 乱七八糟处理类
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Service
@Slf4j
public class OtherProcessServiceImpl implements ProcessService {

    @Autowired
    private DrugAmountService drugAmountService;
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public void process(ArtStats stats) throws Exception {
        // 促排相关信息
        processUltrasound(stats);
        /* ======================= 特殊手术统计 ======================= */
        Long cycleId = stats.getId();
        List<SurgerySpecialOrder> surgeryList = EsUtils.getList(SurgerySpecialOrder.class, OtherQuery.ALL_FINISH_SURGERY_KEY, ListUtil.of(cycleId));
        // 0-取精手术
        stats.setSurgerySpermCount(
            (int)surgeryList.stream().filter(item -> ObjectUtil.equal(item.getName(), 0)).map(SurgerySpecialOrder::getSurgeryDate).count());
        // 1-卵巢囊肿抽吸
        stats.setSurgeryOvarianCysCount(
            (int)surgeryList.stream().filter(item -> ObjectUtil.equal(item.getName(), 1)).map(SurgerySpecialOrder::getSurgeryDate).count());
        // 2-卵泡穿刺手术
        stats.setSurgeryFollicularPunctureCount(
            (int)surgeryList.stream().filter(item -> ObjectUtil.equal(item.getName(), 2)).map(SurgerySpecialOrder::getSurgeryDate).count());
        // 3-输卵管抽吸
        int surgeryTubalAspirationCount =
            (int)surgeryList.stream().filter(item -> ObjectUtil.equal(item.getName(), 3)).map(SurgerySpecialOrder::getSurgeryDate).count();
        stats.setSurgeryTubalAspirationCount(surgeryTubalAspirationCount);
        if (surgeryTubalAspirationCount > 0) {
            stats.setHydrosalpinxExtraction(1);
        }
        // 4-诊断性刮宫
        stats.setSurgeryDiagnosticCurettageCount(
            (int)surgeryList.stream().filter(item -> ObjectUtil.equal(item.getName(), 4)).map(SurgerySpecialOrder::getSurgeryDate).count());
        // 5-宫腔注射手术
        stats.setSurgeryIntrauterineInjectionCount(
            (int)surgeryList.stream().filter(item -> ObjectUtil.equal(item.getName(), 5)).map(SurgerySpecialOrder::getSurgeryDate).count());
        // 6-减胎手术
        List<SurgerySpecialOrder> reduceList = surgeryList.stream().filter(item -> ObjectUtil.equal(item.getName(), 6)).collect(Collectors.toList());
        stats.setReduceEmbryoTimes(reduceList.size());
        AtomicInteger reduceEmbryoNum = new AtomicInteger();
        reduceList.forEach(surgerySpecialOrder -> {
            SurgeryFetalReduction surgeryFetalReduction =
                EsUtils.getOne(SurgeryFetalReduction.class, BaseQuery.BY_KEY, ListUtil.of("surgerySpecialId", surgerySpecialOrder.getId()));
            List<SurgeryFetalReductionDetail> surgeryFetalReductionDetails = EsUtils.getList(SurgeryFetalReductionDetail.class, BaseQuery.BY_KEY,
                ListUtil.of("surgeryFetalReductionId", surgeryFetalReduction.getId()));
            surgeryFetalReductionDetails.forEach(surgeryFetalReductionDetail -> {
                List<SurgeryFetalReductionEmbryoDetail> surgeryFetalReductionEmbryoDetails =
                    EsUtils.getList(SurgeryFetalReductionEmbryoDetail.class, BaseQuery.BY_KEY,
                        ListUtil.of("fetalReductionDetaillId", surgeryFetalReductionDetail.getId()));
                surgeryFetalReductionEmbryoDetails.forEach(surgeryFetalReductionEmbryoDetail -> {
                    if (ObjectUtil.equal(surgeryFetalReductionEmbryoDetail.getTireReduction(), GlobalConstant.YES)) {
                        reduceEmbryoNum.addAndGet(1);
                    }
                });
            });
        });
        stats.setReduceEmbryoNum(reduceEmbryoNum.get());

        // 人授周期此处不需要处理
        if (!ProcessUtils.isAi(stats.getTreatmentPlan())) {
            // 卵子情况
            AtomicInteger frozenOvumNum = new AtomicInteger();
            AtomicInteger abandonOvumNum = new AtomicInteger();
            AtomicInteger inseminationOvumNum = new AtomicInteger();
            List<ArtOvum> artOvumList = EsUtils.getList(ArtOvum.class, BaseQuery.BY_CYCLE_ID, ListUtil.of(cycleId));
            artOvumList.forEach(artOvum -> {
                if (artOvum.getOutcome() == 3 && artOvum.getIsFrozen() == 1) {
                    frozenOvumNum.addAndGet(1);
                } else if (artOvum.getOutcome() == 4) {
                    abandonOvumNum.addAndGet(1);
                } else {
                    inseminationOvumNum.addAndGet(1);
                }
            });
            stats.setFrozenOvumNum(frozenOvumNum.get());
            stats.setAbandonOvumNum(abandonOvumNum.get());
            stats.setInseminationOvumNum(inseminationOvumNum.get());
        }

        // 精子洗涤
        List<Integer> washMethods = new ArrayList<>();
        List<ArtSpermProcessingPostoperation> spermAfter =
            EsUtils.getList(ArtSpermProcessingPostoperation.class, BaseQuery.BY_CYCLE_ID, ListUtil.of(QueryOrder.asc("operatorTime")),
                ListUtil.of(cycleId));
        spermAfter.forEach(after -> {
            washMethods.add(after.getWashMethod());
        });
        stats.setWashMethods(washMethods);

        if (CollUtil.isNotEmpty(spermAfter)) {
            stats.setWashDoctorId(spermAfter.get(0).getOperatorId());
        } else {
            List<ArtSpermProcessingPreoperative> spermBefore =
                EsUtils.getList(ArtSpermProcessingPreoperative.class, BaseQuery.BY_CYCLE_ID, ListUtil.of(QueryOrder.asc("operatorTime")),
                    ListUtil.of(cycleId));
            if (CollUtil.isNotEmpty(spermBefore)) {
                stats.setWashDoctorId(spermBefore.get(0).getOperatorId());
            }
        }

        // 人授
        if (ProcessUtils.isAi(stats.getTreatmentPlan())) {
            List<Date> aiTime = new ArrayList<>();
            List<ArtArtificialInsemination> aiList = EsUtils.getList(ArtArtificialInsemination.class, BaseQuery.BY_CYCLE_ID,
                ListUtil.of(QueryOrder.asc("surgeryDate"), QueryOrder.asc("beginTime")), ListUtil.of(cycleId));
            aiList.forEach(entity -> aiTime.add(entity.getSurgeryDate()));
            stats.setAiTime(aiTime);
            stats.setArtTime(aiTime);
            stats.setArtTreatmentTimes(aiList.size());
            stats.setAiTimes(aiList.size());
            if (CollUtil.isNotEmpty(aiList)) {
                stats.setAiDoctorId(aiList.get(0).getSurgeonId());
            }
            // AIH/AID的取消周期
            stats.setCycleCancel(CollUtil.isEmpty(aiList) ? 1 : 0);
        }

        // 人授周期此处不需要处理
        if (!ProcessUtils.isAi(stats.getTreatmentPlan())) {
            // 胚胎情况
            List<ArtEmbryo> embryos = EsUtils.getList(ArtEmbryo.class, BaseQuery.BY_CYCLE_ID, ListUtil.of(cycleId));
            Set<Date> inseminationTime = new HashSet<>();
            Set<Integer> inseminationMethod = new HashSet<>();
            embryos.forEach(artEmbryo -> {
                inseminationTime.add(artEmbryo.getInseminationTime());
                inseminationMethod.add(artEmbryo.getFertilizationMode());
            });
            stats.setInseminationTime(new ArrayList<>(inseminationTime));
            stats.setInseminationMethod(new ArrayList<>(inseminationMethod));
        }

        // 有效监测日
        this.processMonitorDays(stats);
        // AMH
        this.processAmh(stats);
        // 人授周期此处不需要处理
        if (!ProcessUtils.isAi(stats.getTreatmentPlan())) {
            // 授精医师
            this.processIvfDoctor(stats);
            // 冷解冻医生
            this.processFrozenThawDoctors(stats);
        }
    }

    private void processFrozenThawDoctors(ArtStats stats) {
        Long cycleId = stats.getId();
        List<Long> listIds = new ArrayList<>();
        // 胚胎冷冻 embryoFrozenDoctorId
        List<FtFrozenEmbryo> frozenEmbryoList = EsUtils.getList(FtFrozenEmbryo.class, BaseQuery.BY_CYCLE_ID, ListUtil.of(cycleId)).stream()
            .filter(i -> ObjectUtil.isNotNull(i.getActualFrozenDate())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(frozenEmbryoList)) {
            listIds.addAll(frozenEmbryoList.stream().map(FtFrozenEmbryo::getId).collect(Collectors.toList()));
            List<FtDoStrawEmbryo> details = EsUtils.getList(FtDoStrawEmbryo.class, BaseQuery.IN_KEY, ListUtil.of(QueryOrder.asc("frozenerTime")),
                    ListUtil.of("frozenEmbryoId", listIds.toString())).stream().filter(t -> ObjectUtil.isNotNull(t.getFrozenerId()))
                .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(details)) {
                stats.setEmbryoFrozenDoctorId(details.get(0).getFrozenerId());
            }
        }

        // 卵子冷冻 ovumFrozenDoctorId
        List<FtFrozenOvum> frozenOvumList = EsUtils.getList(FtFrozenOvum.class, BaseQuery.BY_CYCLE_ID, ListUtil.of(cycleId)).stream()
            .filter(i -> ObjectUtil.isNotNull(i.getActualFrozenDate())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(frozenOvumList)) {
            listIds.clear();
            listIds.addAll(frozenOvumList.stream().map(FtFrozenOvum::getId).collect(Collectors.toList()));
            List<FtDoStrawOvum> details = EsUtils.getList(FtDoStrawOvum.class, BaseQuery.IN_KEY, ListUtil.of(QueryOrder.asc("frozenerTime")),
                    ListUtil.of("frozenOvumId", listIds.toString())).stream().filter(t -> ObjectUtil.isNotNull(t.getFrozenerId()))
                .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(details)) {
                stats.setOvumFrozenDoctorId(details.get(0).getFrozenerId());
            }
        }

        // 卵子解冻 ovumThawDoctorId
        List<FtThawOvum> thawOvumList = EsUtils.getList(FtThawOvum.class, BaseQuery.BY_CYCLE_ID, ListUtil.of(cycleId)).stream()
            .filter(i -> ObjectUtil.isNotNull(i.getActualThawDate())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(thawOvumList)) {
            listIds.clear();
            ;
            listIds.addAll(thawOvumList.stream().map(FtThawOvum::getId).collect(Collectors.toList()));
            List<FtThawStrawOvum> details = EsUtils.getList(FtThawStrawOvum.class, BaseQuery.IN_KEY, ListUtil.of(QueryOrder.asc("thawTime")),
                ListUtil.of("thawOvumId", listIds.toString())).stream().filter(t -> ObjectUtil.isNotNull(t.getThawId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(details)) {
                stats.setOvumThawDoctorId(details.get(0).getThawId());
            }
        }
    }

    private void processIvfDoctor(ArtStats stats) {
        List<ArtIcsi> icsiList =
            EsUtils.getList(ArtIcsi.class, BaseQuery.BY_CYCLE_ID, ListUtil.of(QueryOrder.asc("operatorTime")), ListUtil.of(stats.getId()));
        if (CollUtil.isNotEmpty(icsiList)) {
            stats.setIcsiDoctorId(icsiList.get(0).getOperatorId());

            // 同时记录ICSI的卵子授精数、损伤数情况。outcome=2为损伤
            List<ArtIcsiDetail> details = EsUtils.getList(ArtIcsiDetail.class, BaseQuery.IN_KEY,
                ListUtil.of("artIcsiId", icsiList.stream().map(ArtIcsi::getId).collect(Collectors.toList())));
            stats.setIcsiNum(details.size());
            stats.setIcsiDamageNum((int)details.stream().filter(t -> ObjectUtil.equal(t.getOutcome(), 2)).count());
        }

        // IVF和早授精这两个，可能顺序有问题，这里就不管了，覆盖旧覆盖
        List<ArtShortFertilizationObserve> shortList =
            EsUtils.getList(ArtShortFertilizationObserve.class, BaseQuery.BY_CYCLE_ID, ListUtil.of(QueryOrder.asc("operatorTime")),
                ListUtil.of(stats.getId()));
        if (CollUtil.isNotEmpty(shortList)) {
            stats.setIvfDoctorId(shortList.get(0).getOperatorId());

            // 计算SIVF完全不受精
            List<Long> idList = shortList.stream().map(BaseEntity::getId).collect(Collectors.toList());
            int count = EsUtils.count(ArtShortFertilizationObserveDetail.class, IvfQuery.SIVF_PB2, ListUtil.of(idList.toString()));
            stats.setSivfFail(count > 0 ? 0 : 1);
        } else {
            List<ArtIvf> ivfList =
                EsUtils.getList(ArtIvf.class, BaseQuery.BY_CYCLE_ID, ListUtil.of(QueryOrder.asc("operatorTime")), ListUtil.of(stats.getId()));
            if (CollUtil.isNotEmpty(ivfList)) {
                stats.setIvfDoctorId(ivfList.get(0).getOperatorId());
            }
        }
    }

    private void processAmh(ArtStats stats) {
        // 查询这个用户，在周期时间内（开始+结束，如果有结束的话），有没有对应的手术信息
        String amhIds = redisTemplate.opsForValue().get(StatsConstants.RegularValue.AMH_KEY);
        List<Long> amhList = JsonUtil.toList(amhIds, Long.class);
        if (CollUtil.isEmpty(amhList)) {
            return;
        }

        // 1.通过周期找人（此处不需要判断周期情况了，必定会有，前面已经校验了）
        ArtCycle cycle = EsUtils.getOne(ArtCycle.class, BaseQuery.BY_ID, ListUtil.of(stats.getId()));
        Long femaleId = cycle.getPatientFemaleId();

        // 2.通过病人找对应amh报告
        String queryStr = "{\"bool\":{\"filter\":[{\"terms\":{\"itemInternalTypeCode\":{}}},{\"term\":{\"patientId\":{}}}]}}";
        QueryParam param = QueryParam.build(queryStr, ListUtil.of(amhList.toString(), femaleId), ListUtil.of(QueryOrder.desc("reportDate")));
        InspectionReport report = EsUtils.getOne(InspectionReport.class, param);
        if (report != null) {
            // 3.通过报告找详情
            List<InspectionReportExamine> list =
                EsUtils.getList(InspectionReportExamine.class, BaseQuery.BY_KEY, ListUtil.of("reportId", report.getId()));
            for (InspectionReportExamine examine : list) {
                if (NumberUtil.isNumber(examine.getResult())) {
                    // TODO 有Bug，如果AMH项目有多个指标，无法识别。所以最佳还是需要ART处理好，加载AMH保存到字段中
                    stats.setAmh(NumberUtil.toBigDecimal(examine.getResult()));
                    break;
                }
            }
        }
    }

    // treatmentPlan implantedNum

    /**
     * 监测处理类
     *
     * @param stats
     */
    private void processUltrasound(ArtStats stats) throws Exception {
        List<ArtMedicationDetail> artMedicationDetails = drugAmountService.getMedicationList(stats.getId(), 4, false);
        if (CollectionUtil.isNotEmpty(artMedicationDetails)) {
            Date ultrasoundTime = artMedicationDetails.get(0).getUseDrugTime();
            QueryParam param = new QueryParam();
            param.setQueryString(OtherQuery.UL_TIME_KEY);
            param.setParams(ListUtil.of(stats.getId(), DateUtil.formatDateTime(DateUtil.beginOfDay(ultrasoundTime))).toArray());
            param.setOrders(ListUtil.of(QueryOrder.asc("monitorTime")));
            List<UltrasoundMonitor> ultrasoundMonitors = EsUtils.getList(UltrasoundMonitor.class, param);
            BigDecimal fetIntimaThickness = new BigDecimal("0");
            Integer fetIntimaType = null;
            Integer fetIntimaEcho = null;

            int hcgGt20 = 0, hcgGt18 = 0, hcgGt16 = 0;
            for (UltrasoundMonitor ultrasoundMonitor : ultrasoundMonitors) {
                UltrasoundInspect ultrasoundInspect =
                    EsUtils.getOne(UltrasoundInspect.class, BaseQuery.BY_KEY, ListUtil.of("ultrasoundId", ultrasoundMonitor.getId()));
                if (null != ultrasoundInspect) {
                    if (null != ultrasoundInspect.getLeftIntimaThickness()) {
                        fetIntimaThickness = ultrasoundInspect.getLeftIntimaThickness();
                    } else if (null != ultrasoundInspect.getRightIntimaThickness()) {
                        fetIntimaThickness = ultrasoundInspect.getRightIntimaThickness();
                    }
                    if (null != ultrasoundInspect.getLeftIntimaType()) {
                        fetIntimaType = ultrasoundInspect.getLeftIntimaType();
                    } else if (null != ultrasoundInspect.getRightIntimaType()) {
                        fetIntimaType = ultrasoundInspect.getRightIntimaType();
                    }
                    if (null != ultrasoundInspect.getLeftIntimaEcho()) {
                        fetIntimaEcho = ultrasoundInspect.getLeftIntimaEcho();
                    } else if (null != ultrasoundInspect.getRightIntimaEcho()) {
                        fetIntimaEcho = ultrasoundInspect.getRightIntimaEcho();
                    }

                    // 获取卵泡详情
                    List<UltrasoundInspectDetail> details =
                        EsUtils.getList(UltrasoundInspectDetail.class, BaseQuery.BY_KEY, ListUtil.of("inspectId", ultrasoundInspect.getId()));
                    for (UltrasoundInspectDetail detail : details) {
                        // 排掉奇葩数据，没长或者没宽，整了个平均值出来的
                        if (detail.getLength() != null && detail.getWidth() != null) {
                            BigDecimal average = detail.getAverage();
                            if (average.compareTo(new BigDecimal(20)) > 0) {
                                hcgGt20++;
                            } else if (average.compareTo(new BigDecimal(18)) > 0) {
                                hcgGt18++;
                            } else if (average.compareTo(new BigDecimal(16)) > 0) {
                                hcgGt16++;
                            }
                        }
                    }
                    break;
                }
            }
            stats.setFetIntimaThickness(fetIntimaThickness);
            stats.setFetIntimaType(fetIntimaType);
            stats.setFetIntimaEcho(fetIntimaEcho);
            stats.setHcgFollicleGt20(hcgGt20);
            stats.setHcgFollicleGt18(hcgGt18);
            stats.setHcgFollicleGt16(hcgGt16);
        }
        List<ArtMedicationDetail> hcgArtMedicationDetails = drugAmountService.getMedicationList(stats.getId(), 3, true);
        if (CollectionUtil.isNotEmpty(hcgArtMedicationDetails)) {
            Date ultrasoundTime = hcgArtMedicationDetails.get(0).getUseDrugTime();
            QueryParam param = new QueryParam();
            param.setQueryString(OtherQuery.UL_TIME_KEY);
            param.setParams(ListUtil.of(stats.getId(), DateUtil.formatDateTime(DateUtil.beginOfDay(ultrasoundTime))).toArray());
            param.setOrders(ListUtil.of(QueryOrder.asc("monitorTime")));
            List<UltrasoundMonitor> ultrasoundMonitors = EsUtils.getList(UltrasoundMonitor.class, param);
            BigDecimal hcgIntimaThickness = new BigDecimal("0").setScale(4, BigDecimal.ROUND_HALF_UP);
            Integer hgcIntimaType = null;
            Integer hcgIntimaEcho = null;
            BigDecimal hcgPhosphorus = new BigDecimal("0").setScale(4, BigDecimal.ROUND_HALF_UP);
            BigDecimal hcgLh = new BigDecimal("0").setScale(4, BigDecimal.ROUND_HALF_UP);
            BigDecimal hcgFsh = new BigDecimal("0").setScale(4, BigDecimal.ROUND_HALF_UP);
            BigDecimal hcgE2 = new BigDecimal("0").setScale(4, BigDecimal.ROUND_HALF_UP);
            for (UltrasoundMonitor ultrasoundMonitor : ultrasoundMonitors) {
                UltrasoundInspect ultrasoundInspect =
                    EsUtils.getOne(UltrasoundInspect.class, BaseQuery.BY_KEY, ListUtil.of("ultrasoundId", ultrasoundMonitor.getId()));
                if (null != ultrasoundInspect) {
                    if (null != ultrasoundInspect.getLeftIntimaThickness()) {
                        hcgIntimaThickness = ultrasoundInspect.getLeftIntimaThickness();
                    } else if (null != ultrasoundInspect.getRightIntimaThickness()) {
                        hcgIntimaThickness = ultrasoundInspect.getRightIntimaThickness();
                    }
                    if (null != ultrasoundInspect.getLeftIntimaType()) {
                        hgcIntimaType = ultrasoundInspect.getLeftIntimaType();
                    } else if (null != ultrasoundInspect.getRightIntimaType()) {
                        hgcIntimaType = ultrasoundInspect.getRightIntimaType();
                    }
                    if (null != ultrasoundInspect.getLeftIntimaEcho()) {
                        hcgIntimaEcho = ultrasoundInspect.getLeftIntimaEcho();
                    } else if (null != ultrasoundInspect.getRightIntimaEcho()) {
                        hcgIntimaEcho = ultrasoundInspect.getRightIntimaEcho();
                    }
                    break;
                }
            }
            for (UltrasoundMonitor ultrasoundMonitor : ultrasoundMonitors) {
                UltrasoundEndocrine endocrine =
                    EsUtils.getOne(UltrasoundEndocrine.class, BaseQuery.BY_KEY, ListUtil.of("ultrasoundId", ultrasoundMonitor.getId()));
                if (null != endocrine) {
                    if (NumberUtil.isNumber(endocrine.getP())) {
                        hcgPhosphorus = new BigDecimal(endocrine.getP());
                    }
                    if (NumberUtil.isNumber(endocrine.getE2())) {
                        hcgE2 = new BigDecimal(endocrine.getE2());
                    }
                    if (NumberUtil.isNumber(endocrine.getFsh())) {
                        hcgFsh = new BigDecimal(endocrine.getFsh());
                    }
                    if (NumberUtil.isNumber(endocrine.getLh())) {
                        hcgLh = new BigDecimal(endocrine.getLh());
                    }
                    break;
                }
            }
            stats.setHcgIntimaThickness(hcgIntimaThickness);
            stats.setHcgIntimaType(hgcIntimaType);
            stats.setHcgIntimaEcho(hcgIntimaEcho);
            stats.setHcgPhosphorus(hcgPhosphorus);
            stats.setHcgE2(hcgE2);
            stats.setHcgFsh(hcgFsh);
            stats.setHcgLh(hcgLh);
        }

        // 是否有发生积水
        AtomicInteger hydrosalpinx = new AtomicInteger(0);
        // 计算刺激天数
        AtomicInteger stimulateDay = new AtomicInteger(0);

        // 周期基础激素（含hcg日）
        List<BigDecimal> cyclePhosphorus = new ArrayList<>();
        List<BigDecimal> cycleLh = new ArrayList<>();
        List<BigDecimal> cycleFsh = new ArrayList<>();
        List<BigDecimal> cycleE2 = new ArrayList<>();

        List<UltrasoundMonitor> ultrasoundMonitors =
            EsUtils.getList(UltrasoundMonitor.class, BaseQuery.BY_CYCLE_ID, ListUtil.of(QueryOrder.asc("monitorTime")), ListUtil.of(stats.getId()));
        ultrasoundMonitors.forEach(ultrasoundMonitor -> {
            List<UltrasoundInspectAbnormal> abnormalList =
                EsUtils.getList(UltrasoundInspectAbnormal.class, BaseQuery.BY_KEY, ListUtil.of("ultrasoundId", ultrasoundMonitor.getId()));
            abnormalList.forEach(abnormal -> {
                if (StrUtil.isNotEmpty(abnormal.getAbnormalOption())) {
                    // 只要出现过积水，就算是有积水
                    if ("hydrosalpinx".equals(abnormal.getAbnormalOption())) {
                        hydrosalpinx.set(1);
                    }

                    // afc取最后的一次
                    if ("sinusFolliclesNum".equalsIgnoreCase(abnormal.getAbnormalOption()) && NumberUtil.isNumber(abnormal.getContent())) {
                        stats.setAfc(NumberUtil.toBigDecimal(abnormal.getContent()));
                    }
                }
            });

            if (null != ultrasoundMonitor.getStimulusDay()) {
                stimulateDay.incrementAndGet();
            }

            // 获取这一天的激素数据
            UltrasoundEndocrine endocrine =
                EsUtils.getOne(UltrasoundEndocrine.class, BaseQuery.BY_KEY, ListUtil.of("ultrasoundId", ultrasoundMonitor.getId()));
            if (endocrine != null) {
                if (NumberUtil.isNumber(endocrine.getFsh())) {
                    cycleFsh.add(new BigDecimal(endocrine.getFsh()));
                }
                if (NumberUtil.isNumber(endocrine.getP())) {
                    cyclePhosphorus.add(new BigDecimal(endocrine.getP()));
                }
                if (NumberUtil.isNumber(endocrine.getLh())) {
                    cycleLh.add(new BigDecimal(endocrine.getLh()));
                }
                if (NumberUtil.isNumber(endocrine.getE2())) {
                    cycleE2.add(new BigDecimal(endocrine.getE2()));
                }
            }

        });
        stats.setHydrosalpinx(hydrosalpinx.get());
        stats.setStimulateDay(stimulateDay.get());
        stats.setCycleFsh(cycleFsh);
        stats.setCycleLh(cycleLh);
        stats.setCyclePhosphorus(cyclePhosphorus);
        stats.setCycleE2(cycleE2);
    }

    private void processMonitorDays(ArtStats stats) {
        Long id = stats.getId();
        List<UltrasoundInspect> monitors = EsUtils.getList(UltrasoundInspect.class, BaseQuery.BY_CYCLE_ID, ListUtil.of(id));
        // 需要计算有效的监控天数
        int monitorDays = 0;
        for (UltrasoundInspect entity : monitors) {
            if (this.isValidMonitor(entity)) {
                monitorDays++;
            }
        }
        stats.setMonitorDays(monitorDays);
    }

    /**
     * 是否有效的B超监控
     * 必须有卵巢数据，或者内膜数据，才能认定是有效的
     *
     * @param entity B超监控
     * @return
     */
    private boolean isValidMonitor(UltrasoundInspect entity) {
        if (entity == null) {
            return false;
        }

        // 左侧是否都没填信息（true: 都没填写，false: 部分填写了）
        boolean empty = ObjectUtil.isAllEmpty(entity.getLeftAverageLessFiveNumber(), entity.getLeftIntimaEcho(), entity.getLeftIntimaThickness(),
            entity.getLeftIntimaType(), entity.getLeftOvaryLong(), entity.getLeftOvaryWidth(), entity.getRightAverageLessFiveNumber(),
            entity.getRightIntimaEcho(), entity.getRightIntimaThickness(), entity.getRightIntimaType(), entity.getRightOvaryLong(),
            entity.getRightOvaryWidth());

        if (empty) {
            // 都没有，就需要看详情
            int count = EsUtils.count(UltrasoundInspectDetail.class, BaseQuery.BY_KEY, ListUtil.of("inspectId", entity.getId()));
            return count > 0;
        } else {
            return true;
        }
    }
}
