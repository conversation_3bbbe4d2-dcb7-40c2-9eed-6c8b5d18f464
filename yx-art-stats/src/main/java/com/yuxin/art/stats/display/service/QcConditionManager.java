/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-stats
 * 文件名称:QcConditionManager.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.art.stats.display.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.yuxin.art.stats.display.constants.StatsConstants;
import com.yuxin.art.stats.display.vo.StatsConditionVO;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 质控组合条件
 *
 * <AUTHOR>
 * @date 2022-02-15
 */

@Service
@Slf4j
public class QcConditionManager {

    private static final List<String> PN = ListUtil.of("Pn0", "Pn1", "Pn2"/*, "GtPn2"*/);
    private static final List<String> HQ = ListUtil.of("NotHq", "Hq");
    private static final List<String> TERM = ListUtil.of("Cleavage", "Morula", "Bla");
    private static final String ET = "et";
    private static final String THAW = "thaw";
    private static final String NIL = "";
    /**
     * 动态组合字段
     * 1. ET|THAW
     * 2. PN
     * 3. HQ
     * 4. TERM
     */
    private static final String PATTERN = "{}{}{}{}Num";

    public List<QueryBuilder> mergeCustomCondition(List<StatsConditionVO> conditions) {
        List<QueryBuilder> queryBuilders = new ArrayList<>();
        Map<String, List<StatsConditionVO>> map = conditions.stream().collect(Collectors.groupingBy(StatsConditionVO::getField));
        // 普通：etDay、etNum、thawDay
        if (map.containsKey(StatsConstants.ComposeCondition.etDay)) {
            queryBuilders.add(this.mergeEtDay(castType(map.get(StatsConstants.ComposeCondition.etDay).get(0).getVal())));
        }
        if (map.containsKey(StatsConstants.ComposeCondition.etNum)) {
            queryBuilders.add(this.mergeEtNum(castType(map.get(StatsConstants.ComposeCondition.etNum).get(0).getVal())));
        }
        if (map.containsKey(StatsConstants.ComposeCondition.thawDay)) {
            queryBuilders.add(this.mergeThawDay(castType(map.get(StatsConstants.ComposeCondition.thawDay).get(0).getVal())));
        }

        // 移植组合
        if (map.containsKey("etTerm") || map.containsKey("etPn") || map.containsKey("etHq")) {
            StatsConditionVO term = null;
            if (map.containsKey("etTerm")) {
                term = map.get("etTerm").get(0);
            }
            StatsConditionVO pn = null;
            if (map.containsKey("etPn")) {
                pn = map.get("etPn").get(0);
            }
            StatsConditionVO hq = null;
            if (map.containsKey("etHq")) {
                hq = map.get("etHq").get(0);
            }
            queryBuilders.add(mergeCompose(ET, term, pn, hq));
        }

        // 解冻组合
        if (map.containsKey("thawTerm") || map.containsKey("thawPn") || map.containsKey("thawHq")) {
            StatsConditionVO term = null;
            if (map.containsKey("thawTerm")) {
                term = map.get("thawTerm").get(0);
            }
            StatsConditionVO pn = null;
            if (map.containsKey("thawPn")) {
                pn = map.get("thawPn").get(0);
            }
            StatsConditionVO hq = null;
            if (map.containsKey("thawHq")) {
                hq = map.get("thawHq").get(0);
            }
            queryBuilders.add(mergeCompose(THAW, term, pn, hq));
        }

        return queryBuilders;
    }

    private QueryBuilder mergeEtDay(Set<Integer> val) {
        return QueryBuilders.termsQuery("etDay", val);
    }

    private QueryBuilder mergeEtNum(Set<Integer> val) {
        // 取交集，便于校验
        Set<Integer> tempSet = CollUtil.intersectionDistinct(val, CollUtil.newHashSet(0, 1, 2));
        int size = tempSet.size();
        // 0：单胚胎移植，1：双胚胎移植，2：大于双胚胎移植
        if (size == 3) {
            // 三三组合
            return QueryBuilders.rangeQuery("etNum").gt(0);
        } else if (size == 2) {
            // 两两组合，最大值只有两种情况，要么是2，要么是1
            int min = CollUtil.min(tempSet);
            int max = CollUtil.max(tempSet);
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery().minimumShouldMatch(1);
            if (max == 2) {
                boolQuery.should(QueryBuilders.rangeQuery("etNum").gt(2));
            } else {
                boolQuery.should(QueryBuilders.termQuery("etNum", max + 1));
            }
            boolQuery.should(QueryBuilders.termQuery("etNum", min + 1));
            return boolQuery;
        } else if (size == 1) {
            // 单个情况
            int value = CollUtil.getFirst(val);
            if (value == 0 || value == 1) {
                return QueryBuilders.termQuery("etNum", (value + 1));
            } else if (value == 2) {
                return QueryBuilders.rangeQuery("etNum").gt(2);
            }
        }

        return null;
    }

    private QueryBuilder mergeThawDay(Set<Integer> val) {
        return QueryBuilders.termsQuery("thawDay", val);
    }

    private QueryBuilder mergeCompose(String prefix, StatsConditionVO term, StatsConditionVO pn, StatsConditionVO hq) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery().minimumShouldMatch(1);
        if (term != null && pn != null && hq != null) {
            // 都不为空空，三三组合
            Set<Integer> pnVal = castType(pn.getVal());
            Set<Integer> hqVal = castType(hq.getVal());
            Set<Integer> termVal = castType(term.getVal());
            pnVal.forEach(p -> hqVal.forEach(h -> termVal.forEach(t -> boolQuery.should(QueryBuilders.rangeQuery(field(prefix, p, h, t)).gt(0)))));
        } else if (term != null && pn != null) {
            Set<Integer> pnVal = castType(pn.getVal());
            Set<Integer> termVal = castType(term.getVal());
            pnVal.forEach(p -> termVal.forEach(t -> boolQuery.should(QueryBuilders.rangeQuery(field(prefix, p, -1, t)).gt(0))));
        } else if (term != null && hq != null) {
            Set<Integer> hqVal = castType(hq.getVal());
            Set<Integer> termVal = castType(term.getVal());
            hqVal.forEach(h -> termVal.forEach(t -> boolQuery.should(QueryBuilders.rangeQuery(field(prefix, -1, h, t)).gt(0))));
        } else if (pn != null && hq != null) {
            Set<Integer> pnVal = castType(pn.getVal());
            Set<Integer> hqVal = castType(hq.getVal());
            pnVal.forEach(p -> hqVal.forEach(h -> boolQuery.should(QueryBuilders.rangeQuery(field(prefix, p, h, -1)).gt(0))));
        } else if (term != null) {
            Set<Integer> values = castType(term.getVal());
            values.forEach(v -> boolQuery.should(QueryBuilders.rangeQuery(field(prefix, -1, -1, v)).gt(0)));
        } else if (pn != null) {
            Set<Integer> values = castType(pn.getVal());
            values.forEach(v -> boolQuery.should(QueryBuilders.rangeQuery(field(prefix, v, -1, -1)).gt(0)));
        } else if (hq != null) {
            Set<Integer> values = castType(hq.getVal());
            values.forEach(v -> boolQuery.should(QueryBuilders.rangeQuery(field(prefix, -1, v, -1)).gt(0)));
        }
        return boolQuery;
    }

    private Set<Integer> castType(Set<Object> set) {
        return set.stream().map(t -> Integer.valueOf(t.toString())).collect(Collectors.toSet());
    }

    private String field(String prefix, int pnIndex, int hqIndex, int termIndex) {
        return StrUtil.format(
            // 格式
            PATTERN,
            // et 或者 thaw
            prefix,
            // Pn*
            pnIndex >= 0 ? PN.get(pnIndex) : NIL,
            // NotHq|Hq
            hqIndex >= 0 ? HQ.get(hqIndex) : NIL,
            // 胚胎期
            termIndex >= 0 ? TERM.get(termIndex) : NIL);
    }
}
