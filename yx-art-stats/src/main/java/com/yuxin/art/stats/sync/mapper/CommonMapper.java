/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-stats
 * 文件名称:CommonMapper.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */

package com.yuxin.art.stats.sync.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * @ClassName CommonMapper
 * @Description TODO
 * <AUTHOR>
 * @Date 2020-12-31 10:06
 * @Version 1.0
 */
@Mapper
public interface CommonMapper {
    /**
     * 动态查询所有表数据
     *
     * @param tableName 表名
     * @param offset    偏移
     * @param pageSize  每页大小
     * @return
     */
    @Select(
        "select t.* from ${tableName} t where t.id in (select a.id from (select id from ${tableName} order by id asc limit ${offset}, ${pageSize}) a)")
    // @Select("select * from ${tableName} order by id asc limit ${offset}, ${pageSize}")
    List<Map<String, Object>> findAll(@Param("tableName") String tableName, @Param("offset") Integer offset, @Param("pageSize") Integer pageSize);

    /**
     * 每次通过传入上一次分页返回的最大ID，从而直接避开加载所有的分页数据进来的情况
     *
     * @param tableName
     * @param maxId
     * @param pageSize
     * @return
     */
    @Select("select * from ${tableName} where id > ${maxId} order by id asc limit ${pageSize}")
    List<Map<String, Object>> findPage(@Param("tableName") String tableName, @Param("maxId") Long maxId, @Param("pageSize") Integer pageSize);

    @Select("select count(1) from ${tableName}")
    int count(@Param("tableName") String tableName);
}
