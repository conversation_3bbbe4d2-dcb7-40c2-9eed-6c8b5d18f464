/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-stats
 * 文件名称:EmbryoAfter.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-05-25
 */

@Data
public class EmbryoAfter {
    @ExcelProperty("case_no")
    private String cycleId;
    @ExcelProperty("female_id")
    private String femaleId;
    @ExcelProperty("male_id")
    private String maleId;
    @ExcelProperty("tank_no")
    private Integer pos1;
    @ExcelProperty("pail_no")
    private Integer pos2;
    @ExcelProperty("stent_no")
    private Integer pos3;
    @ExcelProperty("pipe_no")
    private Integer pos4;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("frozen_time")
    private Date frozenDate;
    @ExcelProperty("frozen_day")
    private Integer frozenDay;
    @ExcelProperty("plate_number")
    private Integer group;
    @ExcelProperty("sort")
    private Integer no;

    private Integer count;
}
