/*
 * 郑重警告:本内容仅限于广州誉欣数字化科技有限公司内部传阅，禁止外泄以及用于其他的商业目的。
 * 项目名称:yx-art-project
 * 模块名称:yx-art-stats
 * 文件名称:DifferTests.java
 * 版权信息:Copyright© 2024 YuXin.Co.Ltd. All rights reserved
 */
package com.yuxin.tests;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.yuxin.art.stats.display.vo.QcGroupVO;
import com.yuxin.art.stats.sync.SyncProperties;
import com.yuxin.art.stats.templates.service.DifferService;
import com.yuxin.art.stats.templates.vo.*;
import com.yuxin.json.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022-02-24
 */

@SpringBootTest
@Slf4j
@EnableConfigurationProperties({SyncProperties.class})
@ActiveProfiles("statistics")
public class DifferTests {
    @Autowired
    private DifferService differService;

    @Test
    public void testCondition() {
        DifferConditionQueryVO queryVo = new DifferConditionQueryVO();
        // queryVo.setTemplateId(1498956082582786048L);
        // queryVo.setTemplateId(null);
        // queryVo.setConditions(
        // "{\"femaleFactor\":[\"7\",\"1\",\"0\",\"3\"],\"maleFactor\":[\"7\"],\"maleIndication\":[\"0\",\"1\"],\"femaleIndication\":[\"0\"],\"bothFactor\":[\"0\"],\"unknownFactor\":[],\"cohPlanEx\":[],\"hcgDrugType\":[],\"intimaPlanType\":[],\"intimaEcho\":[],\"oviduct\":[],\"etTools\":[],\"clSupport\":[],\"ivfMethod\":[],\"spermSource\":[],\"ovumSource\":[],\"ivfResult\":[],\"frozenCycle\":[],\"etCycle\":[],\"followOutcome\":[],\"imsiIcsi\":[],\"patientFemaleAge$1\":\"1\",\"patientFemaleAge$2\":\"4\",\"patientMaleAge$1\":\"6\",\"patientMaleAge$2\":\"8\",\"infertilityYears$1\":\"4\",\"infertilityYears$2\":\"88\",\"amh$1\":\"3\",\"amh$2\":\"3\",\"patientFemaleAge\":\"1,4\",\"patientMaleAge\":\"6,\",\"infertilityYears\":\"25,\",\"amh\":\"3,4\"}");
        // queryVo.setConditions(
        // "{\"femaleFactor\":[\"0\",\"6\",\"1\"],\"maleFactor\":[\"12\",\"10\",\"9\"],\"maleIndication\":[\"0\",\"1\"],\"femaleIndication\":[\"0\"],\"bothFactor\":[],\"unknownFactor\":[],\"cohPlanEx\":[],\"hcgDrugType\":[],\"intimaPlanType\":[],\"intimaEcho\":[],\"oviduct\":[],\"etTools\":[],\"clSupport\":[],\"ivfMethod\":[],\"spermSource\":[],\"ovumSource\":[],\"ivfResult\":[],\"frozenCycle\":[],\"etCycle\":[],\"followOutcome\":[],\"imsiIcsi\":[],\"patientFemaleAge$1\":\"3\",\"patientFemaleAge$2\":\"4\",\"patientMaleAge$1\":\"6\",\"patientMaleAge$2\":\"7\",\"infertilityYears$1\":\"8\",\"patientFemaleAge\":\"3,4\",\"patientMaleAge\":\"6,7\",\"infertilityYears\":\"8,\"}");
        queryVo.setConditions(
            "{\"femaleFactor\":[\"8\",\"2\"],\"maleFactor\":[],\"maleIndication\":[],\"femaleIndication\":[],\"bothFactor\":[],\"unknownFactor\":[],\"cohPlanEx\":[],\"hcgDrugType\":[],\"intimaPlanType\":[],\"intimaEcho\":[],\"oviduct\":[],\"etTools\":[],\"clSupport\":[],\"ivfMethod\":[],\"spermSource\":[],\"ovumSource\":[],\"ivfResult\":[],\"frozenCycle\":[],\"etCycle\":[],\"followOutcome\":[],\"imsiIcsi\":[],\"artDoctorId\":[],\"hcgDoctorId\":[],\"opuDoctorId\":[],\"etDoctorId\":[],\"aiDoctorId\":[],\"pickDoctorId\":[],\"washDoctorId\":[],\"ivfDoctorId\":[],\"icsiDoctorId\":[],\"embryoFrozenDoctorId\":[],\"embryoThawDoctorId\":[],\"ovumFrozenDoctorId\":[],\"ovumThawDoctorId\":[],\"qcCrowd\":\"0\",\"typeTime\":\"1\",\"etTime\":[],\"cycleBeginTime\":[],\"opuTime\":[\"2022-03-01\",\"2022-04-06\"]}");
        DifferConditionVO result = differService.differCondition(queryVo);
        log.info(JsonUtil.toJson(result));
    }

    @Test
    public void testIndex() {
        DifferIndexQueryVO queryVo = new DifferIndexQueryVO();
        queryVo.setTemplateId(1L);
        queryVo.setIndices(null);
        DifferIndexVO result = differService.differIndex(queryVo);
        log.info(JSON.toJSONString(result, true));
    }

    @Test
    public void testGroup() {
        DifferGroupQueryVO queryVo = new DifferGroupQueryVO();
        queryVo.setTemplateId(1499671865902370816L);
        String str = "[{\"id\":\"21\",\"name\":\"Gn剂量:(3-4)\",\"value\":\"3,4\"},{\"id\":\"21\",\"name\":\"Gn剂量:(5-6)\",\"value\":\"5,6\"}]";
        queryVo.setGroups(JsonUtil.toList(str, QcGroupVO.class));
        DifferGroupVO result = differService.differGroup(queryVo);
        log.info(JsonUtil.toJson(result));
    }

    public static void main(String[] args) {
        Set<String> set1 = new HashSet<>();
        set1.add("a");
        set1.add("b");
        set1.add("c");

        Set<String> set2 = new HashSet<>();
        set2.add("d");
        set2.add("b");
        set2.add("c");

        Sets.SetView<String> difference1 = Sets.difference(set1, set2);
        log.info(difference1.toString());
        Sets.SetView<String> difference2 = Sets.difference(set2, set1);
        log.info(difference2.toString());
    }
}
