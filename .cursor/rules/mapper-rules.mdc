---
description: "Mapper 层开发规范：XML 禁写 SQL，接口 default 方法，命名、Wrapper 和分页批量处理规则"
globs: ["**/mapper/**/*.java", "**/mapper/**/*.xml"]
alwaysApply: true
---
# 数据访问层(Mapper)编码规范

## 1. Mapper XML文件规范
- XML文件中只保留`<resultMap>`和`<sql>`标签定义
- 禁止在XML文件中编写SQL查询语句

## 2. Mapper接口基础规范
- Mapper接口必须继承`BaseMapper`
- 接口必须命名为`XxxMapper`
- 必须采用接口`default`方法完成数据库操作
- 对于简单查询（三个以内相同操作符的必定有值的参数），必须使用`BaseMapper`提供的便捷方法：
  - 精确匹配：`selectOne(Entity::getField, value)`
  - 列表查询：`selectList(Entity::getField, value)`
  - 统计：`selectCount(Entity::getField, value)`
  - 删除：`delete(Entity::getField, value)`
- 对于复杂查询（多条件、不同操作符、连表等），必须使用`MPJLambdaWrapper`：
  - 必须在wrapper构造后第一行调用`selectAll(实体类.class)`
  - 严禁使用链式调用，每个查询条件必须单独成行
  - 必定有值(不可能为空)的参数使用`eq`、`like`等方法
  - 可以为空的参数使用`MPJLambdaWrapper`类的`xxxIfExists`方法判空
- 如果要构建数据库操作,必须使用`MPJLambdaWrapper`,禁止使用其他的Wrapper,无论是否需要连表查询
- 使用`selectAs`方法指定字段别名映射，禁止使用字符串硬编码
- 对于多表关联查询，创建独立的`buildQueryJoinFieldWrapper`方法
- 如果需要实现分页查询，则需要使用`com.yuxin.framework.mvc.vo.PageParams`和`com.yuxin.framework.mvc.vo.PageResult<T>`
- 对于批量操作，使用`BaseMapper`提供的批量API
- 对于大批量操作(>1000条)，必须分批处理

## 3. Mapper方法命名规范
- `selectById`：根据ID查询单个对象
- `selectByXxx`：根据非ID的条件查询单个对象
- `selectList`：无参数查询全部列表
- `selectListByXxx`：根据条件查询多个对象
- `selectCountByXxx`：查统计值
- `selectPage`：分页查询
- `deleteByXxx`：删除
- `updateByXxx`：更新
- 禁止使用select+形容词+名词格式（如selectAvailableList）
- 对于可用状态等条件，使用selectListByStatus(1)
- 对于需要连接查询的情况，使用selectListWithXxx
- 多条件查询将每个条件作为方法参数并在方法名体现
- 方法名称必须明确表达功能，如selectListByOcrAndStatus而非selectAvailableOcrList

## 4. 重构注意事项
- 如原有代码在XML中实现了SQL查询，应将其转换为接口`default`方法实现
- 如原有XML中的SQL查询包含复杂的动态SQL，确保转换为Java代码后逻辑完全一致
- 检查参数来源，确定哪些参数必定有值，哪些参数可能为空
- 严格“功能范围最小化”，只实现明确并必要的功能,未明确要求的功能禁止提供
- 确保查询条件的处理逻辑与原有实现一致，特别是对空值的处理
- 重构时应保持与原有功能完全一致，不得扩展或减少功能范围